<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方法详情 - 康达价格管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 使用与分析方法页面相同的样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
            padding: 12px 8px;
        }

        .table td {
            text-align: center;
            vertical-align: middle;
            padding: 10px 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .method-name {
            text-align: left !important;
            max-width: 300px;
            word-wrap: break-word;
        }

        .project-name {
            text-align: left !important;
            max-width: 200px;
            word-wrap: break-word;
        }

        .badge-qualification {
            font-size: 0.75rem;
            margin: 1px;
        }

        .price-cell {
            font-weight: 500;
        }

        .status-badge {
            font-size: 0.8rem;
        }

        .filter-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
        }

        .loading-spinner {
            text-align: center;
            padding: 50px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2>
                    <i class="fas fa-list-alt me-2"></i>
                    <span id="page-title">方法详情</span>
                </h2>
            </div>
        </div>

        <!-- 筛选信息提示 -->
        <div id="filter-info" class="filter-info" style="display: none;">
            <div class="d-flex align-items-center">
                <i class="fas fa-filter me-2"></i>
                <span id="filter-description"></span>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-bar me-2"></i>统计信息
                        </h6>
                        <p class="card-text mb-0">
                            共找到 <span id="total-count" class="fw-bold text-primary">0</span> 个方法
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-clock me-2"></i>时间信息
                        </h6>
                        <p class="card-text mb-0">
                            变动日期: <span id="change-date" class="fw-bold text-success">-</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方法列表表格 -->
        <div class="row">
            <div class="col-12">
                <div class="table-container">
                    <div id="methods-loading" class="loading-spinner">
                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                        <p>正在加载方法详情...</p>
                    </div>

                    <div id="methods-table-container" style="display: none;">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 60px;">项目ID</th>
                                    <th style="width: 80px;">方法ID</th>
                                    <th style="width: 120px;">分类</th>
                                    <th style="width: 200px;">项目名称</th>
                                    <th style="width: 300px;">方法名称</th>
                                    <th style="width: 100px;">分析价格</th>
                                    <th style="width: 100px;">最低价格</th>
                                    <th style="width: 120px;">采样价格</th>
                                    <th style="width: 80px;">资质</th>
                                    <th style="width: 100px;">状态</th>
                                </tr>
                            </thead>
                            <tbody id="methods-table-body">
                            </tbody>
                        </table>
                    </div>

                    <div id="no-data-message" style="display: none;" class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">没有找到相关方法</h5>
                        <p class="text-muted">请检查筛选条件或稍后重试</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="row mt-3">
            <div class="col-12">
                <nav id="pagination-container" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pagination-info">
                            <span class="text-muted">
                                显示第 <span id="start-index">1</span> - <span id="end-index">20</span> 条，
                                共 <span id="total-methods">0</span> 条记录
                            </span>
                        </div>
                        <ul class="pagination mb-0" id="pagination">
                        </ul>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let totalMethods = 0;
        const methodsPerPage = 20;
        let methodIds = [];
        let changeType = '';
        let changeDate = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            changeType = urlParams.get('change_type') || '';
            changeDate = urlParams.get('change_date') || '';
            const methodIdsParam = urlParams.get('method_ids') || '';
            
            if (methodIdsParam) {
                methodIds = methodIdsParam.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
            }

            // 设置页面标题和筛选信息
            setupPageInfo();
            
            // 加载方法详情
            if (methodIds.length > 0) {
                loadMethodDetails();
            } else {
                showNoDataMessage();
            }
        });

        function setupPageInfo() {
            let title = '方法详情';
            let description = '';
            
            switch(changeType) {
                case 'new_methods':
                    title = '新增方法详情';
                    description = `显示在 ${changeDate} 检测到的新增方法`;
                    break;
                case 'qualification_changes':
                    title = '资质变更方法详情';
                    description = `显示在 ${changeDate} 检测到的资质变更方法`;
                    break;
                case 'price_changes':
                    title = '价格变更方法详情';
                    description = `显示在 ${changeDate} 检测到的价格变更方法`;
                    break;
                default:
                    description = `显示指定的方法列表`;
            }
            
            document.getElementById('page-title').textContent = title;
            document.getElementById('change-date').textContent = changeDate || '-';
            
            if (description) {
                document.getElementById('filter-description').textContent = description;
                document.getElementById('filter-info').style.display = 'block';
            }
        }

        function loadMethodDetails() {
            const startIndex = (currentPage - 1) * methodsPerPage;
            const endIndex = startIndex + methodsPerPage;
            const currentMethodIds = methodIds.slice(startIndex, endIndex);
            
            if (currentMethodIds.length === 0) {
                showNoDataMessage();
                return;
            }

            // 更新统计信息
            document.getElementById('total-count').textContent = methodIds.length;
            
            // 构建API请求
            const params = new URLSearchParams();
            params.append('method_ids', currentMethodIds.join(','));
            
            fetch(`/api/methods_by_ids?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMethods(data.methods);
                        setupPagination();
                    } else {
                        showErrorMessage(data.message || '加载方法详情失败');
                    }
                })
                .catch(error => {
                    showErrorMessage('网络错误: ' + error.message);
                });
        }

        function displayMethods(methods) {
            const loadingDiv = document.getElementById('methods-loading');
            const tableContainer = document.getElementById('methods-table-container');
            const noDataDiv = document.getElementById('no-data-message');
            const tableBody = document.getElementById('methods-table-body');

            // 隐藏加载提示
            loadingDiv.style.display = 'none';

            if (methods.length === 0) {
                tableContainer.style.display = 'none';
                noDataDiv.style.display = 'block';
                return;
            }

            // 显示表格并填充数据
            tableContainer.style.display = 'block';
            noDataDiv.style.display = 'none';

            let html = '';
            methods.forEach(method => {
                html += createMethodRow(method);
            });

            tableBody.innerHTML = html;
        }

        function createMethodRow(method) {
            // 项目ID（显示后2位）
            const projectId = method.service_item_id ?
                String(method.service_item_id).slice(-2).padStart(2, '0') : '--';

            // 方法ID（显示后2位）
            const methodId = method.id ?
                String(method.id).slice(-2).padStart(2, '0') : '--';

            // 分类
            const category = method.category || '-';

            // 项目名称
            const projectName = method.service_item_name || '未知项目';

            // 方法名称
            const methodName = method.method_name || '未命名方法';

            // 分析价格
            const analysisPrice = method.price ? `${method.price}元` : '-';

            // 最低价格
            const lowestPrice = method.lowest_bid ? `${method.lowest_bid}元` : '-';

            // 采样价格（暂时显示为'-'，后续可以扩展）
            const samplePrice = '-';

            // 资质标识
            let qualifications = '';
            if (method.cma) qualifications += '<span class="badge bg-info badge-qualification me-1">CMA</span>';
            if (method.cnas) qualifications += '<span class="badge bg-primary badge-qualification me-1">CNAS</span>';
            if (method.gov_agree) qualifications += '<span class="badge bg-warning badge-qualification me-1">NHC</span>';
            if (!qualifications) qualifications = '-';

            // 状态
            const projectStatus = method.project_status === 0 ? '解锁' : '锁定';
            const methodStatus = method.status === 0 ? '解锁' : '锁定';
            const availability = (method.project_status === 0 && method.status === 0) ? '可用' : '不可用';

            const statusColor = (method.project_status === 0 && method.status === 0) ? 'success' : 'warning';
            const statusIcon = (method.project_status === 0 && method.status === 0) ? 'unlock' : 'lock';

            return `
                <tr>
                    <td>${projectId}</td>
                    <td>${methodId}</td>
                    <td>${category}</td>
                    <td class="project-name">${projectName}</td>
                    <td class="method-name">${methodName}</td>
                    <td class="price-cell">${analysisPrice}</td>
                    <td class="price-cell">${lowestPrice}</td>
                    <td class="price-cell">${samplePrice}</td>
                    <td>${qualifications}</td>
                    <td>
                        <span class="badge bg-${statusColor} status-badge">
                            <i class="fas fa-${statusIcon} me-1"></i>${availability}
                        </span>
                        <br>
                        <small class="text-muted">${projectStatus}/${methodStatus}</small>
                    </td>
                </tr>
            `;
        }

        function setupPagination() {
            const totalPages = Math.ceil(methodIds.length / methodsPerPage);

            // 更新分页信息
            const startIndex = (currentPage - 1) * methodsPerPage + 1;
            const endIndex = Math.min(currentPage * methodsPerPage, methodIds.length);

            document.getElementById('start-index').textContent = startIndex;
            document.getElementById('end-index').textContent = endIndex;
            document.getElementById('total-methods').textContent = methodIds.length;

            if (totalPages <= 1) {
                document.getElementById('pagination-container').style.display = 'none';
                return;
            }

            document.getElementById('pagination-container').style.display = 'block';

            let paginationHtml = '';

            // 上一页
            if (currentPage > 1) {
                paginationHtml += `<li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>`;
            } else {
                paginationHtml += `<li class="page-item disabled">
                    <span class="page-link">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </span>
                </li>`;
            }

            // 页码（显示当前页前后各2页）
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
                }
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页
            if (currentPage < totalPages) {
                paginationHtml += `<li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>`;
            } else {
                paginationHtml += `<li class="page-item disabled">
                    <span class="page-link">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </span>
                </li>`;
            }

            document.getElementById('pagination').innerHTML = paginationHtml;
        }

        function changePage(page) {
            currentPage = page;
            loadMethodDetails();
            window.scrollTo(0, 0);
        }

        function showNoDataMessage() {
            const loadingDiv = document.getElementById('methods-loading');
            const tableContainer = document.getElementById('methods-table-container');
            const noDataDiv = document.getElementById('no-data-message');

            loadingDiv.style.display = 'none';
            tableContainer.style.display = 'none';
            noDataDiv.style.display = 'block';

            document.getElementById('total-count').textContent = '0';
            document.getElementById('pagination-container').style.display = 'none';
        }

        function showErrorMessage(message) {
            const loadingDiv = document.getElementById('methods-loading');
            const tableContainer = document.getElementById('methods-table-container');
            const noDataDiv = document.getElementById('no-data-message');

            loadingDiv.style.display = 'none';
            tableContainer.style.display = 'none';
            noDataDiv.style.display = 'block';

            noDataDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5 class="text-warning">加载失败</h5>
                <p class="text-muted">${message}</p>
                <button class="btn btn-primary" onclick="loadMethodDetails()">
                    <i class="fas fa-redo me-1"></i>重试
                </button>
            `;

            document.getElementById('pagination-container').style.display = 'none';
        }
    </script>
</body>
</html>
