#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量更新管理器
基于method_change_history表实现统计数据的增量更新
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Set, Any


class IncrementalUpdateManager:
    """增量更新管理器"""
    
    def __init__(self, db_path: str = 'kangda_prices.db'):
        self.db_path = db_path
        
    def update_statistics_incrementally(self, since_datetime: datetime = None) -> bool:
        """增量更新统计数据
        
        Args:
            since_datetime: 从什么时间开始更新，如果为None则使用最后更新时间
            
        Returns:
            bool: 更新是否成功
        """
        try:
            print("开始增量更新统计数据...")
            
            # 确定更新起始时间
            if since_datetime is None:
                since_datetime = self._get_last_update_time()
            
            print(f"增量更新起始时间: {since_datetime}")
            
            # 获取变动记录
            changes = self._get_changes_since(since_datetime)
            if not changes:
                print("没有发现新的变动记录")
                return True
            
            print(f"发现 {len(changes)} 条变动记录")
            
            # 按变动类型分组处理
            change_groups = self._group_changes_by_type(changes)
            
            # 更新各类统计
            success_count = 0
            total_count = 0
            
            for change_type, change_list in change_groups.items():
                total_count += 1
                if self._update_statistics_for_change_type(change_type, change_list):
                    success_count += 1
                    print(f"✅ {change_type} 统计更新成功")
                else:
                    print(f"❌ {change_type} 统计更新失败")
            
            # 更新最后更新时间
            self._update_last_update_time()
            
            print(f"增量更新完成: {success_count}/{total_count} 个类型更新成功")
            return success_count == total_count
            
        except Exception as e:
            print(f"增量更新统计数据失败: {e}")
            return False
    
    def _get_last_update_time(self) -> datetime:
        """获取最后更新时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否有统计汇总表
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='statistics_summary'
                """)
                
                if not cursor.fetchone():
                    # 如果没有统计汇总表，返回一周前
                    return datetime.now() - timedelta(days=7)
                
                # 获取最后更新时间
                cursor.execute("""
                    SELECT MAX(last_updated) as last_update
                    FROM statistics_summary
                """)
                
                result = cursor.fetchone()
                if result and result[0]:
                    return datetime.fromisoformat(result[0])
                else:
                    # 如果没有记录，返回一周前
                    return datetime.now() - timedelta(days=7)
                    
        except Exception as e:
            print(f"获取最后更新时间失败: {e}")
            return datetime.now() - timedelta(days=7)
    
    def _get_changes_since(self, since_datetime: datetime) -> List[Dict]:
        """获取指定时间以来的变动记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 检查method_change_history表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='method_change_history'
                """)
                
                if not cursor.fetchone():
                    print("method_change_history表不存在")
                    return []
                
                # 获取变动记录
                query = """
                    SELECT method_id, change_date, change_type, field_name, 
                           old_value, new_value, detected_time
                    FROM method_change_history
                    WHERE detected_time > ?
                      AND change_type != 'historical_migration'
                    ORDER BY detected_time
                """
                
                cursor.execute(query, (since_datetime,))
                results = cursor.fetchall()
                
                return [dict(row) for row in results]
                
        except Exception as e:
            print(f"获取变动记录失败: {e}")
            return []
    
    def _group_changes_by_type(self, changes: List[Dict]) -> Dict[str, List[Dict]]:
        """按变动类型分组变动记录"""
        groups = {
            'new_methods': [],
            'qualification_changes': [],
            'price_changes': []
        }
        
        for change in changes:
            change_type = change.get('change_type', '')
            field_name = change.get('field_name', '')
            
            if change_type == 'new_method':
                groups['new_methods'].append(change)
            elif field_name in ['cma', 'cnas', 'gov_agree']:
                groups['qualification_changes'].append(change)
            elif field_name in ['price', 'lowest_bid', 'analysis_cost']:
                groups['price_changes'].append(change)
        
        return groups
    
    def _update_statistics_for_change_type(self, change_type: str, changes: List[Dict]) -> bool:
        """为指定变动类型更新统计数据"""
        try:
            if not changes:
                return True
            
            # 按日期分组变动
            date_groups = {}
            for change in changes:
                change_date = change['change_date']
                if isinstance(change_date, str):
                    change_date = datetime.fromisoformat(change_date).date()
                
                date_key = change_date.strftime('%Y-%m-%d')
                if date_key not in date_groups:
                    date_groups[date_key] = set()
                
                date_groups[date_key].add(change['method_id'])
            
            # 更新统计汇总表
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for date_key, method_ids in date_groups.items():
                    # 为不同的时间粒度更新统计
                    self._update_period_statistics(cursor, change_type, date_key, method_ids, 'month')
                    self._update_period_statistics(cursor, change_type, date_key, method_ids, 'quarter')
                    self._update_period_statistics(cursor, change_type, date_key, method_ids, 'year')
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"更新 {change_type} 统计失败: {e}")
            return False
    
    def _update_period_statistics(self, cursor, change_type: str, date_key: str, 
                                method_ids: Set[int], period_type: str):
        """更新指定时间粒度的统计数据"""
        try:
            # 转换日期格式
            date_obj = datetime.strptime(date_key, '%Y-%m-%d')
            
            if period_type == 'month':
                period_value = date_obj.strftime('%Y-%m')
            elif period_type == 'quarter':
                quarter = (date_obj.month - 1) // 3 + 1
                period_value = f"{date_obj.year}-Q{quarter}"
            else:  # year
                period_value = date_obj.strftime('%Y')
            
            # 构建统计类型名称
            if change_type == 'qualification_changes':
                # 资质变更需要细分
                stat_types = ['qualification_changes_cma', 'qualification_changes_cnas', 'qualification_changes_nhc']
            else:
                stat_types = [change_type]
            
            for stat_type in stat_types:
                # 获取现有统计数据
                cursor.execute("""
                    SELECT count_value, method_ids
                    FROM statistics_summary
                    WHERE stat_type = ? AND period_type = ? AND period_value = ?
                """, (stat_type, period_type, period_value))
                
                result = cursor.fetchone()
                
                if result:
                    # 更新现有记录
                    current_count = result[0]
                    current_method_ids = json.loads(result[1]) if result[1] else []
                    
                    # 合并方法ID列表
                    updated_method_ids = list(set(current_method_ids + list(method_ids)))
                    updated_count = len(updated_method_ids)
                    
                    cursor.execute("""
                        UPDATE statistics_summary
                        SET count_value = ?, method_ids = ?, last_updated = ?
                        WHERE stat_type = ? AND period_type = ? AND period_value = ?
                    """, (
                        updated_count, json.dumps(updated_method_ids), datetime.now(),
                        stat_type, period_type, period_value
                    ))
                else:
                    # 插入新记录
                    method_ids_list = list(method_ids)
                    cursor.execute("""
                        INSERT INTO statistics_summary
                        (stat_type, period_type, period_value, count_value, method_ids, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        stat_type, period_type, period_value, len(method_ids_list),
                        json.dumps(method_ids_list), datetime.now()
                    ))
                    
        except Exception as e:
            print(f"更新时间粒度统计失败 [{period_type}]: {e}")
    
    def _update_last_update_time(self):
        """更新最后更新时间标记"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 更新所有记录的last_updated时间
                cursor.execute("""
                    UPDATE statistics_summary
                    SET last_updated = ?
                    WHERE last_updated < ?
                """, (datetime.now(), datetime.now() - timedelta(minutes=1)))
                
                conn.commit()
                
        except Exception as e:
            print(f"更新最后更新时间失败: {e}")
    
    def check_data_consistency(self) -> Dict[str, Any]:
        """检查统计数据一致性"""
        try:
            print("开始检查统计数据一致性...")
            
            # 导入价格管理器进行对比
            from price_manager import PriceDataManager
            price_manager = PriceDataManager(self.db_path)
            
            consistency_report = {
                'consistent': True,
                'issues': [],
                'summary_count': 0,
                'realtime_count': 0,
                'checked_at': datetime.now()
            }
            
            # 检查几个关键的统计数据
            test_configs = [
                ('month', 6),
                ('quarter', 12)
            ]
            
            for time_range, months in test_configs:
                try:
                    # 从汇总表获取数据
                    summary_data = price_manager._get_statistics_from_summary_table(time_range, months)
                    
                    # 从实时计算获取数据
                    realtime_data = price_manager._get_detailed_change_statistics_realtime(time_range, months)
                    
                    # 比较数据
                    if summary_data and realtime_data:
                        summary_new_methods = len(summary_data.get('new_methods', []))
                        realtime_new_methods = len(realtime_data.get('new_methods', []))
                        
                        consistency_report['summary_count'] += summary_new_methods
                        consistency_report['realtime_count'] += realtime_new_methods
                        
                        if abs(summary_new_methods - realtime_new_methods) > 5:  # 允许5个的差异
                            consistency_report['consistent'] = False
                            consistency_report['issues'].append(
                                f"{time_range}_{months}: 汇总表{summary_new_methods}个新增方法 vs 实时计算{realtime_new_methods}个"
                            )
                    
                except Exception as e:
                    consistency_report['issues'].append(f"检查 {time_range}_{months} 时出错: {e}")
            
            if consistency_report['consistent']:
                print("✅ 统计数据一致性检查通过")
            else:
                print("❌ 发现统计数据不一致问题:")
                for issue in consistency_report['issues']:
                    print(f"  - {issue}")
            
            return consistency_report
            
        except Exception as e:
            print(f"统计数据一致性检查失败: {e}")
            return {
                'consistent': False,
                'issues': [f"检查过程出错: {e}"],
                'checked_at': datetime.now()
            }


def main():
    """主函数"""
    print("增量更新管理器测试...")
    
    manager = IncrementalUpdateManager()
    
    # 执行增量更新
    success = manager.update_statistics_incrementally()
    if success:
        print("✅ 增量更新成功")
    else:
        print("❌ 增量更新失败")
    
    # 检查数据一致性
    consistency = manager.check_data_consistency()
    print(f"一致性检查结果: {'通过' if consistency['consistent'] else '失败'}")


if __name__ == "__main__":
    main()
