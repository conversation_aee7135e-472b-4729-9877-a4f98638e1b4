-- 标准方法管理模块数据库结构设计
-- 新增表结构，用于支持标准方法管理功能

-- 标准方法表
CREATE TABLE standard_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    standard_number TEXT NOT NULL,                    -- 标准号
    standard_year TEXT NOT NULL,                      -- 年份
    full_standard_number TEXT NOT NULL,               -- 完整标准号（标准号-年份）
    is_appendix_method BOOLEAN DEFAULT FALSE,         -- 是否为附录方法
    appendix_number TEXT,                             -- 附录号（如"附录A"）
    is_informative_appendix BOOLEAN DEFAULT FALSE,    -- 是否属于资料性附录
    standard_name TEXT NOT NULL,                      -- 标准名称
    status INTEGER DEFAULT 0,                         -- 状态：0-有效，1-废止
    remark TEXT,                                      -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 适用范围表
CREATE TABLE applicable_scopes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,                        -- 适用范围名称
    description TEXT,                                 -- 描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 检测项目表
CREATE TABLE detection_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                               -- 检测项目名称
    display_name TEXT NOT NULL,                       -- 显示名称（支持上下标）
    chemical_formula TEXT,                            -- 化学式
    cas_number TEXT,                                  -- CAS号
    category TEXT,                                    -- 分类
    aliases TEXT,                                     -- 别名（JSON格式存储）
    show_aliases_in_list BOOLEAN DEFAULT FALSE,       -- 是否在列表中显示别名
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 标准方法与适用范围关联表
CREATE TABLE standard_method_scopes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    standard_method_id INTEGER NOT NULL,
    applicable_scope_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (standard_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (applicable_scope_id) REFERENCES applicable_scopes(id) ON DELETE CASCADE,
    UNIQUE(standard_method_id, applicable_scope_id)
);

-- 标准方法与检测项目关联表
CREATE TABLE standard_method_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    standard_method_id INTEGER NOT NULL,
    detection_item_id INTEGER NOT NULL,
    is_recommended BOOLEAN DEFAULT TRUE,              -- 是否推荐
    priority INTEGER DEFAULT 1,                      -- 优先级
    remark TEXT,                                      -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (standard_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (detection_item_id) REFERENCES detection_items(id) ON DELETE CASCADE,
    UNIQUE(standard_method_id, detection_item_id)
);

-- 评价方法表
CREATE TABLE evaluation_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                               -- 评价方法名称
    description TEXT,                                 -- 描述
    applicable_scope_id INTEGER,                     -- 适用范围
    is_active BOOLEAN DEFAULT TRUE,                  -- 是否启用
    created_by TEXT,                                 -- 创建者
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (applicable_scope_id) REFERENCES applicable_scopes(id)
);

-- 评价方法推荐配置表
CREATE TABLE evaluation_recommendations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    evaluation_method_id INTEGER NOT NULL,
    detection_item_id INTEGER NOT NULL,
    recommended_standard_method_id INTEGER NOT NULL,
    priority INTEGER DEFAULT 1,                      -- 推荐优先级
    reason TEXT,                                      -- 推荐理由
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (evaluation_method_id) REFERENCES evaluation_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (detection_item_id) REFERENCES detection_items(id) ON DELETE CASCADE,
    FOREIGN KEY (recommended_standard_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    UNIQUE(evaluation_method_id, detection_item_id, recommended_standard_method_id)
);

-- 方法替代关系表（用于智能提示）
CREATE TABLE method_alternatives (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    old_method_id INTEGER NOT NULL,                  -- 旧方法ID
    new_method_id INTEGER NOT NULL,                  -- 新方法ID
    applicable_scope_id INTEGER NOT NULL,           -- 适用范围
    detection_item_id INTEGER NOT NULL,             -- 检测项目
    replacement_type TEXT DEFAULT 'upgrade',         -- 替代类型：upgrade-升级，alternative-替代
    effective_date DATE,                             -- 生效日期
    remark TEXT,                                     -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (old_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (new_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (applicable_scope_id) REFERENCES applicable_scopes(id) ON DELETE CASCADE,
    FOREIGN KEY (detection_item_id) REFERENCES detection_items(id) ON DELETE CASCADE
);

-- 创建索引优化查询性能
CREATE INDEX idx_standard_methods_full_number ON standard_methods(full_standard_number);
CREATE INDEX idx_standard_methods_status ON standard_methods(status);
CREATE INDEX idx_standard_methods_is_appendix ON standard_methods(is_appendix_method);
CREATE INDEX idx_standard_methods_is_informative ON standard_methods(is_informative_appendix);

CREATE INDEX idx_detection_items_name ON detection_items(name);
CREATE INDEX idx_detection_items_category ON detection_items(category);

CREATE INDEX idx_applicable_scopes_name ON applicable_scopes(name);

CREATE INDEX idx_standard_method_scopes_method ON standard_method_scopes(standard_method_id);
CREATE INDEX idx_standard_method_scopes_scope ON standard_method_scopes(applicable_scope_id);

CREATE INDEX idx_standard_method_items_method ON standard_method_items(standard_method_id);
CREATE INDEX idx_standard_method_items_item ON standard_method_items(detection_item_id);
CREATE INDEX idx_standard_method_items_recommended ON standard_method_items(is_recommended);

CREATE INDEX idx_evaluation_methods_active ON evaluation_methods(is_active);
CREATE INDEX idx_evaluation_methods_scope ON evaluation_methods(applicable_scope_id);

CREATE INDEX idx_evaluation_recommendations_method ON evaluation_recommendations(evaluation_method_id);
CREATE INDEX idx_evaluation_recommendations_item ON evaluation_recommendations(detection_item_id);
CREATE INDEX idx_evaluation_recommendations_standard ON evaluation_recommendations(recommended_standard_method_id);

CREATE INDEX idx_method_alternatives_old ON method_alternatives(old_method_id);
CREATE INDEX idx_method_alternatives_new ON method_alternatives(new_method_id);
CREATE INDEX idx_method_alternatives_scope ON method_alternatives(applicable_scope_id);
CREATE INDEX idx_method_alternatives_item ON method_alternatives(detection_item_id);

-- 创建视图方便查询
CREATE VIEW IF NOT EXISTS standard_methods_view AS
SELECT
    sm.id,
    sm.standard_number,
    sm.standard_year,
    sm.full_standard_number,
    sm.is_appendix_method,
    sm.appendix_number,
    sm.is_informative_appendix,
    sm.standard_name,
    sm.status,
    sm.remark,
    sm.created_at,
    sm.updated_at,
    GROUP_CONCAT(aps.name, '、') as applicable_scopes,
    GROUP_CONCAT(di.display_name, '、') as detection_items,
    COUNT(sms.applicable_scope_id) as scope_count,
    COUNT(smi.detection_item_id) as item_count
FROM standard_methods sm
LEFT JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
LEFT JOIN applicable_scopes aps ON sms.applicable_scope_id = aps.id
LEFT JOIN standard_method_items smi ON sm.id = smi.standard_method_id
LEFT JOIN detection_items di ON smi.detection_item_id = di.id
GROUP BY sm.id;

-- 插入一些初始数据
INSERT INTO applicable_scopes (name, description) VALUES 
('固体废物含量', '固体废物中各种成分的含量测定'),
('固体废物浸出', '固体废物浸出毒性测定'),
('水质', '各类水体的质量检测'),
('土壤', '土壤环境质量检测'),
('大气', '大气环境质量检测'),
('噪声', '环境噪声检测');

-- 插入一些常见检测项目
INSERT INTO detection_items (name, display_name, chemical_formula) VALUES 
('铅', 'Pb', 'Pb'),
('镉', 'Cd', 'Cd'),
('汞', 'Hg', 'Hg'),
('砷', 'As', 'As'),
('铬', 'Cr', 'Cr'),
('六价铬', 'Cr⁶⁺', 'Cr(VI)'),
('铜', 'Cu', 'Cu'),
('锌', 'Zn', 'Zn'),
('镍', 'Ni', 'Ni'),
('总磷', 'TP', 'P'),
('总氮', 'TN', 'N'),
('化学需氧量', 'COD', ''),
('生化需氧量', 'BOD₅', ''),
('悬浮物', 'SS', ''),
('pH值', 'pH', ''),
('溶解氧', 'DO', 'O₂');
