"""
康达价格管理系统 - 数据存储和管理模块
提供数据库操作、数据导入导出、数据分析等功能
"""

import sqlite3
import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import os


class PriceDataManager:
    """价格数据管理器"""
    
    def __init__(self, db_path='kangda_prices.db'):
        self.db_path = db_path
        self.ensure_database_exists()
        self._ensure_performance_indexes()
    
    def ensure_database_exists(self):
        """确保数据库存在并已初始化"""
        if not os.path.exists(self.db_path):
            print("数据库不存在，正在初始化...")
            self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查schema文件是否存在
                schema_file = 'database_schema.sql'
                if os.path.exists(schema_file):
                    with open(schema_file, 'r', encoding='utf-8') as f:
                        schema = f.read()
                    conn.executescript(schema)
                    print("数据库初始化完成")
                else:
                    print("警告: 未找到database_schema.sql文件")
        except Exception as e:
            print(f"初始化数据库时出错: {e}")
    
    def _ensure_performance_indexes(self):
        """确保性能相关的索引存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 确保手动基准价格表存在
                self._ensure_manual_baseline_table(cursor)

                # 添加关键索引以提升查询性能
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_check_methods_service_item_id ON check_methods(service_item_id)",
                    "CREATE INDEX IF NOT EXISTS idx_sample_types_check_method_id ON sample_types(check_method_id)",
                    "CREATE INDEX IF NOT EXISTS idx_price_history_check_method_id ON price_history(check_method_id)",
                    "CREATE INDEX IF NOT EXISTS idx_price_history_fetch_time ON price_history(fetch_time)",
                    "CREATE INDEX IF NOT EXISTS idx_price_history_service_item_id ON price_history(service_item_id)",
                    "CREATE INDEX IF NOT EXISTS idx_service_items_name ON service_items(name)",
                    "CREATE INDEX IF NOT EXISTS idx_service_items_number ON service_items(number)",
                    "CREATE INDEX IF NOT EXISTS idx_check_methods_name ON check_methods(name)",
                    "CREATE INDEX IF NOT EXISTS idx_check_methods_status ON check_methods(status)",
                    "CREATE INDEX IF NOT EXISTS idx_manual_baseline_check_method_id ON manual_baseline_prices(check_method_id)"
                ]

                for index_sql in indexes:
                    try:
                        cursor.execute(index_sql)
                    except sqlite3.OperationalError as e:
                        # 如果索引已存在，忽略错误
                        if "already exists" not in str(e):
                            print(f"创建索引时出错: {e}")

                conn.commit()
                print("性能索引检查完成")
        except Exception as e:
            print(f"确保性能索引时出错: {e}")

    def _ensure_manual_baseline_table(self, cursor):
        """确保手动基准价格表存在"""
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS manual_baseline_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_method_id INTEGER NOT NULL,
                    baseline_analysis_price REAL,
                    baseline_lowest_price REAL,
                    baseline_sample_prices TEXT,
                    set_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    set_by_user TEXT,
                    FOREIGN KEY (check_method_id) REFERENCES check_methods(id),
                    UNIQUE(check_method_id)
                )
            """)
            print("手动基准价格表检查完成")
        except Exception as e:
            print(f"创建手动基准价格表时出错: {e}")
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 返回字典格式的结果
        return conn
    
    def search_items(self, keyword: str = "", category: str = "", 
                    limit: Optional[int] = 100, offset: int = 0) -> List[Dict]:
        """搜索检测项目"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            where_conditions = []
            params = []
            
            if keyword:
                where_conditions.append("(si.name LIKE ? OR si.number LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])
            
            if category:
                where_conditions.append("si.service_category_name LIKE ?")
                params.append(f"%{category}%")
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            query = f"""
                SELECT si.*, 
                       COUNT(cm.id) as method_count,
                       MAX(ph.fetch_time) as last_update
                FROM service_items si
                LEFT JOIN check_methods cm ON si.id = cm.service_item_id
                LEFT JOIN price_history ph ON si.id = ph.service_item_id
                {where_clause}
                GROUP BY si.id
                ORDER BY si.name
            """
            
            # 只有在 limit 不为 None 时才添加 LIMIT 和 OFFSET
            if limit is not None:
                query += " LIMIT ? OFFSET ?"
                params.extend([limit, offset])
            
            cursor.execute(query, params)
            
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))
            
            return results

    def search_items_by_display_rows(self, keyword: str = "", category: str = "",
                                   limit: Optional[int] = 100, offset: int = 0) -> List[Dict]:
        """按实际显示行数搜索检测项目（方法×采样类型组合），重构以提高效率和准确性"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            where_conditions = []
            params = []

            if keyword:
                where_conditions.append("(si.name LIKE ? OR si.number LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])

            if category:
                where_conditions.append("si.service_category_name LIKE ?")
                params.append(f"%{category}%")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 使用UNION ALL来合并两种情况：
            # 1. 方法有关联的采样类型
            # 2. 方法没有关联的采样类型
            # 只显示非废止的方法
            deprecated_condition = "AND cm.deprecated = 0"

            query = f"""
                SELECT
                    si.id, si.name, si.number, si.service_category_name, si.second_category_name, si.status,
                    cm.id as method_id, cm.name as method_name, cm.method_no, cm.price as method_price,
                    cm.lowest_bid as method_lowest_bid, cm.status as method_status, cm.cma as method_cma,
                    cm.cnas as method_cnas, cm.gov_agree as method_gov_agree, cm.last_date, cm.order_index,
                    st.id as sample_type_id, st.type as sample_type, st.price as sample_price
                FROM service_items si
                JOIN check_methods cm ON si.id = cm.service_item_id
                JOIN sample_types st ON cm.id = st.check_method_id
                {where_clause}
                {deprecated_condition}

                UNION ALL

                SELECT
                    si.id, si.name, si.number, si.service_category_name, si.second_category_name, si.status,
                    cm.id as method_id, cm.name as method_name, cm.method_no, cm.price as method_price,
                    cm.lowest_bid as method_lowest_bid, cm.status as method_status, cm.cma as method_cma,
                    cm.cnas as method_cnas, cm.gov_agree as method_gov_agree, cm.last_date, cm.order_index,
                    NULL as sample_type_id, NULL as sample_type, NULL as sample_price
                FROM service_items si
                JOIN check_methods cm ON si.id = cm.service_item_id
                LEFT JOIN sample_types st ON cm.id = st.check_method_id
                {where_clause}
                {deprecated_condition}
                AND st.id IS NULL

                ORDER BY name, order_index, sample_type
                LIMIT ? OFFSET ?
            """
            
            limit_params = [limit, offset] if limit is not None else []
            cursor.execute(query, params + params + limit_params)

            results = []
            for row in cursor.fetchall():
                row_dict = dict(row)
                item = {
                    'id': row_dict['id'],
                    'name': row_dict['name'],
                    'number': row_dict['number'],
                    'service_category_name': row_dict['service_category_name'],
                    'second_category_name': row_dict['second_category_name'],
                    'status': row_dict['status'],
                    'methods': [{
                        'id': row_dict['method_id'],
                        'name': row_dict['method_name'],
                        'method_no': row_dict['method_no'],
                        'price': row_dict['method_price'],
                        'lowest_bid': row_dict['method_lowest_bid'],
                        'status': row_dict['method_status'],
                        'cma': row_dict['method_cma'],
                        'cnas': row_dict['method_cnas'],
                        'order_index': row_dict['order_index'],
                        'sample_types': []
                    }]
                }
                if row_dict['sample_type_id']:
                    item['methods'][0]['sample_types'].append({
                        'type': row_dict['sample_type'],
                        'price': row_dict['sample_price']
                    })
                results.append(item)

            return results

    def get_total_item_count(self, keyword: str = "", category: str = "") -> int:
        """获取符合条件的项目总数（原方法，保留用于统计）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            where_conditions = []
            params = []

            if keyword:
                where_conditions.append("(si.name LIKE ? OR si.number LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])

            if category:
                where_conditions.append("si.service_category_name LIKE ?")
                params.append(f"%{category}%")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            query = f"""
                SELECT COUNT(DISTINCT si.id) as total
                FROM service_items si
                {where_clause}
            """

            cursor.execute(query, params)
            result = cursor.fetchone()
            return result['total'] if result else 0

    def get_total_display_rows_count(self, keyword: str = "", category: str = "") -> int:
        """获取符合条件的实际显示行数（方法×采样类型组合数），使用与显示逻辑一致的SQL"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            where_conditions = []
            params = []

            if keyword:
                where_conditions.append("(si.name LIKE ? OR si.number LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])

            if category:
                where_conditions.append("si.service_category_name LIKE ?")
                params.append(f"%{category}%")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 使用与 `search_items_by_display_rows` 类似的逻辑来计算总行数
            query = f"""
                SELECT SUM(rows_count) as total_rows FROM (
                    -- 计算有采样类型的方法所占的行数
                    SELECT COUNT(*) as rows_count
                    FROM service_items si
                    JOIN check_methods cm ON si.id = cm.service_item_id
                    JOIN sample_types st ON cm.id = st.check_method_id
                    {where_clause}
                    AND cm.deprecated = 0

                    UNION ALL

                    -- 计算没有采样类型的方法所占的行数
                    SELECT COUNT(*)
                    FROM service_items si
                    JOIN check_methods cm ON si.id = cm.service_item_id
                    LEFT JOIN sample_types st ON cm.id = st.check_method_id
                    {where_clause}
                    AND cm.deprecated = 0
                    AND st.id IS NULL
                )
            """

            # 需要为每个UNION ALL的部分提供参数
            cursor.execute(query, params + params)
            result = cursor.fetchone()
            return result['total_rows'] if result and result['total_rows'] is not None else 0
    
    def get_item_details(self, item_id: int) -> Dict:
        """获取检测项目详情"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取项目基本信息
            cursor.execute("""
                SELECT * FROM service_items WHERE id = ?
            """, (item_id,))
            
            item = cursor.fetchone()
            if not item:
                return {}
            
            item_dict = dict(item)
            
            # 获取检测方法
            cursor.execute("""
                SELECT cm.*,
                       GROUP_CONCAT(st.type || ':' || st.price) as sample_types
                FROM check_methods cm
                LEFT JOIN sample_types st ON cm.id = st.check_method_id
                WHERE cm.service_item_id = ?
                GROUP BY cm.id
                ORDER BY cm.order_index
            """, (item_id,))
            
            methods = []
            for row in cursor.fetchall():
                method = dict(row)
                if method['sample_types']:
                    sample_types = []
                    for st in method['sample_types'].split(','):
                        if ':' in st:
                            type_name, price = st.split(':', 1)
                            sample_types.append({'type': type_name, 'price': price})
                    method['sample_types'] = sample_types
                else:
                    method['sample_types'] = []
                methods.append(method)
            
            item_dict['methods'] = methods
            
            return item_dict
    
    def get_item_price_history(self, item_id: int, days: int = 90) -> List[Dict]:
        """获取单个项目的价格历史"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取项目的价格历史记录
            query = """
                SELECT 
                    ph.fetch_time,
                    cm.name as method_name,
                    ph.check_method_price as price,
                    cm.lowest_bid,
                    'sample info' as sample_info,
                    'update' as change_type
                FROM price_history ph
                LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                WHERE ph.service_item_id = ?
                  AND ph.fetch_time >= datetime('now', '-{} days')
                ORDER BY ph.fetch_time DESC
                LIMIT 50
            """.format(days)
            
            cursor.execute(query, (item_id,))
            
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))
            
            return results
    
    def get_price_history(self, item_id: int = None, method_id: int = None, 
                         days: int = 30) -> List[Dict]:
        """获取价格历史记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            where_conditions = ["fetch_time >= datetime('now', '-{} days')".format(days)]
            params = []
            
            if item_id:
                where_conditions.append("service_item_id = ?")
                params.append(item_id)
            
            if method_id:
                where_conditions.append("check_method_id = ?")
                params.append(method_id)
            
            where_clause = "WHERE " + " AND ".join(where_conditions)
            
            query = f"""
                SELECT * FROM price_trend_view 
                {where_clause}
                ORDER BY fetch_time ASC
            """
            
            cursor.execute(query, params)
            
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))
            
            return results

    def analyze_method_by_project_name(self, project_name: str) -> Dict:
        """根据项目名称分析方法（支持主项目和子项目匹配）"""
        if not project_name or not project_name.strip():
            return {
                'success': False,
                'message': '项目名称不能为空'
            }

        project_name = project_name.strip()

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取所有项目，然后在Python中进行匹配
            cursor.execute("""
                SELECT id, name, child_item, service_category_name, second_category_name
                FROM service_items
                WHERE name IS NOT NULL
                ORDER BY name
            """)

            all_projects = cursor.fetchall()
            matched_project_ids = []

            # 检查每个项目是否匹配
            for project in all_projects:
                project_matched = False

                # 检查主项目名称
                if project_name.lower() in project['name'].lower():
                    project_matched = True

                # 检查子项目
                if not project_matched and project['child_item']:
                    child_items = [item.strip() for item in project['child_item'].split('、') if item.strip()]
                    for child in child_items:
                        if project_name.lower() in child.lower():
                            project_matched = True
                            break

                if project_matched:
                    matched_project_ids.append(project['id'])

            if not matched_project_ids:
                return {
                    'success': False,
                    'message': f'未找到包含"{project_name}"的项目'
                }

            # 获取匹配项目的所有方法
            placeholders = ','.join(['?' for _ in matched_project_ids])
            cursor.execute(f"""
                SELECT cm.*, si.name as service_item_name, si.number as service_item_number,
                       si.service_category_name, si.second_category_name, si.child_item,
                       si.status as item_status, cm.status as method_status,
                       cm.cma, cm.cnas, cm.gov_agree
                FROM check_methods cm
                INNER JOIN service_items si ON cm.service_item_id = si.id
                WHERE cm.service_item_id IN ({placeholders})
                AND cm.deprecated = 0
                ORDER BY si.service_category_name, si.name, cm.name
            """, matched_project_ids)

            methods = cursor.fetchall()

            if not methods:
                return {
                    'success': False,
                    'message': f'在匹配的项目中未找到有效的检测方法'
                }

            # 处理方法数据
            processed_methods = []
            category_groups = {}
            categories = set()

            for method_row in methods:
                method_dict = dict(method_row)
                method_dict['method_id'] = method_dict['id']

                # 确保方法名称字段正确设置
                method_dict['method_name'] = method_dict.get('name') or method_dict.get('method_name', '')

                # 处理价格显示
                analysis_price = method_dict.get('price') or 0
                lowest_bid = method_dict.get('lowest_bid')

                # 最低价格逻辑：如果lowest_bid为null或0，则使用分析价格
                if lowest_bid is None or lowest_bid == 0:
                    lowest_price = analysis_price
                else:
                    lowest_price = lowest_bid

                method_dict['analysis_price'] = analysis_price
                method_dict['lowest_price'] = lowest_price
                method_dict['analysis_price_display'] = analysis_price
                method_dict['lowest_price_display'] = lowest_price

                # 获取当前有效的采样价格（基于最新抓取会话的记录）
                # 首先获取最新的抓取时间
                cursor.execute("""
                    SELECT MAX(fetch_time) as latest_fetch_time
                    FROM price_history
                    WHERE check_method_id = ?
                """, (method_dict['method_id'],))

                latest_fetch_result = cursor.fetchone()
                latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None

                # 获取采样价格信息（包括已删除的）
                if latest_fetch_time:
                    # 只获取最新抓取时间的采样类型
                    cursor.execute("""
                        SELECT DISTINCT st.type, ph.sample_type_price as latest_price
                        FROM price_history ph
                        LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                        WHERE ph.check_method_id = ?
                        AND ph.fetch_time = ?
                        AND st.type IS NOT NULL
                        AND ph.sample_type_price IS NOT NULL
                        ORDER BY st.type
                    """, (method_dict['method_id'], latest_fetch_time))
                else:
                    # 如果没有价格历史记录，返回空列表
                    cursor.execute("SELECT NULL as type, NULL as latest_price WHERE 1=0")

                sample_types = []
                for sample_row in cursor.fetchall():
                    if sample_row['type']:  # 确保type不为None
                        sample_types.append({
                            'type': sample_row['type'],
                            'price': sample_row['latest_price'] or 0
                        })

                method_dict['sample_types'] = sample_types

                # 获取采样价格的首次价格
                for sample_type in sample_types:
                    cursor.execute("""
                        SELECT sample_type_price as initial_price
                        FROM price_history
                        WHERE check_method_id = ? AND sample_type_id = (
                            SELECT id FROM sample_types
                            WHERE check_method_id = ? AND type = ?
                        )
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_dict['method_id'], method_dict['method_id'], sample_type['type']))

                    initial_sample_row = cursor.fetchone()
                    if initial_sample_row:
                        sample_type['initial_price'] = initial_sample_row['initial_price'] or 0
                    else:
                        sample_type['initial_price'] = sample_type.get('price') or 0

                # 获取首次价格信息（用于价格变动对比）
                # 获取最早的价格记录
                cursor.execute("""
                    SELECT check_method_price as initial_analysis_price
                    FROM price_history
                    WHERE check_method_id = ? AND check_method_price IS NOT NULL
                    ORDER BY fetch_time ASC
                    LIMIT 1
                """, (method_dict['method_id'],))

                initial_analysis_row = cursor.fetchone()
                if initial_analysis_row:
                    method_dict['initial_analysis_price'] = initial_analysis_row['initial_analysis_price'] or 0
                else:
                    # 如果没有历史记录，使用当前价格作为初始价格
                    method_dict['initial_analysis_price'] = method_dict.get('analysis_price') or 0

                # 获取最低价格的初始值（从历史记录中获取最早的lowest_bid）
                cursor.execute("""
                    SELECT lowest_bid as initial_lowest_bid
                    FROM price_history
                    WHERE check_method_id = ? AND lowest_bid IS NOT NULL
                    ORDER BY fetch_time ASC
                    LIMIT 1
                """, (method_dict['method_id'],))

                initial_lowest_row = cursor.fetchone()
                if initial_lowest_row:
                    initial_lowest_bid = initial_lowest_row['initial_lowest_bid']
                    # 如果初始最低价格为null或0，使用初始分析价格
                    if initial_lowest_bid is None or initial_lowest_bid == 0:
                        initial_lowest_price = method_dict.get('initial_analysis_price') or 0
                    else:
                        initial_lowest_price = initial_lowest_bid
                else:
                    # 如果没有最低价格历史记录，使用当前最低价格逻辑
                    current_lowest_bid = method_dict.get('lowest_bid')
                    if current_lowest_bid is None or current_lowest_bid == 0:
                        initial_lowest_price = method_dict.get('initial_analysis_price') or 0
                    else:
                        initial_lowest_price = current_lowest_bid

                method_dict['initial_lowest_price'] = initial_lowest_price

                # 获取采样价格的首次价格
                for sample_type in method_dict['sample_types']:
                    cursor.execute("""
                        SELECT sample_type_price as initial_price
                        FROM price_history
                        WHERE check_method_id = ? AND sample_type_id = (
                            SELECT id FROM sample_types
                            WHERE check_method_id = ? AND type = ?
                        )
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_dict['method_id'], method_dict['method_id'], sample_type['type']))

                    initial_sample_row = cursor.fetchone()
                    if initial_sample_row:
                        sample_type['initial_price'] = initial_sample_row['initial_price'] or 0
                    else:
                        sample_type['initial_price'] = sample_type.get('price') or 0

                # 添加价格变动分析（复用控制台页面的逻辑）
                method_id = method_dict['method_id']

                # 分析价格变动
                recent_analysis_change = self._get_recent_analysis_price_change(cursor, method_id, 7)
                method_dict['analysis_price_change'] = recent_analysis_change

                # 最低价格变动
                recent_lowest_change = self._get_recent_lowest_price_change(cursor, method_id, 7)
                method_dict['lowest_price_change'] = recent_lowest_change

                # 采样价格变动
                recent_sample_changes = self._get_recent_sample_price_changes(cursor, method_id, 7)
                method_dict['sample_price_changes'] = recent_sample_changes

                # 添加到分类分组
                category = method_dict.get('service_category_name', '未分类')
                categories.add(category)

                if category not in category_groups:
                    category_groups[category] = {
                        'category_name': category,
                        'methods': []
                    }
                category_groups[category]['methods'].append(method_dict)
                processed_methods.append(method_dict)

            return {
                'success': True,
                'data': {
                    'search_term': project_name,
                    'search_type': 'project_name',
                    'total_methods': len(processed_methods),
                    'total_projects': len(matched_project_ids),
                    'categories_count': len(categories),
                    'methods': processed_methods,
                    'category_groups': category_groups
                }
            }

    def analyze_method_by_name_and_project(self, method_name: str, project_name: str) -> Dict:
        """同时使用方法名称和项目名称进行搜索（AND关系）"""
        if not method_name.strip() and not project_name.strip():
            return {
                'success': False,
                'message': '方法名称和项目名称不能都为空'
            }

        method_name = method_name.strip()
        project_name = project_name.strip()

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 首先找到匹配项目名称的项目ID
            cursor.execute("""
                SELECT id, name, child_item
                FROM service_items
                WHERE name IS NOT NULL
                ORDER BY name
            """)

            all_projects = cursor.fetchall()
            matched_project_ids = []

            # 检查每个项目是否匹配项目名称
            for project in all_projects:
                project_matched = False

                # 检查主项目名称
                if project_name.lower() in project['name'].lower():
                    project_matched = True

                # 检查子项目
                if not project_matched and project['child_item']:
                    child_items = [item.strip() for item in project['child_item'].split('、') if item.strip()]
                    for child in child_items:
                        if project_name.lower() in child.lower():
                            project_matched = True
                            break

                if project_matched:
                    matched_project_ids.append(project['id'])

            if not matched_project_ids:
                return {
                    'success': False,
                    'message': f'未找到包含"{project_name}"的项目'
                }

            # 在匹配的项目中搜索匹配方法名称的方法
            placeholders = ','.join(['?' for _ in matched_project_ids])
            query = f"""
                SELECT cm.*, si.name as service_item_name, si.number as service_item_number,
                       si.service_category_name, si.second_category_name, si.child_item,
                       si.status as item_status, cm.status as method_status,
                       cm.cma, cm.cnas, cm.gov_agree
                FROM check_methods cm
                INNER JOIN service_items si ON cm.service_item_id = si.id
                WHERE cm.service_item_id IN ({placeholders})
                AND cm.name LIKE ?
                AND cm.deprecated = 0
                ORDER BY si.service_category_name, si.name, cm.name
            """
            params = matched_project_ids + [f'%{method_name}%']
            cursor.execute(query, params)

            methods = cursor.fetchall()

            if not methods:
                return {
                    'success': False,
                    'message': f'未找到同时匹配"{method_name}"和"{project_name}"的方法'
                }

            # 处理方法数据
            processed_methods = []
            category_groups = {}
            categories = set()
            projects = set()

            for method_row in methods:
                method_dict = dict(method_row)
                method_dict['method_id'] = method_dict['id']

                # 确保方法名称字段正确设置
                method_dict['method_name'] = method_dict.get('name') or method_dict.get('method_name', '')

                # 处理价格显示
                analysis_price = method_dict.get('price') or 0
                lowest_bid = method_dict.get('lowest_bid')

                # 最低价格逻辑：如果lowest_bid为null或0，则使用分析价格
                if lowest_bid is None or lowest_bid == 0:
                    lowest_price = analysis_price
                else:
                    lowest_price = lowest_bid

                method_dict['analysis_price'] = analysis_price
                method_dict['lowest_price'] = lowest_price
                method_dict['analysis_price_display'] = analysis_price
                method_dict['lowest_price_display'] = lowest_price

                # 获取当前有效的采样价格（基于最新抓取会话的记录）
                # 首先获取最新的抓取时间
                cursor.execute("""
                    SELECT MAX(fetch_time) as latest_fetch_time
                    FROM price_history
                    WHERE check_method_id = ?
                """, (method_dict['method_id'],))

                latest_fetch_result = cursor.fetchone()
                latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None

                # 获取采样价格信息（包括已删除的）
                if latest_fetch_time:
                    # 只获取最新抓取时间的采样类型
                    cursor.execute("""
                        SELECT DISTINCT st.type, ph.sample_type_price as latest_price
                        FROM price_history ph
                        LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                        WHERE ph.check_method_id = ?
                        AND ph.fetch_time = ?
                        AND st.type IS NOT NULL
                        AND ph.sample_type_price IS NOT NULL
                        ORDER BY st.type
                    """, (method_dict['method_id'], latest_fetch_time))
                else:
                    # 如果没有价格历史记录，返回空列表
                    cursor.execute("SELECT NULL as type, NULL as latest_price WHERE 1=0")

                sample_types = []
                for sample_row in cursor.fetchall():
                    if sample_row['type']:  # 确保type不为None
                        sample_types.append({
                            'type': sample_row['type'],
                            'price': sample_row['latest_price'] or 0
                        })

                method_dict['sample_types'] = sample_types

                # 获取采样价格的首次价格
                for sample_type in sample_types:
                    cursor.execute("""
                        SELECT sample_type_price as initial_price
                        FROM price_history
                        WHERE check_method_id = ? AND sample_type_id = (
                            SELECT id FROM sample_types
                            WHERE check_method_id = ? AND type = ?
                        )
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_dict['method_id'], method_dict['method_id'], sample_type['type']))

                    initial_sample_row = cursor.fetchone()
                    if initial_sample_row:
                        sample_type['initial_price'] = initial_sample_row['initial_price'] or 0
                    else:
                        sample_type['initial_price'] = sample_type.get('price') or 0

                # 获取首次价格信息（用于价格变动对比）
                # 获取最早的价格记录
                cursor.execute("""
                    SELECT check_method_price as initial_analysis_price
                    FROM price_history
                    WHERE check_method_id = ? AND check_method_price IS NOT NULL
                    ORDER BY fetch_time ASC
                    LIMIT 1
                """, (method_dict['method_id'],))

                initial_analysis_row = cursor.fetchone()
                if initial_analysis_row:
                    method_dict['initial_analysis_price'] = initial_analysis_row['initial_analysis_price'] or 0
                else:
                    # 如果没有历史记录，使用当前价格作为初始价格
                    method_dict['initial_analysis_price'] = method_dict.get('analysis_price') or 0

                # 获取最低价格的初始值（从历史记录中获取最早的lowest_bid）
                cursor.execute("""
                    SELECT lowest_bid as initial_lowest_bid
                    FROM price_history
                    WHERE check_method_id = ? AND lowest_bid IS NOT NULL
                    ORDER BY fetch_time ASC
                    LIMIT 1
                """, (method_dict['method_id'],))

                initial_lowest_row = cursor.fetchone()
                if initial_lowest_row:
                    initial_lowest_bid = initial_lowest_row['initial_lowest_bid']
                    # 如果初始最低价格为null或0，使用初始分析价格
                    if initial_lowest_bid is None or initial_lowest_bid == 0:
                        initial_lowest_price = method_dict.get('initial_analysis_price') or 0
                    else:
                        initial_lowest_price = initial_lowest_bid
                else:
                    # 如果没有最低价格历史记录，使用当前最低价格逻辑
                    current_lowest_bid = method_dict.get('lowest_bid')
                    if current_lowest_bid is None or current_lowest_bid == 0:
                        initial_lowest_price = method_dict.get('initial_analysis_price') or 0
                    else:
                        initial_lowest_price = current_lowest_bid

                method_dict['initial_lowest_price'] = initial_lowest_price

                # 获取采样价格的首次价格
                for sample_type in method_dict['sample_types']:
                    cursor.execute("""
                        SELECT sample_type_price as initial_price
                        FROM price_history
                        WHERE check_method_id = ? AND sample_type_id = (
                            SELECT id FROM sample_types
                            WHERE check_method_id = ? AND type = ?
                        )
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_dict['method_id'], method_dict['method_id'], sample_type['type']))

                    initial_sample_row = cursor.fetchone()
                    if initial_sample_row:
                        sample_type['initial_price'] = initial_sample_row['initial_price'] or 0
                    else:
                        sample_type['initial_price'] = sample_type.get('price') or 0

                # 添加价格变动分析（复用控制台页面的逻辑）
                method_id = method_dict['method_id']

                # 分析价格变动
                recent_analysis_change = self._get_recent_analysis_price_change(cursor, method_id, 7)
                method_dict['analysis_price_change'] = recent_analysis_change

                # 最低价格变动
                recent_lowest_change = self._get_recent_lowest_price_change(cursor, method_id, 7)
                method_dict['lowest_price_change'] = recent_lowest_change

                # 采样价格变动
                recent_sample_changes = self._get_recent_sample_price_changes(cursor, method_id, 7)
                method_dict['sample_price_changes'] = recent_sample_changes

                # 统计数据
                category = method_dict.get('service_category_name', '未分类')
                categories.add(category)
                projects.add(method_dict.get('service_item_id'))

                # 添加到分类分组
                if category not in category_groups:
                    category_groups[category] = {
                        'category_name': category,
                        'methods': []
                    }
                category_groups[category]['methods'].append(method_dict)
                processed_methods.append(method_dict)

            return {
                'success': True,
                'data': {
                    'search_term': f'{method_name} + {project_name}',
                    'search_type': 'combined',
                    'total_methods': len(processed_methods),
                    'total_projects': len(projects),
                    'categories_count': len(categories),
                    'methods': processed_methods,
                    'category_groups': category_groups
                }
            }

    def get_method_price_trend(self, method_id: int) -> Dict:
        """获取指定方法的价格趋势数据,并进行优化,只返回变动点"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取方法基本信息,包括当前的最低价格
            cursor.execute("""
                SELECT 
                    cm.id, 
                    cm.name as method_name, 
                    cm.method_no, 
                    cm.price as current_price,
                    cm.lowest_bid as current_lowest_bid,
                    si.name as service_item_name, 
                    si.number as service_item_number
                FROM check_methods cm
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE cm.id = ?
            """, (method_id,))

            method_info = cursor.fetchone()
            if not method_info:
                return {
                    'method_info': None,
                    'price_history': [],
                    'message': '未找到指定的方法'
                }
            
            method_info_dict = dict(method_info)

            # 获取所有相关的价格历史记录
            cursor.execute("""
                SELECT
                    ph.fetch_time,
                    ph.check_method_price,
                    ph.lowest_bid,
                    ph.sample_type_price,
                    st.type as sample_type
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ?
                ORDER BY ph.fetch_time ASC
            """, (method_id,))

            history_data = cursor.fetchall()

            # 获取所有历史上出现过的采样类型
            cursor.execute("""
                SELECT DISTINCT st.type
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ? AND st.type IS NOT NULL
                ORDER BY st.type
            """, (method_id,))
            
            all_sample_types = [row['type'] for row in cursor.fetchall()]

            # 将采样价格按类型分组
            sample_prices_by_type = {}
            # 记录所有出现过的时间点
            all_timestamps = set()

            for record in history_data:
                all_timestamps.add(record['fetch_time'])
                if record['sample_type'] and record['sample_type_price'] is not None:
                    if record['sample_type'] not in sample_prices_by_type:
                        sample_prices_by_type[record['sample_type']] = []
                    sample_prices_by_type[record['sample_type']].append({
                        'time': record['fetch_time'],
                        'price': record['sample_type_price']
                    })

            # 处理和过滤价格数据,只保留变动点
            processed_history = []
            last_prices = {}
            last_sample_types_at_time = {}

            # 将所有时间点排序
            sorted_timestamps = sorted(list(all_timestamps))

            for i, timestamp in enumerate(sorted_timestamps):
                current_point = {'fetch_time': timestamp}
                is_changed = False
                
                # 查找当前时间点的所有记录
                records_at_time = [r for r in history_data if r['fetch_time'] == timestamp]
                
                # 提取分析价格
                analysis_price_record = next((r for r in records_at_time if r['check_method_price'] is not None), None)
                current_analysis_price = analysis_price_record['check_method_price'] if analysis_price_record else None

                # 提取最低价格
                lowest_bid_record = next((r for r in records_at_time if r['lowest_bid'] is not None), None)
                current_lowest_bid = lowest_bid_record['lowest_bid'] if lowest_bid_record else None

                # 提取各个采样价格
                current_sample_prices = {}
                current_sample_types_at_time = set()
                for r in records_at_time:
                    if r['sample_type'] and r['sample_type_price'] is not None:
                        current_sample_prices[r['sample_type']] = r['sample_type_price']
                        current_sample_types_at_time.add(r['sample_type'])

                # 检查哪些采样类型被删除了（上次存在但这次不存在）
                if i > 0:
                    deleted_types = last_sample_types_at_time - current_sample_types_at_time
                    for deleted_type in deleted_types:
                        # 删除的采样类型价格设为0
                        current_sample_prices[deleted_type] = 0
                        is_changed = True

                # 填充缺失的价格,使用上一个时间点的值
                # 分析价格
                if current_analysis_price is None:
                    current_analysis_price = last_prices.get('analysis_price')
                
                # 最低价格
                if current_lowest_bid is None:
                    current_lowest_bid = last_prices.get('lowest_bid')

                # 采样价格 - 对于所有历史上出现过的采样类型
                for sample_type in all_sample_types:
                    if sample_type not in current_sample_prices:
                        # 如果当前时间点没有这个采样类型的价格
                        # 检查是否在当前时间点的采样类型集合中
                        if sample_type in last_prices and sample_type not in current_sample_types_at_time:
                            # 如果上次有价格但这次没有出现，说明被删除了
                            current_sample_prices[sample_type] = 0
                        else:
                            # 否则使用上次的价格
                            current_sample_prices[sample_type] = last_prices.get(sample_type)

                # 检查是否有任何价格发生变动
                if i == 0:
                    is_changed = True # 第一个点总是添加
                else:
                    if current_analysis_price != last_prices.get('analysis_price'):
                        is_changed = True
                    if current_lowest_bid != last_prices.get('lowest_bid'):
                        is_changed = True
                    for sample_type, price in current_sample_prices.items():
                        if price != last_prices.get(sample_type):
                            is_changed = True
                            break
                
                if is_changed:
                    current_point['check_method_price'] = current_analysis_price
                    current_point['lowest_bid'] = current_lowest_bid
                    current_point['sample_prices'] = current_sample_prices.copy()
                    processed_history.append(current_point)

                # 更新最新的价格
                if current_analysis_price is not None:
                    last_prices['analysis_price'] = current_analysis_price
                if current_lowest_bid is not None:
                    last_prices['lowest_bid'] = current_lowest_bid
                for sample_type, price in current_sample_prices.items():
                    if price is not None:
                        last_prices[sample_type] = price
                
                # 更新当前时间点的采样类型集合
                last_sample_types_at_time = current_sample_types_at_time.copy()

            return {
                'method_info': method_info_dict,
                'price_history': processed_history,
                'total_records': len(history_data),
                'filtered_records': len(processed_history),
                'all_sample_types': all_sample_types
            }

    def get_detailed_price_changes(self, days: int = 7) -> List[Dict]:
        """获取详细的价格变动情况，按方法分组显示所有价格类型"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取有价格变动的方法列表，使用INNER JOIN确保数据完整性
            cursor.execute("""
                SELECT DISTINCT
                    ph.check_method_id,
                    si.name as service_item_name,
                    si.number as service_item_number,
                    si.service_category_name,
                    cm.name as check_method_name,
                    cm.method_no,
                    cm.price as current_analysis_price,
                    cm.lowest_bid as current_lowest_price
                FROM price_history ph
                INNER JOIN check_methods cm ON ph.check_method_id = cm.id
                INNER JOIN service_items si ON cm.service_item_id = si.id
                WHERE ph.fetch_time >= datetime('now', '-{} days')
                AND si.name IS NOT NULL
                AND si.number IS NOT NULL
                AND cm.name IS NOT NULL
                AND (
                    ph.check_method_id IN (
                        SELECT DISTINCT check_method_id
                        FROM price_history
                        WHERE fetch_time >= datetime('now', '-{} days')
                        AND check_method_price IS NOT NULL
                        GROUP BY check_method_id
                        HAVING COUNT(DISTINCT check_method_price) > 1
                    )
                    OR ph.check_method_id IN (
                        SELECT DISTINCT check_method_id
                        FROM price_history
                        WHERE fetch_time >= datetime('now', '-{} days')
                        AND sample_type_id IS NOT NULL
                        AND sample_type_price IS NOT NULL
                        GROUP BY check_method_id, sample_type_id
                        HAVING COUNT(DISTINCT sample_type_price) > 1
                    )
                )
                ORDER BY si.name, cm.name
            """.format(days, days, days))

            methods_with_changes = []
            for row in cursor.fetchall():
                method_dict = dict(row)
                method_id = method_dict['check_method_id']

                # 获取分析价格变动 - 比较当前价格与初始价格
                cursor.execute("""
                    SELECT
                        check_method_price as current_price,
                        fetch_time
                    FROM price_history
                    WHERE check_method_id = ? AND check_method_price IS NOT NULL
                    AND fetch_time >= datetime('now', '-{} days')
                    ORDER BY fetch_time DESC
                    LIMIT 1
                """.format(days), (method_id,))

                current_analysis = cursor.fetchone()

                # 获取初始价格（最早的记录）
                cursor.execute("""
                    SELECT
                        check_method_price as initial_price,
                        fetch_time
                    FROM price_history
                    WHERE check_method_id = ? AND check_method_price IS NOT NULL
                    ORDER BY fetch_time ASC
                    LIMIT 1
                """, (method_id,))

                initial_analysis = cursor.fetchone()

                if current_analysis and initial_analysis:
                    current = current_analysis['current_price'] or 0
                    initial = initial_analysis['initial_price'] or 0
                    if current != initial and initial != 0:
                        change = current - initial
                        change_percent = ((current - initial) / initial * 100)
                        method_dict['analysis_price_change'] = {
                            'current': current,
                            'previous': initial,
                            'change': change,
                            'change_percent': change_percent
                        }

                # 获取最低价格变动
                # 最低价格来自check_methods表的lowest_bid字段
                # 如果最低价格为0或null，则使用分析价格
                current_lowest = method_dict['current_lowest_price'] or 0
                current_analysis = method_dict['current_analysis_price'] or 0

                # 如果最低价格为0，使用分析价格
                if current_lowest == 0 and current_analysis > 0:
                    current_lowest = current_analysis

                # 由于数据库结构限制，最低价格的历史记录需要从check_methods的历史快照获取
                # 这里我们暂时使用当前值，后续可以考虑改进数据库结构来存储最低价格历史
                method_dict['lowest_price_change'] = {
                    'current': current_lowest,
                    'previous': current_lowest,
                    'change': 0,
                    'change_percent': 0
                }

                # 获取采样价格变动
                cursor.execute("""
                    SELECT DISTINCT st.type, st.price as current_price
                    FROM sample_types st
                    WHERE st.check_method_id = ?
                    ORDER BY st.type
                """, (method_id,))

                sample_types = []
                for sample_row in cursor.fetchall():
                    sample_type = dict(sample_row)
                    sample_type_name = sample_type['type']

                    # 获取该采样类型的当前价格
                    cursor.execute("""
                        SELECT
                            sample_type_price as current_price,
                            fetch_time
                        FROM price_history ph
                        LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                        WHERE ph.check_method_id = ? AND st.type = ?
                        AND ph.sample_type_price IS NOT NULL
                        AND ph.fetch_time >= datetime('now', '-{} days')
                        ORDER BY ph.fetch_time DESC
                        LIMIT 1
                    """.format(days), (method_id, sample_type_name))

                    current_sample = cursor.fetchone()

                    # 获取该采样类型的初始价格
                    cursor.execute("""
                        SELECT
                            sample_type_price as initial_price,
                            fetch_time
                        FROM price_history ph
                        LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                        WHERE ph.check_method_id = ? AND st.type = ?
                        AND ph.sample_type_price IS NOT NULL
                        ORDER BY ph.fetch_time ASC
                        LIMIT 1
                    """, (method_id, sample_type_name))

                    initial_sample = cursor.fetchone()

                    if current_sample and initial_sample:
                        current = current_sample['current_price'] or 0
                        initial = initial_sample['initial_price'] or 0
                        if current != initial and initial != 0:
                            change = current - initial
                            change_percent = ((current - initial) / initial * 100)
                            sample_type['price_change'] = {
                                'current': current,
                                'previous': initial,
                                'change': change,
                                'change_percent': change_percent
                            }
                            sample_types.append(sample_type)
                    elif current_sample and not initial_sample:
                        # 新增的采样类型
                        current = current_sample['current_price'] or 0
                        sample_type['price_change'] = {
                            'current': current,
                            'previous': 0,
                            'change': current,
                            'change_percent': 100 if current > 0 else 0
                        }
                        sample_types.append(sample_type)

                method_dict['sample_price_changes'] = sample_types

                # 只有真正有变动的方法才添加到结果中
                if ('analysis_price_change' in method_dict or
                    method_dict['sample_price_changes']):
                    methods_with_changes.append(method_dict)

            return methods_with_changes

    def get_comprehensive_price_changes(self, days: int = 7, page: int = 1, per_page: int = 20) -> Dict:
        """获取综合价格变动情况，包含近期变动和基准变动，支持分页"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否为首次抓取（数据库中抓取记录很少）
            cursor.execute("SELECT COUNT(DISTINCT fetch_time) FROM price_history")
            fetch_count = cursor.fetchone()[0]

            # 如果只有1次抓取记录，说明是首次抓取，不应该显示为"变动"
            if fetch_count <= 1:
                return {
                    'recent_changes': [],
                    'baseline_changes': [],
                    'recent_count': 0,
                    'baseline_count': 0,
                    'recent_total_pages': 0,
                    'baseline_total_pages': 0,
                    'current_page': page,
                    'per_page': per_page
                }

            # 获取有价格变动的方法列表
            cursor.execute("""
                SELECT DISTINCT
                    ph.check_method_id,
                    si.name as service_item_name,
                    si.number as service_item_number,
                    si.service_category_name,
                    cm.name as check_method_name,
                    cm.method_no,
                    cm.price as current_analysis_price,
                    cm.lowest_bid as current_lowest_price
                FROM price_history ph
                INNER JOIN check_methods cm ON ph.check_method_id = cm.id
                INNER JOIN service_items si ON cm.service_item_id = si.id
                WHERE ph.fetch_time >= datetime('now', '-{} days')
                AND si.name IS NOT NULL
                AND si.number IS NOT NULL
                AND cm.name IS NOT NULL
                ORDER BY si.name, cm.name
            """.format(days))

            recent_changes = []
            baseline_changes = []

            for row in cursor.fetchall():
                method_dict = dict(row)
                method_id = method_dict['check_method_id']

                # 分析近期价格变动（最近两次抓取）
                recent_analysis_change = self._get_recent_analysis_price_change(cursor, method_id, days)
                recent_lowest_change = self._get_recent_lowest_price_change(cursor, method_id, days)
                recent_sample_changes = self._get_recent_sample_price_changes(cursor, method_id, days)

                # 获取历史价格信息（即使没有变动也获取）
                recent_price_info = self._get_recent_price_info(cursor, method_id, days)

                # 分析基准价格变动（当前价格 vs 历史第一次价格）
                baseline_analysis_change = self._get_baseline_analysis_price_change(cursor, method_id)
                baseline_lowest_change = self._get_baseline_lowest_price_change(cursor, method_id)
                baseline_sample_changes = self._get_baseline_sample_price_changes(cursor, method_id)

                # 检查采样类型删除情况
                deleted_sample_types = self._get_deleted_sample_types(cursor, method_id, days)

                # 检查该方法是否有足够的历史记录来判断变动
                cursor.execute("""
                    SELECT COUNT(DISTINCT fetch_time)
                    FROM price_history
                    WHERE check_method_id = ?
                """, (method_id,))
                method_fetch_count = cursor.fetchone()[0]

                # 只有当方法有2次以上的抓取记录时，才考虑为真正的变动
                if method_fetch_count >= 2:
                    # 构建近期变动记录
                    if recent_analysis_change or recent_lowest_change or recent_sample_changes or deleted_sample_types:
                        recent_method = method_dict.copy()
                        if recent_analysis_change:
                            recent_method['analysis_price_change'] = recent_analysis_change
                        if recent_lowest_change:
                            recent_method['lowest_price_change'] = recent_lowest_change
                        recent_method['sample_price_changes'] = recent_sample_changes
                        recent_method['deleted_sample_types'] = deleted_sample_types

                        # 添加历史价格信息
                        if recent_price_info:
                            recent_method.update(recent_price_info)

                        recent_changes.append(recent_method)

                # 构建基准变动记录（需要检查历史记录数量）
                if method_fetch_count >= 2 and (baseline_analysis_change or baseline_lowest_change or baseline_sample_changes):
                    baseline_method = method_dict.copy()
                    if baseline_analysis_change:
                        baseline_method['analysis_price_change'] = baseline_analysis_change
                    if baseline_lowest_change:
                        baseline_method['lowest_price_change'] = baseline_lowest_change
                    baseline_method['sample_price_changes'] = baseline_sample_changes
                    baseline_changes.append(baseline_method)

            # 计算分页信息
            recent_total = len(recent_changes)
            baseline_total = len(baseline_changes)

            recent_total_pages = (recent_total + per_page - 1) // per_page if recent_total > 0 else 0
            baseline_total_pages = (baseline_total + per_page - 1) // per_page if baseline_total > 0 else 0

            # 应用分页
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page

            recent_changes_paged = recent_changes[start_idx:end_idx]
            baseline_changes_paged = baseline_changes[start_idx:end_idx]

            return {
                'recent_changes': recent_changes_paged,
                'baseline_changes': baseline_changes_paged,
                'recent_count': len(recent_changes_paged),
                'baseline_count': len(baseline_changes_paged),
                'recent_total': recent_total,
                'baseline_total': baseline_total,
                'recent_total_pages': recent_total_pages,
                'baseline_total_pages': baseline_total_pages,
                'current_page': page,
                'per_page': per_page
            }

    def _get_recent_analysis_price_change(self, cursor, method_id, days):
        """获取近期分析价格变动（最新价格 vs 上一个不同价格）"""
        # 获取最新的价格记录
        cursor.execute("""
            SELECT
                check_method_price as price,
                fetch_time
            FROM price_history
            WHERE check_method_id = ? AND check_method_price IS NOT NULL
            ORDER BY fetch_time DESC
            LIMIT 1
        """, (method_id,))
        
        latest_price_row = cursor.fetchone()
        if not latest_price_row:
            return None
        
        current_price = latest_price_row['price']
        current_fetch_time = latest_price_row['fetch_time']

        # 获取在最新记录之前的所有价格，并找到第一个不同的价格
        cursor.execute("""
            SELECT
                check_method_price as price
            FROM price_history
            WHERE check_method_id = ? AND check_method_price IS NOT NULL
            AND fetch_time < ?
            ORDER BY fetch_time DESC
        """, (method_id, current_fetch_time))

        previous_price = None
        for row in cursor.fetchall():
            if row['price'] != current_price:
                previous_price = row['price']
                break
        
        if previous_price is not None:
            current = current_price or 0
            previous = previous_price or 0
            if current != previous:
                change = current - previous
                # 避免除零错误，当历史价格为0时使用特殊处理
                if previous != 0:
                    change_percent = ((current - previous) / previous * 100)
                else:
                    # 从0到非0价格，显示为100%增长（或者可以显示为"新增"）
                    change_percent = 100.0 if current > 0 else 0.0
                return {
                    'current': current,
                    'previous': previous,
                    'change': change,
                    'change_percent': change_percent,
                    'change_type': 'recent'
                }

        return None

    def _get_baseline_analysis_price_change(self, cursor, method_id):
        """获取基准分析价格变动（当前价格 vs 基准价格，优先使用手动设置的基准价格）"""
        # 获取当前价格
        cursor.execute("""
            SELECT check_method_price as current_price
            FROM price_history
            WHERE check_method_id = ? AND check_method_price IS NOT NULL
            ORDER BY fetch_time DESC
            LIMIT 1
        """, (method_id,))

        current_result = cursor.fetchone()
        if not current_result:
            return None

        current = current_result['current_price'] or 0

        # 首先检查是否有手动设置的基准价格
        cursor.execute("""
            SELECT baseline_analysis_price
            FROM manual_baseline_prices
            WHERE check_method_id = ? AND baseline_analysis_price IS NOT NULL
        """, (method_id,))

        manual_baseline_result = cursor.fetchone()

        if manual_baseline_result:
            # 使用手动设置的基准价格
            baseline = manual_baseline_result['baseline_analysis_price'] or 0
        else:
            # 使用历史第一次价格作为基准
            cursor.execute("""
                SELECT check_method_price as baseline_price
                FROM price_history
                WHERE check_method_id = ? AND check_method_price IS NOT NULL
                ORDER BY fetch_time ASC
                LIMIT 1
            """, (method_id,))

            baseline_result = cursor.fetchone()
            if not baseline_result:
                return None
            baseline = baseline_result['baseline_price'] or 0

        if current != baseline:
            change = current - baseline
            # 避免除零错误，当基准价格为0时使用特殊处理
            if baseline != 0:
                change_percent = ((current - baseline) / baseline * 100)
            else:
                # 从0到非0价格，显示为100%增长
                change_percent = 100.0 if current > 0 else 0.0
            return {
                'current': current,
                'previous': baseline,
                'change': change,
                'change_percent': change_percent,
                'change_type': 'baseline',
                'is_manual_baseline': manual_baseline_result is not None
            }
        return None

    def _get_recent_lowest_price_change(self, cursor, method_id, days):
        """获取近期最低价格变动（最新价格 vs 上一个不同价格）"""
        # 获取最新的价格记录，应用默认逻辑（null时使用分析价格）
        cursor.execute("""
            SELECT
                CASE
                    WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                    ELSE lowest_bid
                END as effective_lowest_price,
                fetch_time
            FROM price_history
            WHERE check_method_id = ?
            AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
            ORDER BY fetch_time DESC
            LIMIT 1
        """, (method_id,))

        latest_price_row = cursor.fetchone()
        if not latest_price_row:
            return None

        current_price = latest_price_row['effective_lowest_price']
        current_fetch_time = latest_price_row['fetch_time']

        # 获取在最新记录之前的所有价格，并找到第一个不同的价格
        cursor.execute("""
            SELECT
                CASE
                    WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                    ELSE lowest_bid
                END as effective_lowest_price
            FROM price_history
            WHERE check_method_id = ?
            AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
            AND fetch_time < ?
            ORDER BY fetch_time DESC
        """, (method_id, current_fetch_time))

        previous_price = None
        for row in cursor.fetchall():
            if row['effective_lowest_price'] != current_price:
                previous_price = row['effective_lowest_price']
                break

        if previous_price is not None:
            current = current_price or 0
            previous = previous_price or 0
            if current != previous:
                change = current - previous
                # 避免除零错误，当历史价格为0时使用特殊处理
                if previous != 0:
                    change_percent = ((current - previous) / previous * 100)
                else:
                    # 从0到非0价格，显示为100%增长（或者可以显示为"新增"）
                    change_percent = 100.0 if current > 0 else 0.0
                return {
                    'current': current,
                    'previous': previous,
                    'change': change,
                    'change_percent': change_percent,
                    'change_type': 'recent'
                }
        return None

    def _get_baseline_lowest_price_change(self, cursor, method_id):
        """获取基准最低价格变动（当前价格 vs 基准价格，优先使用手动设置的基准价格）"""
        # 获取当前最低价格，应用默认逻辑（null时使用分析价格）
        cursor.execute("""
            SELECT
                CASE
                    WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                    ELSE lowest_bid
                END as current_price
            FROM price_history
            WHERE check_method_id = ?
            AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
            ORDER BY fetch_time DESC
            LIMIT 1
        """, (method_id,))

        current_result = cursor.fetchone()
        if not current_result:
            return None

        current = current_result['current_price'] or 0

        # 首先检查是否有手动设置的基准价格
        cursor.execute("""
            SELECT baseline_lowest_price
            FROM manual_baseline_prices
            WHERE check_method_id = ? AND baseline_lowest_price IS NOT NULL
        """, (method_id,))

        manual_baseline_result = cursor.fetchone()

        if manual_baseline_result:
            # 使用手动设置的基准价格
            baseline = manual_baseline_result['baseline_lowest_price'] or 0
        else:
            # 使用历史第一次最低价格，应用默认逻辑
            cursor.execute("""
                SELECT
                    CASE
                        WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                        ELSE lowest_bid
                    END as baseline_price
                FROM price_history
                WHERE check_method_id = ?
                AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
                ORDER BY fetch_time ASC
                LIMIT 1
            """, (method_id,))

            baseline_result = cursor.fetchone()
            if not baseline_result:
                return None
            baseline = baseline_result['baseline_price'] or 0

        if current != baseline:
            change = current - baseline
            # 避免除零错误，当基准价格为0时使用特殊处理
            if baseline != 0:
                change_percent = ((current - baseline) / baseline * 100)
            else:
                # 从0到非0价格，显示为100%增长
                change_percent = 100.0 if current > 0 else 0.0
            return {
                'current': current,
                'previous': baseline,
                'change': change,
                'change_percent': change_percent,
                'change_type': 'baseline',
                'is_manual_baseline': manual_baseline_result is not None
            }
        return None

    def _get_recent_sample_price_changes(self, cursor, method_id, days):
        """获取近期采样价格变动，包含所有历史采样类型"""
        # 获取最新抓取时间
        cursor.execute("""
            SELECT MAX(fetch_time) as latest_fetch_time
            FROM price_history
            WHERE check_method_id = ?
        """, (method_id,))

        latest_fetch_result = cursor.fetchone()
        latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None

        if not latest_fetch_time:
            return []

        # 获取所有历史上出现过的采样类型（有非零、非空价格的）
        cursor.execute("""
            SELECT DISTINCT st.type
            FROM price_history ph
            LEFT JOIN sample_types st ON ph.sample_type_id = st.id
            WHERE ph.check_method_id = ?
            AND st.type IS NOT NULL
            AND ph.sample_type_price IS NOT NULL
            AND ph.sample_type_price > 0
            ORDER BY st.type
        """, (method_id,))

        all_sample_types = [row['type'] for row in cursor.fetchall() if row['type']]
        sample_changes = []

        for sample_type in all_sample_types:
            # 获取最新抓取时间的价格（当前价格）
            cursor.execute("""
                SELECT sample_type_price as current_price
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ? AND st.type = ?
                AND ph.fetch_time = ?
                LIMIT 1
            """, (method_id, sample_type, latest_fetch_time))

            current_result = cursor.fetchone()

            # 获取该采样类型最近一次非零、非空的历史价格（排除最新抓取时间）
            cursor.execute("""
                SELECT sample_type_price as historical_price
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ? AND st.type = ?
                AND ph.fetch_time < ?
                AND ph.sample_type_price IS NOT NULL
                AND ph.sample_type_price > 0
                ORDER BY ph.fetch_time DESC
                LIMIT 1
            """, (method_id, sample_type, latest_fetch_time))

            historical_result = cursor.fetchone()

            # 处理不同情况
            # 获取当前价格和历史价格
            current = 0
            historical = 0

            if current_result and current_result['current_price'] is not None:
                current = current_result['current_price']

            if historical_result and historical_result['historical_price'] is not None:
                historical = historical_result['historical_price']

            # 判断采样类型状态和价格变动
            if current_result and current_result['current_price'] is not None:
                # 当前有价格
                if historical != 0:
                    # 有历史价格，计算变动
                    if current != historical:
                        change = current - historical
                        change_percent = ((current - historical) / historical * 100) if historical != 0 else (100.0 if current > 0 else 0.0)
                        sample_changes.append({
                            'type': sample_type,
                            'current_price': current,
                            'price_change': {
                                'current': current,
                                'previous': historical,
                                'change': change,
                                'change_percent': change_percent,
                                'change_type': 'recent'
                            }
                        })
                else:
                    # 没有历史价格，说明是新增的
                    sample_changes.append({
                        'type': sample_type,
                        'current_price': current,
                        'status': 'new',
                        'price_change': {
                            'current': current,
                            'previous': 0,
                            'change': current,
                            'change_percent': 100.0 if current > 0 else 0.0,
                            'change_type': 'recent_addition'
                        }
                    })
            else:
                # 当前没有价格
                # 注意：删除的采样类型由_get_deleted_sample_types方法处理，这里不再重复添加
                # 如果当前没有价格且历史也没有价格，则跳过（不应该出现这种情况）
                pass

        return sample_changes

    def _get_baseline_sample_price_changes(self, cursor, method_id):
        """获取基准采样价格变动（当前价格 vs 基准价格，优先使用手动设置的基准价格）"""
        # 首先获取最新抓取时间
        cursor.execute("""
            SELECT MAX(fetch_time) as latest_fetch_time
            FROM price_history
            WHERE check_method_id = ?
        """, (method_id,))

        latest_fetch_result = cursor.fetchone()
        latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None

        if not latest_fetch_time:
            return []

        # 检查是否有手动设置的基准采样价格
        cursor.execute("""
            SELECT baseline_sample_prices
            FROM manual_baseline_prices
            WHERE check_method_id = ? AND baseline_sample_prices IS NOT NULL
        """, (method_id,))

        manual_baseline_result = cursor.fetchone()
        manual_baseline_prices = {}

        if manual_baseline_result and manual_baseline_result['baseline_sample_prices']:
            try:
                manual_baseline_prices = json.loads(manual_baseline_result['baseline_sample_prices'])
            except:
                manual_baseline_prices = {}

        # 获取历史上所有出现过的采样类型（不仅仅是最新的）
        cursor.execute("""
            SELECT DISTINCT st.type
            FROM price_history ph
            LEFT JOIN sample_types st ON ph.sample_type_id = st.id
            WHERE ph.check_method_id = ?
            AND st.type IS NOT NULL
            AND ph.sample_type_price IS NOT NULL
            ORDER BY st.type
        """, (method_id,))

        all_sample_types = [row['type'] for row in cursor.fetchall() if row['type']]
        sample_changes = []

        for sample_type in all_sample_types:
            # 检查在最新抓取时间中是否存在该采样类型
            cursor.execute("""
                SELECT sample_type_price as current_price
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ? AND st.type = ?
                AND ph.fetch_time = ?
                LIMIT 1
            """, (method_id, sample_type, latest_fetch_time))

            current_result = cursor.fetchone()

            # 获取基准价格（优先使用手动设置的基准价格）
            if sample_type in manual_baseline_prices:
                # 使用手动设置的基准价格
                baseline = manual_baseline_prices[sample_type]
                is_manual_baseline = True
            else:
                # 使用历史第一次价格作为基准
                cursor.execute("""
                    SELECT sample_type_price as baseline_price
                    FROM price_history ph
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.check_method_id = ? AND st.type = ?
                    AND ph.sample_type_price IS NOT NULL
                    ORDER BY ph.fetch_time ASC
                    LIMIT 1
                """, (method_id, sample_type))

                baseline_result = cursor.fetchone()
                if not baseline_result:
                    continue  # 跳过没有历史价格的采样类型
                baseline = baseline_result['baseline_price'] or 0
                is_manual_baseline = False

            # 处理当前价格和基准价格
            if not current_result:
                # 在最新抓取中没有找到该采样类型，说明已被删除
                # 已删除的采样类型
                sample_changes.append({
                    'type': sample_type,
                    'current_price': 0,
                    'status': 'deleted',
                    'last_price': baseline,
                    'price_change': {
                        'current': 0,
                        'previous': baseline,
                        'change': -baseline,
                        'change_percent': -100.0,
                        'change_type': 'baseline_deletion',
                        'is_manual_baseline': is_manual_baseline
                    }
                })
            elif current_result:
                # 在最新抓取中找到该采样类型，计算价格变动
                current = current_result['current_price'] or 0
                change = current - baseline

                # 避免除零错误，当基准价格为0时使用特殊处理
                if baseline != 0:
                    change_percent = ((current - baseline) / baseline * 100)
                else:
                    # 从0到非0价格，显示为100%增长
                    change_percent = 100.0 if current > 0 else 0.0

                # 只有当价格有实际变动时才添加到结果中（变动不为0）
                if change != 0:
                    sample_changes.append({
                        'type': sample_type,
                        'current_price': current,
                        'price_change': {
                            'current': current,
                            'previous': baseline,
                            'change': change,
                            'change_percent': change_percent,
                            'change_type': 'baseline',
                            'is_manual_baseline': is_manual_baseline
                        }
                    })

        return sample_changes

    def _get_deleted_sample_types(self, cursor, method_id, days):
        """获取已删除的采样类型 - 显示所有历史上存在过但现在不存在的采样类型"""
        # 获取最新的抓取时间
        cursor.execute("""
            SELECT MAX(fetch_time) as latest_fetch_time
            FROM price_history
            WHERE check_method_id = ?
        """, (method_id,))

        latest_result = cursor.fetchone()
        latest_fetch_time = latest_result['latest_fetch_time'] if latest_result else None

        if not latest_fetch_time:
            return []

        # 获取历史上所有出现过的采样类型（有非零、非空价格的）
        cursor.execute("""
            SELECT DISTINCT st.type
            FROM price_history ph
            LEFT JOIN sample_types st ON ph.sample_type_id = st.id
            WHERE ph.check_method_id = ?
            AND st.type IS NOT NULL
            AND ph.sample_type_price IS NOT NULL
            AND ph.sample_type_price > 0
            ORDER BY st.type
        """, (method_id,))

        all_historical_types = [row['type'] for row in cursor.fetchall() if row['type']]

        # 获取最新抓取时间存在的采样类型（有非零、非空价格的）
        cursor.execute("""
            SELECT DISTINCT st.type
            FROM price_history ph
            LEFT JOIN sample_types st ON ph.sample_type_id = st.id
            WHERE ph.check_method_id = ?
            AND ph.fetch_time = ?
            AND st.type IS NOT NULL
            AND ph.sample_type_price IS NOT NULL
            AND ph.sample_type_price > 0
        """, (method_id, latest_fetch_time))

        current_types = [row['type'] for row in cursor.fetchall() if row['type']]

        # 找出历史上存在但现在不存在的采样类型
        deleted_types = []
        for hist_type in all_historical_types:
            if hist_type not in current_types:
                # 获取该采样类型最后一次非零、非空的价格
                cursor.execute("""
                    SELECT ph.sample_type_price as last_price, ph.fetch_time as last_seen
                    FROM price_history ph
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.check_method_id = ? AND st.type = ?
                    AND ph.sample_type_price IS NOT NULL
                    AND ph.sample_type_price > 0
                    ORDER BY ph.fetch_time DESC
                    LIMIT 1
                """, (method_id, hist_type))
        

                last_record = cursor.fetchone()
                if last_record:
                    deleted_types.append({
                        'type': hist_type,
                        'last_price': last_record['last_price'] or 0,
                        'last_seen': last_record['last_seen'],
                        'status': 'deleted',
                        'current': 0,  # 删除后价格归为0
                        'previous': last_record['last_price'] or 0,
                        'change': -(last_record['last_price'] or 0),
                        'change_percent': -100.0,  # 删除表示-100%变化
                        'change_type': 'deletion',
                        'price_change': {
                            'current': 0,
                            'previous': last_record['last_price'] or 0,
                            'change': -(last_record['last_price'] or 0),
                            'change_percent': -100.0,
                            'change_type': 'deletion'
                        }
                    })

        return deleted_types

    def _get_recent_price_info(self, cursor, method_id, days):
        """获取近期价格信息（包括历史价格，即使没有变动）"""
        # 获取分析价格的历史信息
        cursor.execute("""
            SELECT
                check_method_price as price,
                fetch_time
            FROM price_history
            WHERE check_method_id = ? AND check_method_price IS NOT NULL
            ORDER BY fetch_time DESC
            LIMIT 2
        """, (method_id,))

        analysis_prices = cursor.fetchall()

        # 获取最低价格的历史信息
        cursor.execute("""
            SELECT
                lowest_bid as price,
                fetch_time
            FROM price_history
            WHERE check_method_id = ? AND lowest_bid IS NOT NULL
            ORDER BY fetch_time DESC
            LIMIT 2
        """, (method_id,))

        lowest_prices = cursor.fetchall()

        price_info = {}

        # 处理分析价格历史
        if len(analysis_prices) >= 2:
            price_info['previous_analysis_price'] = analysis_prices[1]['price']
        elif len(analysis_prices) == 1:
            price_info['previous_analysis_price'] = analysis_prices[0]['price']

        # 处理最低价格历史
        if len(lowest_prices) >= 2:
            price_info['previous_lowest_price'] = lowest_prices[1]['price']
        elif len(lowest_prices) == 1:
            price_info['previous_lowest_price'] = lowest_prices[0]['price']

        return price_info if price_info else None

    def get_price_changes(self, days: int = 7) -> List[Dict]:
        """获取价格变动情况（包含所有价格类型）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 分析价格变动
            analysis_price_query = """
                WITH price_comparison AS (
                    SELECT
                        ph.service_item_id,
                        ph.check_method_id,
                        ph.sample_type_id,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        cm.name as check_method_name,
                        cm.method_no,
                        st.type as sample_type,
                        ph.check_method_price as current_price,
                        ph.fetch_time,
                        '分析价格' as price_type,
                        LAG(ph.check_method_price) OVER (
                            PARTITION BY ph.service_item_id, ph.check_method_id
                            ORDER BY ph.fetch_time
                        ) as prev_price
                    FROM price_history ph
                    LEFT JOIN service_items si ON ph.service_item_id = si.id
                    LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.fetch_time >= datetime('now', '-{} days')
                    AND ph.check_method_price IS NOT NULL
                )
                SELECT *,
                       (current_price - prev_price) as price_change,
                       CASE
                           WHEN prev_price IS NULL THEN 0
                           WHEN prev_price = 0 THEN
                               CASE
                                   WHEN current_price = 0 THEN 0
                                   ELSE 100
                               END
                           ELSE ((current_price - prev_price) / prev_price * 100)
                       END as price_change_percent
                FROM price_comparison
                WHERE prev_price IS NOT NULL
                AND current_price != prev_price
            """.format(days)

            # 采样价格变动
            sample_price_query = """
                WITH price_comparison AS (
                    SELECT
                        ph.service_item_id,
                        ph.check_method_id,
                        ph.sample_type_id,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        cm.name as check_method_name,
                        cm.method_no,
                        st.type as sample_type,
                        ph.sample_type_price as current_price,
                        ph.fetch_time,
                        '采样价格' as price_type,
                        LAG(ph.sample_type_price) OVER (
                            PARTITION BY ph.service_item_id, ph.check_method_id, ph.sample_type_id
                            ORDER BY ph.fetch_time
                        ) as prev_price
                    FROM price_history ph
                    LEFT JOIN service_items si ON ph.service_item_id = si.id
                    LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.fetch_time >= datetime('now', '-{} days')
                    AND ph.sample_type_price IS NOT NULL
                    AND ph.sample_type_id IS NOT NULL
                )
                SELECT *,
                       (current_price - prev_price) as price_change,
                       CASE
                           WHEN prev_price IS NULL THEN 0
                           WHEN prev_price = 0 THEN
                               CASE
                                   WHEN current_price = 0 THEN 0
                                   ELSE 100
                               END
                           ELSE ((current_price - prev_price) / prev_price * 100)
                       END as price_change_percent
                FROM price_comparison
                WHERE prev_price IS NOT NULL
                AND current_price != prev_price
            """.format(days)

            # 分析成本变动
            analysis_cost_query = """
                WITH price_comparison AS (
                    SELECT
                        ph.service_item_id,
                        ph.check_method_id,
                        ph.sample_type_id,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        cm.name as check_method_name,
                        cm.method_no,
                        st.type as sample_type,
                        ph.analysis_cost as current_price,
                        ph.fetch_time,
                        '分析成本' as price_type,
                        LAG(ph.analysis_cost) OVER (
                            PARTITION BY ph.service_item_id, ph.check_method_id
                            ORDER BY ph.fetch_time
                        ) as prev_price
                    FROM price_history ph
                    LEFT JOIN service_items si ON ph.service_item_id = si.id
                    LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.fetch_time >= datetime('now', '-{} days')
                    AND ph.analysis_cost IS NOT NULL
                )
                SELECT *,
                       (current_price - prev_price) as price_change,
                       CASE
                           WHEN prev_price IS NULL THEN 0
                           WHEN prev_price = 0 THEN
                               CASE
                                   WHEN current_price = 0 THEN 0
                                   ELSE 100
                               END
                           ELSE ((current_price - prev_price) / prev_price * 100)
                       END as price_change_percent
                FROM price_comparison
                WHERE prev_price IS NOT NULL
                AND current_price != prev_price
            """.format(days)

            # 分别执行查询并合并结果
            all_results = []

            # 执行分析价格查询
            cursor.execute(analysis_price_query)
            for row in cursor.fetchall():
                all_results.append(dict(row))

            # 执行采样价格查询
            cursor.execute(sample_price_query)
            for row in cursor.fetchall():
                all_results.append(dict(row))

            # 执行分析成本查询
            cursor.execute(analysis_cost_query)
            for row in cursor.fetchall():
                all_results.append(dict(row))

            # 按时间排序
            all_results.sort(key=lambda x: x['fetch_time'], reverse=True)

            return all_results
    
    def get_method_change_statistics(self, time_range: str = 'month', months: int = None) -> Dict:
        """获取基于last_date字段的变动方法统计

        Args:
            time_range: 时间范围类型 ('year', 'quarter', 'month')
            months: 自定义月数，如果不指定则使用默认值

        Returns:
            Dict: 包含统计数据的字典
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 根据时间范围确定分组方式和默认月数
            if time_range == 'year':
                # 按月分组
                date_format = "%Y-%m"
                date_trunc = "strftime('%Y-%m', last_date)"
                period_name = "月份"
                default_months = 12 if months is None else months
            elif time_range == 'quarter':
                # 按周分组
                date_format = "%Y-W%W"
                date_trunc = "strftime('%Y-W%W', last_date)"
                period_name = "周"
                default_months = 6 if months is None else months
            else:  # month
                # 按天分组
                date_format = "%Y-%m-%d"
                date_trunc = "strftime('%Y-%m-%d', last_date)"
                period_name = "日期"
                default_months = 3 if months is None else months

            # 查询有变动的方法统计
            query = f"""
                SELECT
                    {date_trunc} as period,
                    COUNT(*) as method_count
                FROM check_methods
                WHERE last_date IS NOT NULL
                  AND last_date >= datetime('now', '-{default_months} months')
                GROUP BY {date_trunc}
                HAVING COUNT(*) > 0
                ORDER BY period
            """

            cursor.execute(query)
            results = cursor.fetchall()

            statistics = []
            for row in results:
                statistics.append({
                    'period': row['period'],
                    'method_count': row['method_count'],
                    'period_name': period_name
                })

            return {
                'statistics': statistics,
                'time_range': time_range,
                'months': default_months,
                'total_periods': len(statistics)
            }

    def get_method_change_statistics_v2(self, time_range: str = 'month', months: int = None) -> Dict:
        """获取基于method_change_history表的变动方法统计（新版本）

        Args:
            time_range: 时间范围类型 ('year', 'quarter', 'month')
            months: 自定义月数，如果不指定则使用默认值

        Returns:
            Dict: 包含统计数据的字典
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查method_change_history表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='method_change_history'
            """)
            if not cursor.fetchone():
                print("method_change_history表不存在，使用原有统计方法")
                return self.get_method_change_statistics(time_range, months)

            # 根据时间范围确定分组方式和默认月数
            if time_range == 'year':
                # 按月分组
                date_format = "%Y-%m"
                date_trunc = "strftime('%Y-%m', change_date)"
                period_name = "月份"
                default_months = 12 if months is None else months
            elif time_range == 'quarter':
                # 按周分组
                date_format = "%Y-W%W"
                date_trunc = "strftime('%Y-W%W', change_date)"
                period_name = "周"
                default_months = 6 if months is None else months
            else:  # month
                # 按天分组
                date_format = "%Y-%m-%d"
                date_trunc = "strftime('%Y-%m-%d', change_date)"
                period_name = "日期"
                default_months = 3 if months is None else months

            # 查询变动历史统计 - 按变动日期分组，统计不重复的方法数量
            query = f"""
                SELECT
                    {date_trunc} as period,
                    COUNT(DISTINCT method_id) as method_count
                FROM method_change_history
                WHERE change_date >= date('now', '-{default_months} months')
                  AND change_type != 'historical_migration'
                GROUP BY {date_trunc}
                HAVING COUNT(DISTINCT method_id) > 0
                ORDER BY period
            """

            cursor.execute(query)
            results = cursor.fetchall()

            statistics = []
            for row in results:
                statistics.append({
                    'period': row['period'],
                    'method_count': row['method_count'],
                    'period_name': period_name
                })

            return {
                'statistics': statistics,
                'time_range': time_range,
                'months': default_months,
                'total_periods': len(statistics),
                'data_source': 'method_change_history'
            }

    def get_methods_changed_on_date_v2(self, target_date: str) -> List[Dict]:
        """获取指定日期变动的方法详情（基于变动历史表）

        Args:
            target_date: 目标日期，格式为 'YYYY-MM-DD'

        Returns:
            List[Dict]: 方法详细信息列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查method_change_history表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='method_change_history'
            """)
            if not cursor.fetchone():
                print("method_change_history表不存在，使用原有方法")
                return self.get_methods_changed_on_date(target_date)

            # 查询指定日期有变动的方法
            query = """
                SELECT DISTINCT
                    cm.id as method_id,
                    cm.method_no,
                    cm.name as method_name,
                    cm.price as analysis_price,
                    cm.lowest_bid as lowest_price,
                    cm.cma,
                    cm.cnas,
                    cm.gov_agree,
                    cm.status as method_status,
                    cm.last_date,
                    si.id as service_item_id,
                    si.name as service_item_name,
                    si.number as service_item_number,
                    si.child_item,
                    si.service_category_name,
                    si.second_category_name,
                    si.status as item_status,
                    GROUP_CONCAT(mch.change_type, ', ') as change_types,
                    GROUP_CONCAT(mch.field_name, ', ') as changed_fields
                FROM method_change_history mch
                INNER JOIN check_methods cm ON mch.method_id = cm.id
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE DATE(mch.change_date) = DATE(?)
                  AND mch.change_type != 'historical_migration'
                GROUP BY cm.id
                ORDER BY si.service_category_name, si.name, cm.name
            """

            cursor.execute(query, (target_date,))
            methods = []

            for row in cursor.fetchall():
                method_dict = dict(row)
                methods.append(method_dict)

            return methods

    def get_detailed_change_statistics(self, time_range: str = 'month', months: int = None) -> Dict:
        """获取详细的变动统计（新增方法、资质变更、价格变更）
        优先使用统计汇总表，回退到实时计算

        Args:
            time_range: 时间范围类型 ('year', 'quarter', 'month')
            months: 自定义月数，如果不指定则使用默认值

        Returns:
            Dict: 包含详细统计数据的字典
        """
        # 尝试从统计汇总表获取数据
        try:
            summary_result = self._get_statistics_from_summary_table(time_range, months)
            if summary_result and summary_result.get('data_source') == 'statistics_summary':
                print(f"使用统计汇总表数据: {time_range}_{months}")
                return summary_result
        except Exception as e:
            print(f"从统计汇总表获取数据失败，回退到实时计算: {e}")

        # 回退到原有的实时计算方法
        return self._get_detailed_change_statistics_realtime(time_range, months)

    def _get_statistics_from_summary_table(self, time_range: str, months: int = None) -> Dict:
        """从统计汇总表获取统计数据"""
        with self.get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 检查统计汇总表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='statistics_summary'
            """)
            if not cursor.fetchone():
                return {}

            # 设置默认月数
            default_months = {'year': 24, 'quarter': 12, 'month': 6}.get(time_range, 6)
            if months is None:
                months = default_months

            # 检查数据新鲜度
            cursor.execute("""
                SELECT MAX(last_updated) as last_update
                FROM statistics_summary
                WHERE period_type = ?
            """, (time_range,))

            result = cursor.fetchone()
            if result and result['last_update']:
                from datetime import datetime, timedelta
                last_update = datetime.fromisoformat(result['last_update'])
                now = datetime.now()

                # 如果数据超过6小时，认为过期
                if (now - last_update).total_seconds() > 6 * 3600:
                    print(f"统计汇总表数据过期，最后更新: {last_update}")
                    return {}

            # 获取各类统计数据
            new_methods = self._get_summary_data_by_type('new_methods', time_range, months)
            cma_changes = self._get_summary_data_by_type('qualification_changes_cma', time_range, months)
            cnas_changes = self._get_summary_data_by_type('qualification_changes_cnas', time_range, months)
            nhc_changes = self._get_summary_data_by_type('qualification_changes_nhc', time_range, months)
            price_changes = self._get_summary_data_by_type('price_changes', time_range, months)

            # 组装返回数据
            return {
                'new_methods': new_methods.get('statistics', []),
                'qualification_changes': {
                    'cma': cma_changes.get('statistics', []),
                    'cnas': cnas_changes.get('statistics', []),
                    'nhc': nhc_changes.get('statistics', [])
                },
                'price_changes': price_changes.get('statistics', []),
                'method_details': {
                    'new_methods': new_methods.get('method_details', {}),
                    'qualification_changes': {
                        'cma': cma_changes.get('method_details', {}),
                        'cnas': cnas_changes.get('method_details', {}),
                        'nhc': nhc_changes.get('method_details', {})
                    },
                    'price_changes': price_changes.get('method_details', {})
                },
                'time_range': time_range,
                'months': months,
                'total_periods': len(new_methods.get('statistics', [])),
                'data_source': 'statistics_summary'
            }

    def _get_summary_data_by_type(self, stat_type: str, period_type: str, months: int) -> Dict:
        """从汇总表获取指定类型的统计数据"""
        with self.get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 构建时间范围条件
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=months * 30)

            query = """
                SELECT period_value, count_value, method_ids
                FROM statistics_summary
                WHERE stat_type = ? AND period_type = ? AND period_value >= ?
                ORDER BY period_value
            """

            cursor.execute(query, (stat_type, period_type, cutoff_date.strftime('%Y-%m')))
            results = cursor.fetchall()

            # 转换为标准格式
            statistics = []
            method_details = {}

            for row in results:
                period = row['period_value']
                count = row['count_value']
                method_ids_json = row['method_ids']

                statistics.append({
                    'period': period,
                    'count': count,
                    'period_name': self._get_period_name(period_type)
                })

                if method_ids_json:
                    import json
                    method_ids = json.loads(method_ids_json)
                    if method_ids:
                        method_details[period] = method_ids

            return {
                'statistics': statistics,
                'method_details': method_details
            }

    def _get_period_name(self, period_type: str) -> str:
        """获取时间周期名称"""
        period_names = {
            'year': '年',
            'quarter': '季度',
            'month': '月'
        }
        return period_names.get(period_type, '时间')

    def _get_detailed_change_statistics_realtime(self, time_range: str = 'month', months: int = None) -> Dict:
        """获取详细的变动统计（实时计算版本）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 根据时间范围确定分组方式和默认月数
            if time_range == 'year':
                date_trunc = "strftime('%Y-%m', fetch_time)"
                period_name = "月份"
                default_months = 12 if months is None else months
            elif time_range == 'quarter':
                date_trunc = "strftime('%Y-W%W', fetch_time)"
                period_name = "周"
                default_months = 6 if months is None else months
            else:  # month
                date_trunc = "strftime('%Y-%m-%d', fetch_time)"
                period_name = "日期"
                default_months = 3 if months is None else months

            # 获取所有抓取时间点，按时间排序
            cursor.execute("""
                SELECT DISTINCT fetch_time
                FROM price_history
                WHERE fetch_time >= datetime('now', '-{} months')
                ORDER BY fetch_time
            """.format(default_months))
            fetch_times = [row[0] for row in cursor.fetchall()]

            if len(fetch_times) < 2:
                return {
                    'new_methods': [],
                    'qualification_changes': {'cma': [], 'cnas': [], 'nhc': []},
                    'price_changes': [],
                    'time_range': time_range,
                    'total_periods': 0
                }

            # 统计结果字典
            new_methods_stats = {}
            cma_changes_stats = {}
            cnas_changes_stats = {}
            nhc_changes_stats = {}
            price_changes_stats = {}

            # 详细方法ID信息字典
            new_methods_details = {}
            cma_changes_details = {}
            cnas_changes_details = {}
            nhc_changes_details = {}
            price_changes_details = {}

            # 逐对比较相邻的抓取时间点
            for i in range(1, len(fetch_times)):
                prev_time = fetch_times[i-1]
                curr_time = fetch_times[i]

                # 获取时间段标识
                cursor.execute(f"SELECT {date_trunc} as period FROM (SELECT ? as fetch_time)", (curr_time,))
                period = cursor.fetchone()[0]

                # 1. 检测新增方法（同时获取方法ID列表）
                new_methods_count, new_method_ids = self._count_new_methods_with_ids(cursor, prev_time, curr_time)
                if new_methods_count > 0:
                    if period not in new_methods_details:
                        new_methods_details[period] = []
                    # 添加新的方法ID，避免重复
                    existing_ids = set(new_methods_details[period])
                    new_unique_ids = [mid for mid in new_method_ids if mid not in existing_ids]
                    new_methods_details[period].extend(new_unique_ids)
                    # 重新计算该时间段的总数（基于去重后的ID列表）
                    new_methods_stats[period] = len(new_methods_details[period])

                # 2. 检测资质变更（同时获取方法ID列表）
                cma_count, cnas_count, nhc_count, cma_ids, cnas_ids, nhc_ids = self._count_qualification_changes_with_ids(cursor, prev_time, curr_time)

                # CMA资质变更去重处理
                if cma_count > 0:
                    if period not in cma_changes_details:
                        cma_changes_details[period] = []
                    existing_cma_ids = set(cma_changes_details[period])
                    new_unique_cma_ids = [mid for mid in cma_ids if mid not in existing_cma_ids]
                    cma_changes_details[period].extend(new_unique_cma_ids)
                    cma_changes_stats[period] = len(cma_changes_details[period])

                # CNAS资质变更去重处理
                if cnas_count > 0:
                    if period not in cnas_changes_details:
                        cnas_changes_details[period] = []
                    existing_cnas_ids = set(cnas_changes_details[period])
                    new_unique_cnas_ids = [mid for mid in cnas_ids if mid not in existing_cnas_ids]
                    cnas_changes_details[period].extend(new_unique_cnas_ids)
                    cnas_changes_stats[period] = len(cnas_changes_details[period])

                # NHC资质变更去重处理
                if nhc_count > 0:
                    if period not in nhc_changes_details:
                        nhc_changes_details[period] = []
                    existing_nhc_ids = set(nhc_changes_details[period])
                    new_unique_nhc_ids = [mid for mid in nhc_ids if mid not in existing_nhc_ids]
                    nhc_changes_details[period].extend(new_unique_nhc_ids)
                    nhc_changes_stats[period] = len(nhc_changes_details[period])

                # 3. 检测价格变更（同时获取方法ID列表）
                price_count, price_change_ids = self._count_price_changes_with_ids(cursor, prev_time, curr_time)
                if price_count > 0:
                    if period not in price_changes_details:
                        price_changes_details[period] = []
                    # 价格变更去重处理
                    existing_price_ids = set(price_changes_details[period])
                    new_unique_price_ids = [mid for mid in price_change_ids if mid not in existing_price_ids]
                    price_changes_details[period].extend(new_unique_price_ids)
                    price_changes_stats[period] = len(price_changes_details[period])

            # 转换为列表格式
            def stats_to_list(stats_dict):
                return [{'period': period, 'count': count, 'period_name': period_name}
                       for period, count in sorted(stats_dict.items())]

            return {
                'new_methods': stats_to_list(new_methods_stats),
                'qualification_changes': {
                    'cma': stats_to_list(cma_changes_stats),
                    'cnas': stats_to_list(cnas_changes_stats),
                    'nhc': stats_to_list(nhc_changes_stats)
                },
                'price_changes': stats_to_list(price_changes_stats),
                'time_range': time_range,
                'total_periods': len(set(list(new_methods_stats.keys()) +
                                        list(cma_changes_stats.keys()) +
                                        list(cnas_changes_stats.keys()) +
                                        list(nhc_changes_stats.keys()) +
                                        list(price_changes_stats.keys()))),
                # 保存详细的方法ID信息，用于精确展示
                'method_details': {
                    'new_methods': new_methods_details,
                    'qualification_changes': {
                        'cma': cma_changes_details,
                        'cnas': cnas_changes_details,
                        'nhc': nhc_changes_details
                    },
                    'price_changes': price_changes_details
                }
            }

    def get_methods_by_date(self, target_date: str) -> List[Dict]:
        """获取指定日期有变动的方法详细信息

        Args:
            target_date: 目标日期 (YYYY-MM-DD格式)

        Returns:
            List[Dict]: 方法详细信息列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 查询指定日期有变动的方法
            query = """
                SELECT DISTINCT
                    cm.id as method_id,
                    cm.method_no,
                    cm.name as method_name,
                    cm.price as analysis_price,
                    cm.lowest_bid as lowest_price,
                    cm.cma,
                    cm.cnas,
                    cm.gov_agree,
                    cm.status as method_status,
                    cm.last_date,
                    si.id as service_item_id,
                    si.name as service_item_name,
                    si.number as service_item_number,
                    si.child_item,
                    si.service_category_name,
                    si.second_category_name,
                    si.status as item_status
                FROM check_methods cm
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE DATE(cm.last_date) = DATE(?)
                ORDER BY si.service_category_name, si.name, cm.name
            """

            cursor.execute(query, (target_date,))
            methods = []

            for row in cursor.fetchall():
                method_dict = dict(row)

                # 获取最新抓取时间
                cursor.execute("""
                    SELECT MAX(fetch_time) as latest_fetch_time
                    FROM price_history
                    WHERE check_method_id = ?
                """, (method_dict['method_id'],))

                latest_fetch_result = cursor.fetchone()
                latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None

                # 获取采样价格信息
                sample_prices = []
                if latest_fetch_time:
                    cursor.execute("""
                        SELECT DISTINCT st.type, ph.sample_type_price as latest_price
                        FROM price_history ph
                        LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                        WHERE ph.check_method_id = ?
                        AND ph.fetch_time = ?
                        AND st.type IS NOT NULL
                        AND ph.sample_type_price IS NOT NULL
                        ORDER BY st.type
                    """, (method_dict['method_id'], latest_fetch_time))

                    for sample_row in cursor.fetchall():
                        sample_prices.append({
                            'type': sample_row['type'],
                            'price': sample_row['latest_price']
                        })

                method_dict['sample_prices'] = sample_prices
                methods.append(method_dict)

            return methods

    def _count_new_methods(self, cursor, prev_time: str, curr_time: str) -> int:
        """统计新增方法数量"""
        query = """
            SELECT COUNT(DISTINCT curr.check_method_id) as new_count
            FROM price_history curr
            LEFT JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                         AND prev.fetch_time = ?
            WHERE curr.fetch_time = ?
              AND prev.check_method_id IS NULL
        """
        cursor.execute(query, (prev_time, curr_time))
        result = cursor.fetchone()
        return result[0] if result else 0

    def _count_new_methods_with_ids(self, cursor, prev_time: str, curr_time: str) -> tuple:
        """统计新增方法数量并返回方法ID列表"""
        query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            LEFT JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                         AND prev.fetch_time = ?
            WHERE curr.fetch_time = ?
              AND prev.check_method_id IS NULL
        """
        cursor.execute(query, (prev_time, curr_time))
        method_ids = [row[0] for row in cursor.fetchall()]
        return len(method_ids), method_ids

    def _count_qualification_changes(self, cursor, prev_time: str, curr_time: str) -> tuple:
        """统计资质变更数量，返回(CMA变更数, CNAS变更数, NHC变更数)"""

        # CMA变更统计
        cma_query = """
            SELECT COUNT(DISTINCT curr.check_method_id) as cma_changes
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.cma_status != prev.cma_status
        """
        cursor.execute(cma_query, (curr_time, prev_time))
        cma_count = cursor.fetchone()[0] or 0

        # CNAS变更统计
        cnas_query = """
            SELECT COUNT(DISTINCT curr.check_method_id) as cnas_changes
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.cnas_status != prev.cnas_status
        """
        cursor.execute(cnas_query, (curr_time, prev_time))
        cnas_count = cursor.fetchone()[0] or 0

        # NHC变更统计
        nhc_query = """
            SELECT COUNT(DISTINCT curr.check_method_id) as nhc_changes
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.gov_agree_status != prev.gov_agree_status
        """
        cursor.execute(nhc_query, (curr_time, prev_time))
        nhc_count = cursor.fetchone()[0] or 0

        return cma_count, cnas_count, nhc_count

    def _count_qualification_changes_with_ids(self, cursor, prev_time: str, curr_time: str) -> tuple:
        """统计资质变更数量并返回方法ID列表，返回(CMA变更数, CNAS变更数, NHC变更数, CMA_IDs, CNAS_IDs, NHC_IDs)"""

        # CMA变更统计
        cma_query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.cma_status != prev.cma_status
        """
        cursor.execute(cma_query, (curr_time, prev_time))
        cma_ids = [row[0] for row in cursor.fetchall()]

        # CNAS变更统计
        cnas_query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.cnas_status != prev.cnas_status
        """
        cursor.execute(cnas_query, (curr_time, prev_time))
        cnas_ids = [row[0] for row in cursor.fetchall()]

        # NHC变更统计
        nhc_query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.gov_agree_status != prev.gov_agree_status
        """
        cursor.execute(nhc_query, (curr_time, prev_time))
        nhc_ids = [row[0] for row in cursor.fetchall()]

        return len(cma_ids), len(cnas_ids), len(nhc_ids), cma_ids, cnas_ids, nhc_ids

    def _count_price_changes(self, cursor, prev_time: str, curr_time: str) -> int:
        """统计价格变更数量（只统计已存在方法的价格变化，排除新增方法）"""
        # 调用详细版本的方法，只返回数量
        count, _ = self._count_price_changes_with_ids(cursor, prev_time, curr_time)
        return count

    def _count_price_changes_with_ids(self, cursor, prev_time: str, curr_time: str) -> tuple:
        """统计价格变更数量并返回方法ID列表

        修正的逻辑：
        1. 只统计已存在方法的价格变化，排除新增方法
        2. 同一个方法ID无论多少价格类型变动，只计算1个
        3. 统计所有类型的价格变化：分析价格、最低价格、采样价格变化、采样类型新增/删除
        """
        # 首先获取在前一时间点就存在的方法ID（排除新增方法）
        existing_methods_query = """
            SELECT DISTINCT check_method_id
            FROM price_history
            WHERE fetch_time = ?
        """
        cursor.execute(existing_methods_query, (prev_time,))
        existing_method_ids = set(row[0] for row in cursor.fetchall())

        if not existing_method_ids:
            return 0, []

        # 使用一个集合来收集所有有价格变化的方法ID，自动去重
        changed_method_ids = set()

        # 1. 方法级别价格变化（分析价格、最低价格）
        method_level_query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.check_method_id IN ({})
              AND (
                  curr.check_method_price != prev.check_method_price
                  OR curr.lowest_bid != prev.lowest_bid
              )
        """.format(','.join('?' * len(existing_method_ids)))

        params = [curr_time, prev_time] + list(existing_method_ids)
        cursor.execute(method_level_query, params)
        changed_method_ids.update(row[0] for row in cursor.fetchall())

        # 2. 采样价格变化（相同采样类型的价格变化）
        sample_price_query = """
            SELECT DISTINCT curr.check_method_id
            FROM price_history curr
            INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                         AND curr.sample_type_id = prev.sample_type_id
            WHERE curr.fetch_time = ?
              AND prev.fetch_time = ?
              AND curr.check_method_id IN ({})
              AND curr.sample_type_id IS NOT NULL
              AND curr.sample_type_price != prev.sample_type_price
        """.format(','.join('?' * len(existing_method_ids)))

        cursor.execute(sample_price_query, params)
        changed_method_ids.update(row[0] for row in cursor.fetchall())

        # 3. 采样类型的新增和删除（只针对已存在的方法）
        sample_type_changes_query = """
            SELECT DISTINCT method_id FROM (
                -- 当前时间点的采样类型
                SELECT check_method_id as method_id, sample_type_id
                FROM price_history
                WHERE fetch_time = ?
                  AND sample_type_id IS NOT NULL
                  AND check_method_id IN ({})

                EXCEPT

                -- 前一时间点的采样类型
                SELECT check_method_id as method_id, sample_type_id
                FROM price_history
                WHERE fetch_time = ?
                  AND sample_type_id IS NOT NULL
                  AND check_method_id IN ({})
            )
        """.format(','.join('?' * len(existing_method_ids)), ','.join('?' * len(existing_method_ids)))

        params_sample_types = [curr_time] + list(existing_method_ids) + [prev_time] + list(existing_method_ids)
        cursor.execute(sample_type_changes_query, params_sample_types)
        changed_method_ids.update(row[0] for row in cursor.fetchall())

        # 转换为列表并返回（集合自动确保了去重）
        all_changed_method_ids = list(changed_method_ids)

        return len(all_changed_method_ids), all_changed_method_ids

    def get_methods_by_ids(self, method_ids: List[int]) -> List[Dict]:
        """根据方法ID列表获取方法详情"""
        if not method_ids:
            return []

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 构建IN查询的占位符
            placeholders = ','.join(['?' for _ in method_ids])

            query = f"""
                SELECT
                    cm.id,
                    cm.method_no,
                    cm.name as method_name,
                    cm.price,
                    cm.lowest_bid,
                    cm.analysis_cost,
                    cm.cma,
                    cm.cnas,
                    cm.gov_agree,
                    cm.status,
                    cm.last_date,
                    si.id as service_item_id,
                    si.name as service_item_name,
                    si.status as project_status,
                    COALESCE(si.service_category_name, '') as category
                FROM check_methods cm
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE cm.id IN ({placeholders})
                ORDER BY cm.id
            """

            cursor.execute(query, method_ids)
            rows = cursor.fetchall()

            methods = []
            for row in rows:
                method_dict = {
                    'id': row[0],
                    'method_no': row[1],
                    'method_name': row[2],
                    'price': row[3],
                    'lowest_bid': row[4],
                    'analysis_cost': row[5],
                    'cma': row[6],
                    'cnas': row[7],
                    'gov_agree': row[8],
                    'status': row[9],
                    'last_date': row[10],
                    'service_item_id': row[11],
                    'service_item_name': row[12],
                    'project_status': row[13],
                    'category': row[14]
                }
                methods.append(method_dict)

            return methods

    def get_statistics(self, sort_by='item_count', page=1, per_page=6) -> Dict:
        """获取数据统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            stats = {}

            # 总项目数
            cursor.execute("SELECT COUNT(*) as count FROM service_items")
            stats['total_items'] = cursor.fetchone()['count']

            # 总方法数
            cursor.execute("SELECT COUNT(*) as count FROM check_methods")
            stats['total_methods'] = cursor.fetchone()['count']

            # 价格记录数
            cursor.execute("SELECT COUNT(*) as count FROM price_history")
            stats['total_price_records'] = cursor.fetchone()['count']
            
            # 分类统计
            stats['categories_data'] = self.get_categories_statistics(sort_by=sort_by, page=page, per_page=per_page)
            stats['categories'] = stats['categories_data']['categories']

            # 最新抓取时间
            cursor.execute("""
                SELECT MAX(fetch_time) as last_fetch 
                FROM price_history
            """)
            last_fetch = cursor.fetchone()['last_fetch']
            stats['last_fetch'] = last_fetch
            
            # 近期价格变动统计
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM price_history 
                WHERE fetch_time >= datetime('now', '-7 days')
            """)
            stats['recent_updates'] = cursor.fetchone()['count']
            
            # 锁定项目数
            cursor.execute("SELECT COUNT(*) as count FROM service_items WHERE status = 1")
            stats['locked_items'] = cursor.fetchone()['count']
            
            # 锁定方法数
            cursor.execute("SELECT COUNT(*) as count FROM check_methods WHERE status = 1")
            stats['locked_methods'] = cursor.fetchone()['count']

            # 计算比例
            if stats['total_items'] > 0:
                stats['locked_items_ratio'] = (stats['locked_items'] / stats['total_items']) * 100
            else:
                stats['locked_items_ratio'] = 0
            
            if stats['total_methods'] > 0:
                stats['locked_methods_ratio'] = (stats['locked_methods'] / stats['total_methods']) * 100
            else:
                stats['locked_methods_ratio'] = 0
            
            return stats

    def get_categories_statistics(self, sort_by: str = 'item_count', page: int = 1, per_page: int = 6) -> Dict:
        """获取分类统计信息，支持排序和分页"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 基础查询
            query = """
                SELECT
                    si.service_category_name,
                    COUNT(DISTINCT si.id) as item_count,
                    SUM(CASE WHEN si.status = 1 THEN 1 ELSE 0 END) as locked_item_count,
                    COUNT(cm.id) as method_count,
                    SUM(CASE WHEN cm.status = 1 THEN 1 ELSE 0 END) as locked_method_count,
                    SUM(CASE WHEN cm.status = 0 THEN 1 ELSE 0 END) as normal_method_count
                FROM service_items si
                LEFT JOIN check_methods cm ON si.id = cm.service_item_id
                WHERE si.service_category_name IS NOT NULL
                GROUP BY si.service_category_name
            """
            
            cursor.execute(query)
            all_categories = [dict(row) for row in cursor.fetchall()]
            
            # 在Python中处理资质统计和排序
            for category in all_categories:
                category_name = category['service_category_name']

                # 预计算项目统计百分比
                if category['item_count'] > 0:
                    unlocked_items = category['item_count'] - category['locked_item_count']
                    category['unlocked_item_percentage'] = round((unlocked_items / category['item_count']) * 100, 1) if category['item_count'] > 0 else 0
                    category['locked_item_percentage'] = round((category['locked_item_count'] / category['item_count']) * 100, 1) if category['item_count'] > 0 else 0
                else:
                    category['unlocked_item_percentage'] = 0
                    category['locked_item_percentage'] = 0

                # 预计算方法统计百分比
                if category['method_count'] > 0:
                    category['normal_method_percentage'] = round((category['normal_method_count'] / category['method_count']) * 100, 1) if category['method_count'] > 0 else 0
                    category['locked_method_percentage'] = round((category['locked_method_count'] / category['method_count']) * 100, 1) if category['method_count'] > 0 else 0
                else:
                    category['normal_method_percentage'] = 0
                    category['locked_method_percentage'] = 0
                
                cursor.execute("""
                    SELECT DISTINCT
                        si.id,
                        si.name,
                        si.child_item,
                        CASE WHEN EXISTS(
                            SELECT 1 FROM check_methods cm
                            WHERE cm.service_item_id = si.id AND cm.cma = 1
                        ) THEN 1 ELSE 0 END as has_cma,
                        CASE WHEN EXISTS(
                            SELECT 1 FROM check_methods cm
                            WHERE cm.service_item_id = si.id AND cm.cnas = 1
                        ) THEN 1 ELSE 0 END as has_cnas,
                        CASE WHEN EXISTS(
                            SELECT 1 FROM check_methods cm
                            WHERE cm.service_item_id = si.id AND cm.gov_agree = 1
                        ) THEN 1 ELSE 0 END as has_nhc
                    FROM service_items si
                    WHERE si.service_category_name = ?
                """, (category_name,))
                
                items = cursor.fetchall()
                cma_names, cnas_names, nhc_names = set(), set(), set()
                
                for item in items:
                    project_name = item['name'] or ''
                    child_item = item['child_item'] or ''
                    
                    def add_names_to_set(names_set, has_qual, child_item, project_name):
                        if has_qual:
                            if child_item.strip():
                                sub_projects = [sub.strip() for sub in child_item.split('、') if sub.strip()]
                                for sub_name in sub_projects:
                                    names_set.add(sub_name)
                            elif project_name.strip():
                                names_set.add(project_name.strip())

                    add_names_to_set(cma_names, item['has_cma'], child_item, project_name)
                    add_names_to_set(cnas_names, item['has_cnas'], child_item, project_name)
                    add_names_to_set(nhc_names, item['has_nhc'], child_item, project_name)

                category['cma_count'] = len(cma_names)
                category['cnas_count'] = len(cnas_names)
                category['nhc_count'] = len(nhc_names)
                category['qualification_count'] = category['cma_count'] + category['cnas_count'] + category['nhc_count']
            
            # 排序
            if sort_by == 'name':
                all_categories.sort(key=lambda x: x['service_category_name'])
            elif sort_by == 'method_count':
                all_categories.sort(key=lambda x: x['method_count'], reverse=True)
            elif sort_by == 'qualification_count':
                all_categories.sort(key=lambda x: x['qualification_count'], reverse=True)
            else: # 默认按项目数
                all_categories.sort(key=lambda x: x['item_count'], reverse=True)

            total_categories = len(all_categories)
            total_pages = (total_categories + per_page - 1) // per_page
            
            start = (page - 1) * per_page
            end = start + per_page
            paginated_categories = all_categories[start:end]

            return {
                "categories": paginated_categories,
                "total_categories": total_categories,
                "total_pages": total_pages,
                "current_page": page,
                "per_page": per_page
            }

    def get_categories_chart_data(self) -> Dict:
        """获取分类图表数据，包含总方法数量和可用方法数量"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 查询每个分类的总方法数量和可用方法数量
            query = """
                SELECT
                    si.service_category_name,
                    COUNT(cm.id) as total_methods,
                    SUM(CASE WHEN si.status = 0 AND cm.status = 0 THEN 1 ELSE 0 END) as available_methods
                FROM service_items si
                LEFT JOIN check_methods cm ON si.id = cm.service_item_id
                WHERE si.service_category_name IS NOT NULL
                AND cm.deprecated = 0
                GROUP BY si.service_category_name
                HAVING COUNT(cm.id) > 0
                ORDER BY total_methods DESC
            """

            cursor.execute(query)
            categories = [dict(row) for row in cursor.fetchall()]

            return {
                "success": True,
                "categories": categories
            }

    def export_to_csv(self, filepath: str, item_ids: List[int] = None) -> bool:
        """导出数据到CSV文件"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                where_clause = ""
                params = []
                
                if item_ids:
                    placeholders = ','.join(['?' for _ in item_ids])
                    where_clause = f"WHERE service_item_id IN ({placeholders})"
                    params = item_ids
                
                query = f"""
                    SELECT * FROM price_trend_view 
                    {where_clause}
                    ORDER BY service_item_name, check_method_name, fetch_time
                """
                
                cursor.execute(query, params)
                
                # 写入CSV文件
                with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    if cursor.description:
                        fieldnames = [desc[0] for desc in cursor.description]
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        
                        for row in cursor.fetchall():
                            writer.writerow(dict(row))
                
                print(f"数据已导出到: {filepath}")
                return True
                
        except Exception as e:
            print(f"导出CSV时出错: {e}")
            return False
    
    def export_to_excel(self, filepath: str, item_ids: List[int] = None) -> bool:
        """导出数据到Excel文件"""
        try:
            with self.get_connection() as conn:
                where_clause = ""
                params = []
                
                if item_ids:
                    placeholders = ','.join(['?' for _ in item_ids])
                    where_clause = f"WHERE service_item_id IN ({placeholders})"
                    params = item_ids
                
                query = f"""
                    SELECT * FROM price_trend_view 
                    {where_clause}
                    ORDER BY service_item_name, check_method_name, fetch_time
                """
                
                # 使用pandas导出Excel
                df = pd.read_sql_query(query, conn, params=params)
                
                with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='价格数据', index=False)
                    
                    # 如果有多个分类，创建分类工作表
                    categories = df['service_category_name'].unique()
                    if len(categories) > 1:
                        for category in categories:
                            if pd.notna(category):
                                category_data = df[df['service_category_name'] == category]
                                sheet_name = category[:30]  # Excel工作表名称限制
                                category_data.to_excel(writer, sheet_name=sheet_name, index=False)
                
                print(f"数据已导出到: {filepath}")
                return True
                
        except Exception as e:
            print(f"导出Excel时出错: {e}")
            return False
    
    def import_from_json(self, filepath: str) -> bool:
        """从JSON文件导入数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 假设JSON格式与API返回格式一致
            with self.get_connection() as conn:
                fetch_time = datetime.now()
                
                if isinstance(data, list):
                    items_data = data
                elif isinstance(data, dict) and 'ret' in data and 'rows' in data['ret']:
                    items_data = data['ret']['rows']
                else:
                    print("不支持的JSON格式")
                    return False
                
                for item in items_data:
                    self._save_item_data(conn, item, fetch_time)
                
                conn.commit()
                print(f"已从 {filepath} 导入 {len(items_data)} 个项目的数据")
                return True
                
        except Exception as e:
            print(f"导入JSON时出错: {e}")
            return False
    
    def _save_item_data(self, conn, item_data, fetch_time):
        """保存单个项目数据到数据库（内部方法）"""
        cursor = conn.cursor()
        
        # 这里重用price_fetcher.py中的保存逻辑
        # 为了避免重复代码，可以将保存逻辑抽取到公共模块
        pass
    
    def cleanup_old_data(self, days: int = 90) -> int:
        """清理指定天数之前的历史数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除旧的价格历史记录
                cursor.execute("""
                    DELETE FROM price_history 
                    WHERE fetch_time < datetime('now', '-{} days')
                """.format(days))
                
                deleted_count = cursor.rowcount
                
                # 删除旧的抓取日志
                cursor.execute("""
                    DELETE FROM fetch_logs 
                    WHERE fetch_time < datetime('now', '-{} days')
                """.format(days))
                
                conn.commit()
                print(f"已清理 {deleted_count} 条 {days} 天前的历史数据")
                return deleted_count
                
        except Exception as e:
            print(f"清理数据时出错: {e}")
            return 0
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"数据库已备份到: {backup_path}")
            return True
        except Exception as e:
            print(f"备份数据库时出错: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            import shutil
            shutil.copy2(backup_path, self.db_path)
            print(f"数据库已从 {backup_path} 恢复")
            return True
        except Exception as e:
            print(f"恢复数据库时出错: {e}")
            return False
    
    def get_fetch_logs(self, days: int = 7) -> List[Dict]:
        """获取抓取日志"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM fetch_logs 
                WHERE fetch_time >= datetime('now', '-{} days')
                ORDER BY fetch_time DESC
            """.format(days))
            
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))
            
            return results

    def get_method_sample_prices_with_history(self, method_id: int, include_deleted: bool = True) -> Dict:
        """获取方法的采样价格，包括已删除的采样类型
        Args:
            method_id: 方法ID
            include_deleted: 是否包含已删除的采样类型
        Returns:
            Dict: 包含当前和历史采样价格信息
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取该方法的所有历史采样类型（去重）
            cursor.execute("""
                SELECT DISTINCT st.type
                FROM price_history ph
                LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                WHERE ph.check_method_id = ? AND st.type IS NOT NULL
                ORDER BY st.type
            """, (method_id,))
            
            all_historical_types = [row['type'] for row in cursor.fetchall()]
            
            # 获取最新抓取时间
            cursor.execute("""
                SELECT MAX(fetch_time) as latest_fetch_time
                FROM price_history
                WHERE check_method_id = ?
            """, (method_id,))
            
            latest_fetch_result = cursor.fetchone()
            latest_fetch_time = latest_fetch_result['latest_fetch_time'] if latest_fetch_result else None
            
            current_sample_types = []
            deleted_sample_types = []
            
            if latest_fetch_time:
                # 获取最新抓取时间存在的采样类型
                cursor.execute("""
                    SELECT DISTINCT st.type, ph.sample_type_price as latest_price
                    FROM price_history ph
                    LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                    WHERE ph.check_method_id = ?
                    AND ph.fetch_time = ?
                    AND st.type IS NOT NULL
                    AND ph.sample_type_price IS NOT NULL
                    ORDER BY st.type
                """, (method_id, latest_fetch_time))
                
                current_types_data = {row['type']: row['latest_price'] for row in cursor.fetchall()}
                
                # 分类当前存在和已删除的采样类型
                for hist_type in all_historical_types:
                    if hist_type in current_types_data:
                        # 获取该采样类型的首次价格
                        cursor.execute("""
                            SELECT sample_type_price as initial_price
                            FROM price_history
                            WHERE check_method_id = ? AND sample_type_id = (
                                SELECT id FROM sample_types
                                WHERE check_method_id = ? AND type = ?
                            )
                            ORDER BY fetch_time ASC
                            LIMIT 1
                        """, (method_id, method_id, hist_type))
                        
                        initial_sample_row = cursor.fetchone()
                        initial_price = initial_sample_row['initial_price'] if initial_sample_row else 0
                        
                        current_sample_types.append({
                            'type': hist_type,
                            'price': current_types_data[hist_type] or 0,
                            'initial_price': initial_price or 0,
                            'status': 'active'
                        })
                    else:
                        # 采样类型被删除，获取最后一次的价格
                        cursor.execute("""
                            SELECT ph.sample_type_price as last_price, ph.fetch_time as last_seen
                            FROM price_history ph
                            LEFT JOIN sample_types st ON ph.sample_type_id = st.id
                            WHERE ph.check_method_id = ? AND st.type = ?
                            AND ph.sample_type_price IS NOT NULL
                            ORDER BY ph.fetch_time DESC
                            LIMIT 1
                        """, (method_id, hist_type))
                        
                        last_record = cursor.fetchone()
                        if last_record and include_deleted:
                            deleted_sample_types.append({
                                'type': hist_type,
                                'last_price': last_record['last_price'] or 0,
                                'last_seen': last_record['last_seen'],
                                'status': 'deleted'
                            })
            
            return {
                'current_sample_types': current_sample_types,
                'deleted_sample_types': deleted_sample_types,
                'latest_fetch_time': latest_fetch_time
            }

    def analyze_method_by_name(self, method_name: str) -> Dict:
        """根据方法名称分析方法数据，智能处理各种匹配问题"""
        import re
        
        def clean_text(text):
            """清理文本：处理不可见字符和编码问题"""
            if not text:
                return ''
            
            # 移除各种不可见字符和控制字符
            text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)  # 移除控制字符
            text = re.sub(r'[\ufeff\u200b\u200c\u200d\u2060]', '', text)  # 移除零宽字符
            text = re.sub(r'\s+', ' ', text).strip()  # 标准化空格
            
            # 全角转半角的映射
            full_to_half = {
                '（': '(', '）': ')', '［': '[', '］': ']', '｛': '{', '｝': '}',
                '－': '-', '＿': '_', '＋': '+', '＝': '=', '｜': '|',
                '：': ':', '；': ';', '，': ',', '。': '.', '？': '?', '！': '!',
                '／': '/', '＼': '\\', '＊': '*', '＆': '&', '％': '%',
                '＃': '#', '＄': '$', '＠': '@', '～': '~', '｀': '`',
                '"': '"', '"': '"', ''': "'", ''': "'", '…': '...',
                '〈': '<', '〉': '>', '《': '<', '》': '>'
            }
            
            # 替换全角符号为半角
            for full, half in full_to_half.items():
                text = text.replace(full, half)
            
            return text
        
        cleaned_input = clean_text(method_name.strip())
        
        if not cleaned_input:
            return {
                'success': False,
                'message': '方法名称不能为空',
                'data': {}
            }
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 使用分阶段匹配策略，从精确到模糊
                query = """
                    SELECT DISTINCT
                        cm.id as method_id,
                        cm.method_no,
                        cm.name as method_name,
                        cm.price as analysis_price,
                        cm.lowest_bid as lowest_price,
                        cm.cma,
                        cm.cnas,
                        cm.gov_agree,
                        cm.status as method_status,
                        si.id as service_item_id,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        si.child_item,
                        si.service_category_name,
                        si.second_category_name,
                        si.status as item_status
                    FROM check_methods cm
                    LEFT JOIN service_items si ON cm.service_item_id = si.id
                    WHERE 1=1
                """
                
                conditions = []
                params = []
                
                # 第1阶段：尝试完全精确匹配（原文）
                conditions.append("cm.name = ?")
                params.append(method_name.strip())
                
                # 第2阶段：清理后的精确匹配
                if cleaned_input != method_name.strip():
                    conditions.append("cm.name = ?")
                    params.append(cleaned_input)
                
                # 第3阶段：标准化空格后的精确匹配
                normalized_space_original = re.sub(r'\\s+', ' ', method_name.strip())
                conditions.append("REPLACE(REPLACE(cm.name, '  ', ' '), '   ', ' ') = ?")
                params.append(normalized_space_original)
                
                normalized_space_cleaned = re.sub(r'\\s+', ' ', cleaned_input)
                if normalized_space_cleaned != normalized_space_original:
                    conditions.append("REPLACE(REPLACE(cm.name, '  ', ' '), '   ', ' ') = ?")
                    params.append(normalized_space_cleaned)
                
                # 第4阶段：去除所有空格的精确匹配
                no_space_original = re.sub(r'\\s+', '', method_name.strip())
                conditions.append("REPLACE(cm.name, ' ', '') = ?")
                params.append(no_space_original)
                
                no_space_cleaned = re.sub(r'\\s+', '', cleaned_input)
                if no_space_cleaned != no_space_original:
                    conditions.append("REPLACE(cm.name, ' ', '') = ?")
                    params.append(no_space_cleaned)
                
                # 第5阶段：模糊匹配（包含）
                conditions.append("cm.name LIKE ?")
                params.append(f"%{cleaned_input}%")
                
                # 第6阶段：去空格的模糊匹配
                conditions.append("REPLACE(cm.name, ' ', '') LIKE ?")
                params.append(f"%{no_space_cleaned}%")
                
                # 组合查询条件
                query += " AND (" + " OR ".join(conditions) + ")"
                query += " ORDER BY " + \
                         "CASE " + \
                         "  WHEN cm.name = ? THEN 1 " + \
                         "  WHEN cm.name = ? THEN 2 " + \
                         "  WHEN REPLACE(REPLACE(cm.name, '  ', ' '), '   ', ' ') = ? THEN 3 " + \
                         "  WHEN REPLACE(REPLACE(cm.name, '  ', ' '), '   ', ' ') = ? THEN 4 " + \
                         "  WHEN REPLACE(cm.name, ' ', '') = ? THEN 5 " + \
                         "  WHEN REPLACE(cm.name, ' ', '') = ? THEN 6 " + \
                         "  ELSE 7 " + \
                         "END, " + \
                         "si.service_category_name, si.second_category_name, si.name"
                
                # 为排序添加参数
                sort_params = [method_name.strip(), cleaned_input, 
                              normalized_space_original, normalized_space_cleaned,
                              no_space_original, no_space_cleaned]
                all_params = params + sort_params
                
                cursor.execute(query, all_params)
                
                methods = []
                for row in cursor.fetchall():
                    method_dict = dict(row)
                    
                    # 计算项目数量（根据子项目判断）
                    child_item = method_dict.get('child_item', '') or ''
                    if child_item.strip():
                        # 有子项目，按逗号分隔计算数量
                        project_count = len([item.strip() for item in child_item.split('、') if item.strip()])
                    else:
                        # 没有子项目，数量为1
                        project_count = 1
                    
                    method_dict['project_count'] = project_count
                    
                    # 处理价格显示
                    analysis_price = method_dict.get('analysis_price') or 0
                    lowest_price = method_dict.get('lowest_price') or analysis_price
                    
                    method_dict['analysis_price_display'] = analysis_price if analysis_price else 0
                    method_dict['lowest_price_display'] = lowest_price if lowest_price else analysis_price
                    
                    # 获取采样价格信息（包括已删除的）
                    sample_data = self.get_method_sample_prices_with_history(method_dict['method_id'], include_deleted=True)
                    method_dict['sample_types'] = sample_data['current_sample_types']
                    method_dict['deleted_sample_types'] = sample_data['deleted_sample_types']

                    # 获取首次价格信息（用于价格变动对比）
                    # 获取最早的价格记录
                    cursor.execute("""
                        SELECT check_method_price as initial_analysis_price
                        FROM price_history
                        WHERE check_method_id = ? AND check_method_price IS NOT NULL
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_dict['method_id'],))

                    initial_analysis_row = cursor.fetchone()
                    if initial_analysis_row:
                        method_dict['initial_analysis_price'] = initial_analysis_row['initial_analysis_price'] or 0
                    else:
                        # 如果没有历史记录，使用当前价格作为初始价格
                        method_dict['initial_analysis_price'] = method_dict.get('analysis_price') or 0

                    # 对于最低价格，由于数据库结构限制，我们暂时使用当前价格作为初始价格
                    # 在实际应用中，可以考虑改进数据库结构来存储最低价格的历史记录
                    initial_lowest_price = method_dict.get('lowest_price') or 0
                    initial_analysis_price = method_dict.get('initial_analysis_price') or 0

                    # 如果最低价格为0，使用分析价格
                    if initial_lowest_price == 0 and initial_analysis_price > 0:
                        initial_lowest_price = initial_analysis_price

                    method_dict['initial_lowest_price'] = initial_lowest_price

                    # 获取采样价格的首次价格
                    for sample_type in method_dict['sample_types']:
                        cursor.execute("""
                            SELECT sample_type_price as initial_price
                            FROM price_history
                            WHERE check_method_id = ? AND sample_type_id = (
                                SELECT id FROM sample_types
                                WHERE check_method_id = ? AND type = ?
                            )
                            ORDER BY fetch_time ASC
                            LIMIT 1
                        """, (method_dict['method_id'], method_dict['method_id'], sample_type['type']))

                        initial_sample_row = cursor.fetchone()
                        if initial_sample_row:
                            sample_type['initial_price'] = initial_sample_row['initial_price'] or 0
                        else:
                            sample_type['initial_price'] = sample_type.get('price', 0)

                    # 添加价格变动分析（复用控制台页面的逻辑）
                    method_id = method_dict['method_id']

                    # 分析价格变动
                    recent_analysis_change = self._get_recent_analysis_price_change(cursor, method_id, 7)
                    method_dict['analysis_price_change'] = recent_analysis_change

                    # 最低价格变动
                    recent_lowest_change = self._get_recent_lowest_price_change(cursor, method_id, 7)
                    method_dict['lowest_price_change'] = recent_lowest_change

                    # 采样价格变动
                    recent_sample_changes = self._get_recent_sample_price_changes(cursor, method_id, 7)
                    method_dict['sample_price_changes'] = recent_sample_changes

                    methods.append(method_dict)
                
                if not methods:
                    return {
                        'success': False,
                        'message': f'未找到匹配的方法: {method_name}',
                        'data': {}
                    }
                
                # 按分类分组统计
                category_groups = {}
                for method in methods:
                    category_key = method.get('service_category_name') or '未分类'
                    second_category = method.get('second_category_name') or ''
                    
                    if category_key not in category_groups:
                        category_groups[category_key] = {
                            'primary_category': category_key,
                            'methods': []
                        }
                    
                    category_groups[category_key]['methods'].append(method)
                
                # 计算总体统计
                total_methods = len(methods)
                total_projects = sum(method['project_count'] for method in methods)
                categories_count = len(category_groups)
                
                return {
                    'success': True,
                    'message': f'找到 {total_methods} 个匹配的方法',
                    'data': {
                        'search_method_name': method_name,
                        'normalized_name': cleaned_input,
                        'total_methods': total_methods,
                        'total_projects': total_projects,
                        'categories_count': categories_count,
                        'category_groups': category_groups,
                        'methods': methods
                    }
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'查询方法分析数据时出错: {str(e)}',
                'data': {}
            }

    def update_method_status(self, method_id: int, status: int) -> Dict:
        """更新方法废止状态
        Args:
            method_id: 方法ID
            status: 废止状态值 (0=有效, 1=废止)
        Returns:
            Dict: 操作结果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 首先检查方法是否存在
                cursor.execute("SELECT id, name, deprecated FROM check_methods WHERE id = ?", (method_id,))
                method = cursor.fetchone()

                if not method:
                    return {
                        'success': False,
                        'message': f'方法ID {method_id} 不存在'
                    }

                old_status = method['deprecated']
                method_name = method['name']

                # 更新废止状态
                cursor.execute("""
                    UPDATE check_methods
                    SET deprecated = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (status, method_id))

                conn.commit()

                status_text = "有效" if status == 0 else "废止"
                old_status_text = "有效" if old_status == 0 else "废止"

                return {
                    'success': True,
                    'message': f'方法 "{method_name}" 状态已从 {old_status_text} 更新为 {status_text}',
                    'data': {
                        'method_id': method_id,
                        'method_name': method_name,
                        'old_status': old_status,
                        'new_status': status
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'更新方法状态时出错: {str(e)}'
            }

    def batch_update_method_status(self, method_ids: List[int], status: int) -> Dict:
        """批量更新方法废止状态
        Args:
            method_ids: 方法ID列表
            status: 废止状态值 (0=有效, 1=废止)
        Returns:
            Dict: 操作结果
        """
        if not method_ids:
            return {
                'success': False,
                'message': '方法ID列表不能为空'
            }

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 首先检查所有方法是否存在
                placeholders = ','.join(['?' for _ in method_ids])
                cursor.execute(f"""
                    SELECT id, name, deprecated FROM check_methods
                    WHERE id IN ({placeholders})
                """, method_ids)

                existing_methods = cursor.fetchall()
                existing_ids = [method['id'] for method in existing_methods]

                # 检查是否有不存在的方法ID
                missing_ids = [mid for mid in method_ids if mid not in existing_ids]
                if missing_ids:
                    return {
                        'success': False,
                        'message': f'以下方法ID不存在: {missing_ids}'
                    }

                # 统计状态变更
                status_changes = {
                    'updated': 0,
                    'unchanged': 0,
                    'methods': []
                }

                for method in existing_methods:
                    if method['deprecated'] != status:
                        status_changes['updated'] += 1
                    else:
                        status_changes['unchanged'] += 1

                    status_changes['methods'].append({
                        'id': method['id'],
                        'name': method['name'],
                        'old_status': method['deprecated'],
                        'new_status': status
                    })

                # 批量更新废止状态
                if status_changes['updated'] > 0:
                    cursor.execute(f"""
                        UPDATE check_methods
                        SET deprecated = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id IN ({placeholders})
                    """, [status] + method_ids)

                    conn.commit()

                status_text = "有效" if status == 0 else "废止"

                return {
                    'success': True,
                    'message': f'批量操作完成：{status_changes["updated"]} 个方法状态已更新为 {status_text}，{status_changes["unchanged"]} 个方法状态无变化',
                    'data': {
                        'total_methods': len(method_ids),
                        'updated_count': status_changes['updated'],
                        'unchanged_count': status_changes['unchanged'],
                        'target_status': status,
                        'methods': status_changes['methods']
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'批量更新方法状态时出错: {str(e)}'
            }

    def get_methods_by_status(self, status: int = None, keyword: str = "", project_number: str = "",
                             category: str = "", method_name: str = "", method_name2: str = "",
                             cma: str = "", cnas: str = "", nhc: str = "", availability: str = "",
                             date_filter: str = "", change_filter: str = "", change_date: str = "",
                             method_ids: str = "", sort_order: str = "last_date_desc",
                             limit: int = 100, offset: int = 0) -> Dict:
        """根据状态获取方法列表
        Args:
            status: 方法废止状态 (0=有效, 1=废止, None=全部)
            keyword: 项目名称或子项目内容关键词
            project_number: 项目编号关键词
            category: 分类筛选
            method_name: 方法名称筛选1
            method_name2: 方法名称筛选2 (与method_name使用AND逻辑)
            cma: CMA资质筛选 ("1"=有CMA, "0"=无CMA, ""=全部)
            cnas: CNAS资质筛选 ("1"=有CNAS, "0"=无CNAS, ""=全部)
            nhc: NHC资质筛选 ("1"=有NHC, "0"=无NHC, ""=全部)
            availability: 方法可用性筛选 ("available"=可用, "unavailable"=不可用, ""=全部)
            date_filter: 日期筛选 (YYYY-MM-DD格式，筛选指定日期变动的方法)
            change_filter: 变动类型筛选 ("new_methods"=新增方法, "qualification_changes"=资质变更, "price_changes"=价格变更)
            change_date: 变动日期 (配合change_filter使用)
            sort_order: 排序方式 ("last_date_desc"=最后变动时间降序, "last_date_asc"=最后变动时间升序, "method_name"=方法名称, "default"=默认)
            limit: 每页数量
            offset: 偏移量
        Returns:
            Dict: 查询结果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                where_conditions = []
                params = []

                # 方法ID列表筛选（优先级最高，如果指定了method_ids则忽略其他筛选条件）
                if method_ids:
                    try:
                        # 解析方法ID列表
                        id_list = [int(id.strip()) for id in method_ids.split(',') if id.strip()]
                        if id_list:
                            # 构建IN查询
                            placeholders = ','.join(['?' for _ in id_list])
                            where_conditions.append(f"cm.id IN ({placeholders})")
                            params.extend(id_list)
                        else:
                            # 如果ID列表为空，添加一个永远为假的条件
                            where_conditions.append("1 = 0")
                    except ValueError:
                        # 如果ID格式不正确，添加一个永远为假的条件
                        where_conditions.append("1 = 0")

                # 如果没有指定方法ID列表，则使用其他筛选条件
                elif not method_ids:
                    # 方法废止状态筛选
                    if status is not None:
                        where_conditions.append("cm.deprecated = ?")
                        params.append(status)

                    # 项目编号筛选
                    if project_number:
                        where_conditions.append("si.number LIKE ?")
                        params.append(f"%{project_number}%")

                    # 项目名称或子项目内容筛选
                    if keyword:
                        # 如果没有提供project_number，为了向后兼容，keyword仍然搜索编号
                        if not project_number:
                            where_conditions.append("(si.name LIKE ? OR si.number LIKE ? OR si.child_item LIKE ?)")
                            params.extend([f"%{keyword}%", f"%{keyword}%", f"%{keyword}%"])
                        else:
                            # 如果提供了project_number，keyword只搜索项目名称和子项目内容
                            where_conditions.append("(si.name LIKE ? OR si.child_item LIKE ?)")
                            params.extend([f"%{keyword}%", f"%{keyword}%"])

                    # 分类筛选
                    if category:
                        where_conditions.append("si.service_category_name LIKE ?")
                        params.append(f"%{category}%")

                    # 方法名称筛选（支持两个方法名称的AND逻辑）
                    method_name_conditions = []

                    if method_name:
                        # 清理输入文本
                        import re
                        def clean_text(text):
                            text = re.sub(r'[\u00A0\u2000-\u200B\u2028\u2029\u3000]', ' ', text)
                            text = re.sub(r'\s+', ' ', text)
                            return text.strip()

                        cleaned_method_name = clean_text(method_name.strip())

                        method1_conditions = []
                        # 精确匹配
                        method1_conditions.append("cm.name = ?")
                        params.append(method_name.strip())

                        # 清理后精确匹配
                        if cleaned_method_name != method_name.strip():
                            method1_conditions.append("cm.name = ?")
                            params.append(cleaned_method_name)

                        # 模糊匹配
                        method1_conditions.append("cm.name LIKE ?")
                        params.append(f"%{cleaned_method_name}%")

                        method_name_conditions.append(f"({' OR '.join(method1_conditions)})")

                    if method_name2:
                        # 清理输入文本
                        import re
                        def clean_text(text):
                            text = re.sub(r'[\u00A0\u2000-\u200B\u2028\u2029\u3000]', ' ', text)
                            text = re.sub(r'\s+', ' ', text)
                            return text.strip()

                        cleaned_method_name2 = clean_text(method_name2.strip())

                        method2_conditions = []
                        # 精确匹配
                        method2_conditions.append("cm.name = ?")
                        params.append(method_name2.strip())

                        # 清理后精确匹配
                        if cleaned_method_name2 != method_name2.strip():
                            method2_conditions.append("cm.name = ?")
                            params.append(cleaned_method_name2)

                        # 模糊匹配
                        method2_conditions.append("cm.name LIKE ?")
                        params.append(f"%{cleaned_method_name2}%")

                        method_name_conditions.append(f"({' OR '.join(method2_conditions)})")

                    # 如果有方法名称筛选条件，使用AND逻辑连接
                    if method_name_conditions:
                        where_conditions.append(f"({' AND '.join(method_name_conditions)})")

                    # 资质筛选逻辑
                    cert_conditions = []
                    if cma:
                        cert_conditions.append("cm.cma = 1")
                    if cnas:
                        cert_conditions.append("cm.cnas = 1")
                    if nhc:
                        cert_conditions.append("cm.gov_agree = 1")

                    if cert_conditions:
                        # 如果有资质筛选条件，使用AND逻辑连接（必须同时满足所有勾选的资质）
                        where_conditions.append("(" + " AND ".join(cert_conditions) + ")")

                    # 可用性筛选逻辑（基于锁定状态，不是废止状态）
                    if availability == "available":
                        # 筛选可用的方法（项目和方法都未锁定）
                        where_conditions.append("si.status = 0 AND cm.status = 0")
                    elif availability == "unavailable":
                        # 筛选不可用的方法（项目或方法被锁定）
                        where_conditions.append("(si.status = 1 OR cm.status = 1)")

                    # 日期筛选逻辑
                    if date_filter:
                        where_conditions.append("DATE(cm.last_date) = DATE(?)")
                        params.append(date_filter)

                    # 变动类型筛选逻辑（精确筛选）
                    if change_filter and change_date:
                        # 首先获取指定时间段内的所有抓取时间点
                        if change_filter == 'new_methods':
                            # 新增方法：找到在指定时间段内被检测为新增的方法
                            where_conditions.append("""
                                cm.id IN (
                                    SELECT method_id FROM (
                                        SELECT
                                            curr.check_method_id as method_id,
                                            curr.fetch_time as curr_time,
                                            prev.fetch_time as prev_time
                                        FROM price_history curr
                                        LEFT JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                            AND prev.fetch_time = (
                                                SELECT MAX(fetch_time)
                                                FROM price_history p2
                                                WHERE p2.check_method_id = curr.check_method_id
                                                  AND p2.fetch_time < curr.fetch_time
                                            )
                                        WHERE prev.check_method_id IS NULL
                                    ) new_methods_detection
                                    WHERE DATE(curr_time) = DATE(?)
                                       OR (prev_time IS NULL AND DATE(curr_time) <= DATE(?))
                                )
                            """)
                            params.extend([change_date, change_date])

                        elif change_filter == 'qualification_changes':
                            # 资质变更：找到在指定时间段内被检测为资质变更的方法
                            where_conditions.append("""
                                cm.id IN (
                                    SELECT method_id FROM (
                                        SELECT
                                            curr.check_method_id as method_id,
                                            curr.fetch_time as curr_time
                                        FROM price_history curr
                                        INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                            AND prev.fetch_time = (
                                                SELECT MAX(fetch_time)
                                                FROM price_history p2
                                                WHERE p2.check_method_id = curr.check_method_id
                                                  AND p2.fetch_time < curr.fetch_time
                                            )
                                        WHERE (
                                            curr.cma_status != prev.cma_status
                                            OR curr.cnas_status != prev.cnas_status
                                            OR curr.gov_agree_status != prev.gov_agree_status
                                        )
                                    ) qualification_changes_detection
                                    WHERE DATE(curr_time) = DATE(?)
                                )
                            """)
                            params.append(change_date)

                        elif change_filter == 'price_changes':
                            # 价格变更：找到在指定时间段内被检测为价格变更的方法
                            where_conditions.append("""
                                cm.id IN (
                                    SELECT method_id FROM (
                                        SELECT
                                            curr.check_method_id as method_id,
                                            curr.fetch_time as curr_time
                                        FROM price_history curr
                                        INNER JOIN price_history prev ON curr.check_method_id = prev.check_method_id
                                            AND prev.sample_type_id = curr.sample_type_id
                                            AND prev.fetch_time = (
                                                SELECT MAX(fetch_time)
                                                FROM price_history p2
                                                WHERE p2.check_method_id = curr.check_method_id
                                                  AND p2.sample_type_id = curr.sample_type_id
                                                  AND p2.fetch_time < curr.fetch_time
                                            )
                                        WHERE (
                                            curr.check_method_price != prev.check_method_price
                                            OR curr.lowest_bid != prev.lowest_bid
                                            OR curr.sample_type_price != prev.sample_type_price
                                        )
                                    ) price_changes_detection
                                    WHERE DATE(curr_time) = DATE(?)
                                )
                            """)
                            params.append(change_date)

                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)

                # 计算总数
                count_query = f"""
                    SELECT COUNT(DISTINCT cm.id) as total
                    FROM check_methods cm
                    LEFT JOIN service_items si ON cm.service_item_id = si.id
                    {where_clause}
                """
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()['total']

                # 优先按last_date排序，如果last_date为null则按updated_at排序
                # 这样既保证有变动时间的方法按变动时间排序，又保证最近更新的方法排在前面
                order_clause = """ORDER BY
                    CASE
                        WHEN cm.last_date IS NOT NULL THEN cm.last_date
                        ELSE cm.updated_at
                    END DESC,
                    cm.updated_at DESC,
                    cm.id DESC"""

                # 获取数据
                query = f"""
                    SELECT DISTINCT
                        cm.id as method_id,
                        cm.method_no,
                        cm.name as method_name,
                        cm.price as analysis_price,
                        cm.lowest_bid as lowest_price,
                        cm.cma,
                        cm.cnas,
                        cm.gov_agree,
                        cm.last_date,
                        cm.status as method_status,
                        cm.updated_at as method_updated_at,
                        si.id as service_item_id,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        si.child_item,
                        si.service_category_name,
                        si.second_category_name,
                        si.status as item_status
                    FROM check_methods cm
                    LEFT JOIN service_items si ON cm.service_item_id = si.id
                    {where_clause}
                    {order_clause}
                    LIMIT ? OFFSET ?
                """

                cursor.execute(query, params + [limit, offset])
                methods = []

                for row in cursor.fetchall():
                    method_dict = dict(row)

                    # 获取采样价格信息（包括已删除的）
                    sample_data = self.get_method_sample_prices_with_history(method_dict['method_id'], include_deleted=True)
                    method_dict['sample_types'] = sample_data['current_sample_types']
                    method_dict['deleted_sample_types'] = sample_data['deleted_sample_types']

                    # 添加价格变动分析（复用控制台页面的逻辑）
                    method_id = method_dict['method_id']

                    # 分析价格变动
                    recent_analysis_change = self._get_recent_analysis_price_change(cursor, method_id, 7)
                    method_dict['analysis_price_change'] = recent_analysis_change

                    # 最低价格变动
                    recent_lowest_change = self._get_recent_lowest_price_change(cursor, method_id, 7)
                    method_dict['lowest_price_change'] = recent_lowest_change

                    # 采样价格变动
                    recent_sample_changes = self._get_recent_sample_price_changes(cursor, method_id, 7)
                    method_dict['sample_price_changes'] = recent_sample_changes

                    methods.append(method_dict)

                return {
                    'success': True,
                    'data': {
                        'methods': methods,
                        'total': total_count,
                        'page': offset // limit + 1,
                        'per_page': limit,
                        'has_more': offset + limit < total_count
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'查询方法列表时出错: {str(e)}',
                'data': {'methods': [], 'total': 0}
            }


    def set_manual_baseline_prices(self, method_ids: list, username: str = None) -> Dict:
        """为指定方法设置手动基准价格"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                success_count = 0
                failed_methods = []

                for method_id in method_ids:
                    try:
                        # 获取当前价格作为基准价格（使用与基准变动计算相同的数据源）
                        # 分析价格：从price_history表获取最新的check_method_price
                        cursor.execute("""
                            SELECT check_method_price as current_analysis_price
                            FROM price_history
                            WHERE check_method_id = ? AND check_method_price IS NOT NULL
                            ORDER BY fetch_time DESC
                            LIMIT 1
                        """, (method_id,))

                        analysis_price_result = cursor.fetchone()
                        current_analysis_price = analysis_price_result['current_analysis_price'] if analysis_price_result else None

                        # 最低价格：从price_history表获取最新的effective_lowest_price
                        cursor.execute("""
                            SELECT
                                CASE
                                    WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                                    ELSE lowest_bid
                                END as current_lowest_price
                            FROM price_history
                            WHERE check_method_id = ?
                            AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
                            ORDER BY fetch_time DESC
                            LIMIT 1
                        """, (method_id,))

                        lowest_price_result = cursor.fetchone()
                        current_lowest_price = lowest_price_result['current_lowest_price'] if lowest_price_result else None

                        if not current_analysis_price and not current_lowest_price:
                            failed_methods.append(f"方法ID {method_id}: 未找到当前价格数据")
                            continue

                        # 获取当前采样价格
                        cursor.execute("""
                            SELECT type, price
                            FROM sample_types
                            WHERE check_method_id = ? AND price IS NOT NULL
                        """, (method_id,))

                        sample_prices = {}
                        for row in cursor.fetchall():
                            sample_prices[row['type']] = row['price']

                        # 插入或更新手动基准价格
                        cursor.execute("""
                            INSERT OR REPLACE INTO manual_baseline_prices
                            (check_method_id, baseline_analysis_price, baseline_lowest_price,
                             baseline_sample_prices, set_time, set_by_user)
                            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
                        """, (
                            method_id,
                            current_analysis_price,
                            current_lowest_price,
                            json.dumps(sample_prices) if sample_prices else None,
                            username
                        ))

                        success_count += 1

                    except Exception as e:
                        failed_methods.append(f"方法ID {method_id}: {str(e)}")

                conn.commit()

                return {
                    'success': True,
                    'message': f'成功设置 {success_count} 个方法的基准价格',
                    'success_count': success_count,
                    'failed_count': len(failed_methods),
                    'failed_methods': failed_methods
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'设置基准价格时出错: {str(e)}'
            }

    def get_manual_baseline_prices(self, method_ids: list = None) -> Dict:
        """获取手动设置的基准价格"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if method_ids:
                    placeholders = ','.join(['?'] * len(method_ids))
                    cursor.execute(f"""
                        SELECT
                            mbp.*,
                            cm.name as method_name,
                            si.name as service_item_name
                        FROM manual_baseline_prices mbp
                        JOIN check_methods cm ON mbp.check_method_id = cm.id
                        JOIN service_items si ON cm.service_item_id = si.id
                        WHERE mbp.check_method_id IN ({placeholders})
                        ORDER BY mbp.set_time DESC
                    """, method_ids)
                else:
                    cursor.execute("""
                        SELECT
                            mbp.*,
                            cm.name as method_name,
                            si.name as service_item_name
                        FROM manual_baseline_prices mbp
                        JOIN check_methods cm ON mbp.check_method_id = cm.id
                        JOIN service_items si ON cm.service_item_id = si.id
                        ORDER BY mbp.set_time DESC
                    """)

                baselines = []
                for row in cursor.fetchall():
                    baseline_dict = dict(row)
                    # 解析采样价格JSON
                    if baseline_dict['baseline_sample_prices']:
                        try:
                            baseline_dict['baseline_sample_prices'] = json.loads(baseline_dict['baseline_sample_prices'])
                        except:
                            baseline_dict['baseline_sample_prices'] = {}
                    else:
                        baseline_dict['baseline_sample_prices'] = {}
                    baselines.append(baseline_dict)

                return {
                    'success': True,
                    'baselines': baselines
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取基准价格时出错: {str(e)}'
            }

    def get_manual_baseline_details(self, page: int = 1, per_page: int = 20) -> Dict:
        """获取手动基准价格的详细信息，包含当前价格和默认基准价格的对比"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取所有手动基准价格记录
                cursor.execute("""
                    SELECT
                        mbp.*,
                        cm.name as method_name,
                        cm.method_no,
                        si.name as service_item_name,
                        si.number as service_item_number,
                        si.service_category_name
                    FROM manual_baseline_prices mbp
                    JOIN check_methods cm ON mbp.check_method_id = cm.id
                    JOIN service_items si ON cm.service_item_id = si.id
                    ORDER BY mbp.set_time DESC
                """)

                all_records = cursor.fetchall()
                total_count = len(all_records)

                # 应用分页
                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page
                paged_records = all_records[start_idx:end_idx]

                detailed_baselines = []

                for record in paged_records:
                    method_id = record['check_method_id']

                    # 获取当前价格（分析价格）
                    cursor.execute("""
                        SELECT check_method_price as current_analysis_price
                        FROM price_history
                        WHERE check_method_id = ? AND check_method_price IS NOT NULL
                        ORDER BY fetch_time DESC
                        LIMIT 1
                    """, (method_id,))

                    current_analysis_result = cursor.fetchone()
                    current_analysis_price = current_analysis_result['current_analysis_price'] if current_analysis_result else None

                    # 获取当前最低价格
                    cursor.execute("""
                        SELECT
                            CASE
                                WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                                ELSE lowest_bid
                            END as current_lowest_price
                        FROM price_history
                        WHERE check_method_id = ?
                        AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
                        ORDER BY fetch_time DESC
                        LIMIT 1
                    """, (method_id,))

                    current_lowest_result = cursor.fetchone()
                    current_lowest_price = current_lowest_result['current_lowest_price'] if current_lowest_result else None

                    # 获取默认基准价格（历史第一次分析价格）
                    cursor.execute("""
                        SELECT check_method_price as default_baseline_analysis_price
                        FROM price_history
                        WHERE check_method_id = ? AND check_method_price IS NOT NULL
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_id,))

                    default_analysis_result = cursor.fetchone()
                    default_baseline_analysis_price = default_analysis_result['default_baseline_analysis_price'] if default_analysis_result else None

                    # 获取默认基准最低价格（历史第一次最低价格）
                    cursor.execute("""
                        SELECT
                            CASE
                                WHEN lowest_bid IS NULL OR lowest_bid = 0 THEN check_method_price
                                ELSE lowest_bid
                            END as default_baseline_lowest_price
                        FROM price_history
                        WHERE check_method_id = ?
                        AND (lowest_bid IS NOT NULL OR check_method_price IS NOT NULL)
                        ORDER BY fetch_time ASC
                        LIMIT 1
                    """, (method_id,))

                    default_lowest_result = cursor.fetchone()
                    default_baseline_lowest_price = default_lowest_result['default_baseline_lowest_price'] if default_lowest_result else None

                    # 获取当前采样价格
                    cursor.execute("""
                        SELECT st.type, st.price
                        FROM sample_types st
                        WHERE st.check_method_id = ? AND st.price IS NOT NULL
                        ORDER BY st.type
                    """, (method_id,))

                    current_sample_prices = {}
                    for sample_row in cursor.fetchall():
                        current_sample_prices[sample_row['type']] = sample_row['price']

                    # 解析手动基准采样价格
                    manual_baseline_sample_prices = {}
                    if record['baseline_sample_prices']:
                        try:
                            manual_baseline_sample_prices = json.loads(record['baseline_sample_prices'])
                        except:
                            manual_baseline_sample_prices = {}

                    # 构建详细记录
                    detailed_record = {
                        'id': record['id'],
                        'check_method_id': method_id,
                        'method_name': record['method_name'],
                        'method_no': record['method_no'],
                        'service_item_name': record['service_item_name'],
                        'service_item_number': record['service_item_number'],
                        'service_category_name': record['service_category_name'],
                        'set_time': record['set_time'],
                        'set_by_user': record['set_by_user'],

                        # 当前价格
                        'current_analysis_price': current_analysis_price,
                        'current_lowest_price': current_lowest_price,
                        'current_sample_prices': current_sample_prices,

                        # 手动基准价格
                        'manual_baseline_analysis_price': record['baseline_analysis_price'],
                        'manual_baseline_lowest_price': record['baseline_lowest_price'],
                        'manual_baseline_sample_prices': manual_baseline_sample_prices,

                        # 默认基准价格
                        'default_baseline_analysis_price': default_baseline_analysis_price,
                        'default_baseline_lowest_price': default_baseline_lowest_price,
                    }

                    detailed_baselines.append(detailed_record)

                # 计算分页信息
                total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 0

                return {
                    'success': True,
                    'baselines': detailed_baselines,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'current_page': page,
                    'per_page': per_page
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取手动基准价格详细信息时出错: {str(e)}'
            }

    def remove_manual_baseline_prices(self, method_ids: list) -> Dict:
        """移除指定方法的手动基准价格"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                placeholders = ','.join(['?'] * len(method_ids))
                cursor.execute(f"""
                    DELETE FROM manual_baseline_prices
                    WHERE check_method_id IN ({placeholders})
                """, method_ids)

                deleted_count = cursor.rowcount
                conn.commit()

                return {
                    'success': True,
                    'message': f'成功移除 {deleted_count} 个方法的手动基准价格',
                    'deleted_count': deleted_count
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'移除基准价格时出错: {str(e)}'
            }


def main():
    """示例用法"""
    manager = PriceDataManager()

    # 获取统计信息
    stats = manager.get_statistics()
    print("数据库统计信息:")
    print(f"总项目数: {stats['total_items']}")
    print(f"总方法数: {stats['total_methods']}")
    print(f"价格记录数: {stats['total_price_records']}")
    print(f"最后抓取时间: {stats['last_fetch']}")

    # 搜索项目
    items = manager.search_items(keyword="金属", limit=5)
    print(f"\n搜索到 {len(items)} 个包含'金属'的项目")

    # 获取价格变动
    changes = manager.get_price_changes(days=7)
    print(f"\n近7天有 {len(changes)} 个价格变动")

    # 导出数据示例
    # manager.export_to_csv("price_data.csv")
    # manager.export_to_excel("price_data.xlsx")


if __name__ == "__main__":
    main()