#!/usr/bin/env python3
"""
标准方法管理模块
提供标准方法的增删改查、Excel导入、智能推荐等功能
"""

import sqlite3
import pandas as pd
import re
import json
import io
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from database_cache_manager import OptimizedQueryMixin, cache_manager

class StandardMethodsManager(OptimizedQueryMixin):
    """标准方法管理器"""

    def __init__(self, db_path: str = 'kangda_prices.db'):
        self.db_path = db_path

    def _parse_standard_number(self, full_standard_number: str) -> Tuple[str, str, int]:
        """
        解析标准号，提取标准类型、数字部分和年份

        Args:
            full_standard_number: 完整标准号，如 "GB 5085.3-2007"

        Returns:
            (标准类型, 数字部分字符串, 年份) 如 ("GB", "5085.3", 2007)
            数字部分保持字符串格式以正确处理 5085.10 vs 5085.3 的排序
        """
        if not full_standard_number:
            return ("", "0", 0)

        # 匹配标准号格式：标准类型 + 数字 + 年份
        # 支持格式：GB 5085.3-2007, GB/T 14848-2017, HJ/T 299-2007 等
        pattern = r'^([A-Z]+(?:/[A-Z]+)?)\s*(\d+(?:\.\d+)?)-(\d{4})$'
        match = re.match(pattern, full_standard_number.strip())

        if match:
            standard_type = match.group(1)  # GB, HJ, GB/T, HJ/T 等
            number_part = match.group(2)   # "5085.3" 保持字符串格式
            year = int(match.group(3))      # 2007
            return (standard_type, number_part, year)

        # 如果无法解析，尝试提取年份
        year_match = re.search(r'-(\d{4})$', full_standard_number)
        year = int(year_match.group(1)) if year_match else 0

        # 尝试提取标准类型
        type_match = re.match(r'^([A-Z]+(?:/[A-Z]+)?)', full_standard_number.strip())
        standard_type = type_match.group(1) if type_match else full_standard_number

        return (standard_type, "0", year)

    def _compare_number_strings(self, num1: str, num2: str) -> int:
        """
        比较两个数字字符串，正确处理小数点

        Args:
            num1, num2: 数字字符串，如 "5085.3", "5085.10"

        Returns:
            -1 if num1 < num2, 0 if equal, 1 if num1 > num2
        """
        try:
            # 分割整数部分和小数部分
            parts1 = num1.split('.')
            parts2 = num2.split('.')

            int_part1 = int(parts1[0])
            int_part2 = int(parts2[0])

            # 先比较整数部分
            if int_part1 != int_part2:
                return -1 if int_part1 < int_part2 else 1

            # 整数部分相同，比较小数部分
            dec_part1 = parts1[1] if len(parts1) > 1 else "0"
            dec_part2 = parts2[1] if len(parts2) > 1 else "0"

            # 补齐小数位数进行比较
            max_len = max(len(dec_part1), len(dec_part2))
            dec_part1 = dec_part1.ljust(max_len, '0')
            dec_part2 = dec_part2.ljust(max_len, '0')

            if dec_part1 < dec_part2:
                return -1
            elif dec_part1 > dec_part2:
                return 1
            else:
                return 0

        except (ValueError, IndexError):
            # 如果解析失败，按字符串比较
            return -1 if num1 < num2 else (1 if num1 > num2 else 0)

    def _get_standard_type_priority(self, standard_type: str) -> int:
        """
        获取标准类型的优先级（数字越小优先级越高）

        Args:
            standard_type: 标准类型，如 "GB", "HJ", "GB/T", "HJ/T"

        Returns:
            优先级数字
        """
        priority_map = {
            'GB': 1,      # 国家标准
            'HJ': 2,      # 环保行业标准
            'GB/T': 3,    # 国家推荐标准
            'HJ/T': 4,    # 环保行业推荐标准
        }

        return priority_map.get(standard_type, 999)  # 其他标准类型排在最后

    def _create_sort_key(self, method_data: Dict[str, Any]) -> Tuple[int, int, str, str]:
        """
        为标准方法创建排序键

        排序规则：
        1. 年份降序（新到旧）
        2. 同年份内按标准类型优先级
        3. 同类型内按数字升序（使用字符串比较以正确处理小数）
        4. 最后按标准号字符串排序（兜底）

        Returns:
            (负年份, 类型优先级, 数字部分字符串, 标准号) 用于排序
        """
        full_number = method_data.get('full_standard_number', '')
        standard_type, number_part, year = self._parse_standard_number(full_number)
        type_priority = self._get_standard_type_priority(standard_type)

        # 为了正确排序数字，需要对数字字符串进行格式化
        # 将 "5085.3" 转换为 "05085.30" 这样的格式以便字符串排序
        formatted_number = self._format_number_for_sorting(number_part)

        return (-year, type_priority, formatted_number, full_number)

    def _format_number_for_sorting(self, number_str: str) -> str:
        """
        格式化数字字符串用于排序

        Args:
            number_str: 数字字符串，如 "5085.3", "5085.10"

        Returns:
            格式化后的字符串，如 "05085.30", "05085.10"
        """
        try:
            if '.' in number_str:
                int_part, dec_part = number_str.split('.')
                # 整数部分补齐到5位，小数部分补齐到2位
                return f"{int_part.zfill(5)}.{dec_part.ljust(2, '0')}"
            else:
                # 没有小数点的情况
                return f"{number_str.zfill(5)}.00"
        except:
            return number_str.zfill(10)  # 兜底处理
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_standard_method(self, method_id: int) -> Dict[str, Any]:
        """获取单个标准方法详情"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 查询方法基本信息
                cursor.execute("""
                    SELECT sm.*,
                           (SELECT GROUP_CONCAT(aps.name, '、')
                            FROM standard_method_scopes sms2
                            JOIN applicable_scopes aps ON sms2.applicable_scope_id = aps.id
                            WHERE sms2.standard_method_id = sm.id) as applicable_scopes,
                           (SELECT GROUP_CONCAT(di.display_name, '、')
                            FROM standard_method_items smi2
                            JOIN detection_items di ON smi2.detection_item_id = di.id
                            WHERE smi2.standard_method_id = sm.id) as detection_items,
                           (SELECT GROUP_CONCAT(sms3.applicable_scope_id, ',')
                            FROM standard_method_scopes sms3
                            WHERE sms3.standard_method_id = sm.id) as applicable_scope_ids
                    FROM standard_methods sm
                    WHERE sm.id = ?
                """, (method_id,))

                row = cursor.fetchone()
                if not row:
                    return {
                        'success': False,
                        'message': '标准方法不存在'
                    }

                # 构建方法信息
                method = {
                    'id': row[0],
                    'standard_number': row[1],
                    'standard_year': row[2],
                    'full_standard_number': row[3],
                    'is_appendix_method': bool(row[4]),
                    'appendix_number': row[5],
                    'is_informative_appendix': bool(row[6]),
                    'standard_name': row[7],
                    'status': row[8],
                    'remark': row[9],
                    'created_at': row[10],
                    'updated_at': row[11],
                    'applicable_scopes': row[12] or '',
                    'detection_items': row[13] or '',
                    'applicable_scope_ids': [int(x) for x in row[14].split(',') if x] if row[14] else []
                }

                return {
                    'success': True,
                    'data': method
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取标准方法失败: {str(e)}'
            }

    def get_standard_methods(self, keyword: str = '', scope_id: int = None,
                           item_id: int = None, item_name: str = '', is_appendix: bool = None, 
                           is_informative: bool = None, status: int = None, 
                           limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """
        获取标准方法列表
        
        Args:
            keyword: 搜索关键词
            scope_id: 适用范围ID
            item_id: 检测项目ID
            item_name: 检测项目名称（模糊匹配）
            is_appendix: 是否为附录方法
            is_informative: 是否为资料性附录
            status: 状态筛选
            limit: 每页数量
            offset: 偏移量
            
        Returns:
            包含方法列表和总数的字典
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if keyword:
                    where_conditions.append("""
                        (sm.full_standard_number LIKE ? OR sm.standard_name LIKE ? 
                         OR sm.appendix_number LIKE ?)
                    """)
                    keyword_param = f'%{keyword}%'
                    params.extend([keyword_param, keyword_param, keyword_param])
                
                if scope_id is not None:
                    where_conditions.append("sms.applicable_scope_id = ?")
                    params.append(scope_id)
                
                if item_id is not None:
                    where_conditions.append("smi.detection_item_id = ?")
                    params.append(item_id)
                
                # 新增：支持检测项目名称的模糊匹配
                if item_name:
                    # 处理顿号分隔的情况，匹配任意一个检测项目
                    item_parts = [part.strip() for part in item_name.split('、') if part.strip()]
                    if item_parts:
                        # 对每个项目进行模糊匹配
                        item_conditions = []
                        for item_part in item_parts:
                            item_conditions.append("(di.name LIKE ? OR di.display_name LIKE ?)")
                            item_param = f'%{item_part}%'
                            params.extend([item_param, item_param])
                        
                        where_conditions.append(f"({' OR '.join(item_conditions)})")
                
                if is_appendix is not None:
                    where_conditions.append("sm.is_appendix_method = ?")
                    params.append(is_appendix)
                
                if is_informative is not None:
                    where_conditions.append("sm.is_informative_appendix = ?")
                    params.append(is_informative)
                
                if status is not None:
                    where_conditions.append("sm.status = ?")
                    params.append(status)
                
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                # 查询总数 - 需要JOIN检测项目表
                count_sql = f"""
                    SELECT COUNT(DISTINCT sm.id)
                    FROM standard_methods sm
                    LEFT JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                    LEFT JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                    LEFT JOIN detection_items di ON smi.detection_item_id = di.id
                    {where_clause}
                """
                
                cursor.execute(count_sql, params)
                total = cursor.fetchone()[0]
                
                # 查询数据 - 需要JOIN检测项目表（不在SQL中排序，在Python中排序）
                data_sql = f"""
                    SELECT DISTINCT sm.*,
                           (SELECT GROUP_CONCAT(aps.name, '、')
                            FROM standard_method_scopes sms2
                            JOIN applicable_scopes aps ON sms2.applicable_scope_id = aps.id
                            WHERE sms2.standard_method_id = sm.id) as applicable_scopes,
                           (SELECT GROUP_CONCAT(
                                CASE
                                    WHEN di2.show_aliases_in_list = 1 AND di2.aliases IS NOT NULL AND di2.aliases != '' THEN
                                        di2.display_name || '(' || REPLACE(REPLACE(di2.aliases, '["', ''), '"]', '') || ')'
                                    ELSE di2.display_name
                                END, '、')
                            FROM standard_method_items smi2
                            JOIN detection_items di2 ON smi2.detection_item_id = di2.id
                            WHERE smi2.standard_method_id = sm.id) as detection_items
                    FROM standard_methods sm
                    LEFT JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                    LEFT JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                    LEFT JOIN detection_items di ON smi.detection_item_id = di.id
                    {where_clause}
                """

                cursor.execute(data_sql, params)
                all_methods = [dict(row) for row in cursor.fetchall()]

                # 使用Python进行复杂排序
                all_methods.sort(key=self._create_sort_key)

                # 应用分页
                methods = all_methods[offset:offset + limit]
                
                # 检查是否有新方法可替代资料性附录方法，并获取详细的替代信息
                for method in methods:
                    if method['is_informative_appendix']:
                        # 获取按检测项目分组的替代信息
                        item_alternatives = self._get_item_alternatives_info(method['id'])
                        method['has_alternatives'] = len(item_alternatives) > 0
                        method['item_alternatives'] = item_alternatives
                    else:
                        method['has_alternatives'] = False
                        method['item_alternatives'] = {}
                
                return {
                    'success': True,
                    'data': {
                        'methods': methods,
                        'total': total,
                        'page': offset // limit + 1,
                        'per_page': limit,
                        'has_more': offset + limit < total
                    }
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'获取标准方法列表失败: {str(e)}',
                'data': {'methods': [], 'total': 0}
            }
    
    def _check_method_alternatives(self, method_id: int) -> bool:
        """检查方法是否有替代方案"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM method_alternatives
                    WHERE old_method_id = ?
                """, (method_id,))
                return cursor.fetchone()[0] > 0
        except:
            return False

    def _get_database_aliases(self, item_name: str) -> List[str]:
        """
        从数据库获取检测项目的别名

        Args:
            item_name: 检测项目名称

        Returns:
            List[str]: 别名列表
        """
        import json  # 在函数开始处导入

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 查找匹配的检测项目及其别名
                cursor.execute("""
                    SELECT aliases FROM detection_items
                    WHERE name = ? OR display_name = ?
                    AND aliases IS NOT NULL AND aliases != ''
                """, (item_name, item_name))

                result = cursor.fetchone()
                if result and result[0]:
                    try:
                        aliases = json.loads(result[0])
                        return aliases if isinstance(aliases, list) else []
                    except (json.JSONDecodeError, TypeError):
                        return []

                # 如果直接匹配不到，尝试通过别名反向查找
                cursor.execute("""
                    SELECT name, display_name, aliases FROM detection_items
                    WHERE aliases IS NOT NULL AND aliases != ''
                """)

                for row in cursor.fetchall():
                    name, display_name, aliases_json = row
                    try:
                        aliases = json.loads(aliases_json)
                        if isinstance(aliases, list) and item_name in aliases:
                            # 如果输入的是别名，返回主名称和其他别名
                            result_aliases = [name, display_name] + aliases
                            return [alias for alias in result_aliases if alias != item_name]
                    except (json.JSONDecodeError, TypeError):
                        continue

                return []

        except Exception as e:
            print(f"获取数据库别名时出错: {str(e)}")
            return []

    def _normalize_item_name(self, item_name: str) -> List[str]:
        """
        标准化检测项目名称，生成可能的匹配名称列表
        处理别名和化学符号的匹配

        别名识别规则：
        1. 从数据库获取设置的别名
        2. 处理末尾括号的别名识别
        3. 使用硬编码的化学符号映射作为后备
        """
        if not item_name:
            return []

        normalized_names = [item_name.strip()]

        # 1. 从数据库获取别名
        db_aliases = self._get_database_aliases(item_name.strip())
        normalized_names.extend(db_aliases)

        # 2. 处理末尾括号的别名识别
        import re

        # 匹配末尾的中文括号（）
        chinese_bracket_pattern = r'^(.+?)（([^（）]+)）\s*$'
        chinese_match = re.match(chinese_bracket_pattern, item_name.strip())

        # 匹配末尾的英文括号()
        english_bracket_pattern = r'^(.+?)\(([^()]+)\)\s*$'
        english_match = re.match(english_bracket_pattern, item_name.strip())

        main_part = None
        alias_part = None

        if chinese_match:
            main_part = chinese_match.group(1).strip()
            alias_part = chinese_match.group(2).strip()
        elif english_match:
            main_part = english_match.group(1).strip()
            alias_part = english_match.group(2).strip()

        if main_part and alias_part:
            normalized_names.extend([main_part, alias_part])

            # 特殊处理化学元素
            if main_part == '铬' and alias_part == '六价':
                normalized_names.extend(['六价铬', 'Cr⁶⁺', 'Cr(VI)', 'Cr6+'])
            elif main_part == '铬' and alias_part == '三价':
                normalized_names.extend(['三价铬', 'Cr³⁺', 'Cr(III)', 'Cr3+'])
            elif main_part == 'BOD₅' and alias_part == '生化需氧量':
                normalized_names.extend(['生化需氧量', 'BOD5'])
            elif main_part == '生化需氧量' and alias_part == 'BOD₅':
                normalized_names.extend(['BOD₅', 'BOD5'])
            elif main_part == '化学需氧量' and alias_part == 'COD':
                normalized_names.extend(['COD'])
            elif main_part == 'COD' and alias_part == '化学需氧量':
                normalized_names.extend(['化学需氧量'])

        # 3. 处理常见的化学符号映射（作为后备）
        symbol_mappings = {
            '铅': ['Pb'],
            '镉': ['Cd'],
            '汞': ['Hg'],
            '砷': ['As'],
            '铬': ['Cr'],
            '六价铬': ['Cr⁶⁺', 'Cr(VI)', 'Cr6+'],
            'Cr⁶⁺': ['六价铬', 'Cr(VI)', 'Cr6+'],
            '铜': ['Cu'],
            '锌': ['Zn'],
            '镍': ['Ni'],
            '铁': ['Fe'],
            '锰': ['Mn'],
            'pH': ['pH值'],
            'pH值': ['pH'],
            'COD': ['化学需氧量'],
            '化学需氧量': ['COD'],
            '生化需氧量': ['BOD₅', 'BOD5'],
            'BOD₅': ['生化需氧量', 'BOD5'],
            'BOD5': ['生化需氧量', 'BOD₅'],
            '总磷': ['TP'],
            'TP': ['总磷'],
            '总氮': ['TN'],
            'TN': ['总氮'],
            '悬浮物': ['SS'],
            'SS': ['悬浮物'],
            '溶解氧': ['DO'],
            'DO': ['溶解氧']
        }

        # 添加映射的名称（只有在数据库中没有找到别名时才使用）
        for name in list(normalized_names):
            if name in symbol_mappings and not db_aliases:
                normalized_names.extend(symbol_mappings[name])

        # 去重并返回
        return list(set(normalized_names))

    def _get_item_alternatives_info(self, method_id: int) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取资料性附录方法的按检测项目分组的替代信息
        返回格式: {检测项目名称: [替代方法列表]}
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取当前方法的信息
                cursor.execute("""
                    SELECT full_standard_number, standard_year, is_informative_appendix
                    FROM standard_methods
                    WHERE id = ?
                """, (method_id,))

                current_method = cursor.fetchone()
                if not current_method or not current_method[2]:  # 不是资料性附录
                    return {}

                current_year = int(current_method[1]) if current_method[1].isdigit() else 0

                # 获取当前方法的检测项目
                cursor.execute("""
                    SELECT di.name, di.display_name
                    FROM standard_method_items smi
                    JOIN detection_items di ON smi.detection_item_id = di.id
                    WHERE smi.standard_method_id = ?
                """, (method_id,))

                current_items = cursor.fetchall()
                if not current_items:
                    return {}

                # 获取当前方法的适用范围
                cursor.execute("""
                    SELECT aps.id, aps.name
                    FROM standard_method_scopes sms
                    JOIN applicable_scopes aps ON sms.applicable_scope_id = aps.id
                    WHERE sms.standard_method_id = ?
                """, (method_id,))

                current_scopes = cursor.fetchall()
                if not current_scopes:
                    return {}

                item_alternatives = {}

                # 为每个检测项目查找替代方法
                for item_name, item_display_name in current_items:
                    # 生成该检测项目的标准化名称列表
                    normalized_names = self._normalize_item_name(item_name)
                    normalized_names.extend(self._normalize_item_name(item_display_name))
                    normalized_names = list(set(normalized_names))  # 去重

                    # 按适用范围分组的替代方法
                    scope_grouped_alternatives = {}

                    # 查找所有可能的替代方法（扩展适用范围匹配逻辑）
                    placeholders = ','.join(['?'] * len(normalized_names))

                    # 获取所有相关的适用范围ID（包括部分匹配的范围）
                    related_scope_ids = set()
                    for scope_id, scope_name in current_scopes:
                        related_scope_ids.add(scope_id)

                        # 查找包含相同关键词的其他适用范围
                        scope_keywords = scope_name.split('、')
                        for keyword in scope_keywords:
                            keyword = keyword.strip()
                            if keyword:
                                cursor.execute("""
                                    SELECT id FROM applicable_scopes
                                    WHERE name LIKE ? OR name = ?
                                """, (f'%{keyword}%', keyword))

                                for (related_id,) in cursor.fetchall():
                                    related_scope_ids.add(related_id)

                    related_scope_ids = list(related_scope_ids)
                    scope_placeholders = ','.join(['?'] * len(related_scope_ids))

                    cursor.execute(f"""
                        SELECT DISTINCT sm.id, sm.full_standard_number, sm.standard_name,
                               sm.standard_year, sm.is_informative_appendix,
                               di.name as item_name, di.display_name as item_display_name
                        FROM standard_methods sm
                        JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                        JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                        JOIN detection_items di ON smi.detection_item_id = di.id
                        WHERE sms.applicable_scope_id IN ({scope_placeholders})
                          AND sm.id != ?
                          AND sm.status = 0
                          AND (di.name IN ({placeholders}) OR di.display_name IN ({placeholders}))
                          AND (
                              -- 非资料性附录方法
                              sm.is_informative_appendix = 0
                              OR
                              -- 或者是更新的资料性附录方法
                              (sm.is_informative_appendix = 1 AND CAST(sm.standard_year AS INTEGER) > ?)
                          )
                        ORDER BY sm.is_informative_appendix ASC, sm.standard_year DESC
                    """, related_scope_ids + [method_id] + normalized_names + normalized_names + [current_year])

                    potential_alternatives = cursor.fetchall()

                    # 为每个找到的替代方法获取其实际的适用范围
                    for alt_row in potential_alternatives:
                        alt_id = alt_row[0]
                        alt_info = {
                            'id': alt_id,
                            'full_standard_number': alt_row[1],
                            'standard_name': alt_row[2],
                            'standard_year': alt_row[3],
                            'is_informative_appendix': bool(alt_row[4]),
                            'matched_item_name': alt_row[5],
                            'matched_item_display': alt_row[6],
                            'is_newer': not bool(alt_row[4]) or int(alt_row[3]) > current_year
                        }

                        # 获取该替代方法的实际适用范围
                        cursor.execute("""
                            SELECT aps.name
                            FROM standard_method_scopes sms
                            JOIN applicable_scopes aps ON sms.applicable_scope_id = aps.id
                            WHERE sms.standard_method_id = ?
                            ORDER BY aps.name
                        """, (alt_id,))

                        alt_scopes = cursor.fetchall()

                        # 将该替代方法添加到其实际适用的每个范围中
                        for (alt_scope_name,) in alt_scopes:
                            if alt_scope_name not in scope_grouped_alternatives:
                                scope_grouped_alternatives[alt_scope_name] = []

                            # 避免重复添加相同的方法
                            if not any(alt['id'] == alt_info['id'] for alt in scope_grouped_alternatives[alt_scope_name]):
                                scope_grouped_alternatives[alt_scope_name].append(alt_info)

                    # 如果在任何适用范围内找到替代方法，添加到结果中
                    if scope_grouped_alternatives:
                        item_alternatives[item_display_name] = scope_grouped_alternatives

                return item_alternatives

        except Exception as e:
            print(f"获取检测项目替代方法信息失败: {e}")
            return {}

    def _get_method_alternatives_info(self, method_id: int) -> List[Dict[str, Any]]:
        """获取资料性附录方法的智能替代信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取当前方法的信息
                cursor.execute("""
                    SELECT full_standard_number, standard_year, is_informative_appendix
                    FROM standard_methods
                    WHERE id = ?
                """, (method_id,))

                current_method = cursor.fetchone()
                if not current_method or not current_method[2]:  # 不是资料性附录
                    return []

                current_year = int(current_method[1]) if current_method[1].isdigit() else 0

                # 查找同一适用范围内的新方法
                cursor.execute("""
                    SELECT sm_new.id, sm_new.full_standard_number, sm_new.standard_name,
                           sm_new.standard_year, sm_new.is_informative_appendix,
                           aps.name as scope_name,
                           GROUP_CONCAT(di.display_name, '、') as detection_items
                    FROM standard_methods sm_current
                    JOIN standard_method_scopes sms_current ON sm_current.id = sms_current.standard_method_id
                    JOIN applicable_scopes aps ON sms_current.applicable_scope_id = aps.id
                    JOIN standard_method_scopes sms_new ON aps.id = sms_new.applicable_scope_id
                    JOIN standard_methods sm_new ON sms_new.standard_method_id = sm_new.id
                    LEFT JOIN standard_method_items smi_new ON sm_new.id = smi_new.standard_method_id
                    LEFT JOIN detection_items di ON smi_new.detection_item_id = di.id
                    WHERE sm_current.id = ?
                      AND sm_new.id != ?
                      AND sm_new.status = 0
                      AND (
                          -- 非资料性附录方法
                          sm_new.is_informative_appendix = 0
                          OR
                          -- 或者是更新的资料性附录方法
                          (sm_new.is_informative_appendix = 1 AND CAST(sm_new.standard_year AS INTEGER) > ?)
                      )
                    GROUP BY sm_new.id, sm_new.full_standard_number, sm_new.standard_name,
                             sm_new.standard_year, sm_new.is_informative_appendix, aps.name
                    ORDER BY sm_new.is_informative_appendix ASC, sm_new.standard_year DESC
                """, (method_id, method_id, current_year))

                alternatives = []
                for row in cursor.fetchall():
                    alternatives.append({
                        'id': row[0],
                        'full_standard_number': row[1],
                        'standard_name': row[2],
                        'standard_year': row[3],
                        'is_informative_appendix': bool(row[4]),
                        'scope_name': row[5],
                        'detection_items': row[6] or '',
                        'is_newer': not bool(row[4]) or int(row[3]) > current_year
                    })

                return alternatives

        except Exception as e:
            print(f"获取替代方法信息失败: {e}")
            return []
    
    def get_method_alternatives(self, method_id: int) -> List[Dict[str, Any]]:
        """获取方法的替代方案"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT ma.*, 
                           sm_new.full_standard_number as new_method_number,
                           sm_new.standard_name as new_method_name,
                           aps.name as scope_name,
                           di.display_name as item_name
                    FROM method_alternatives ma
                    JOIN standard_methods sm_new ON ma.new_method_id = sm_new.id
                    JOIN applicable_scopes aps ON ma.applicable_scope_id = aps.id
                    JOIN detection_items di ON ma.detection_item_id = di.id
                    WHERE ma.old_method_id = ?
                    ORDER BY ma.effective_date DESC
                """, (method_id,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"获取方法替代方案失败: {e}")
            return []
    
    def add_standard_method(self, method_data: Dict[str, Any]) -> Dict[str, Any]:
        """添加标准方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建完整标准号
                full_number = f"{method_data['standard_number']}-{method_data['standard_year']}"
                
                # 插入标准方法
                cursor.execute("""
                    INSERT INTO standard_methods 
                    (standard_number, standard_year, full_standard_number, is_appendix_method,
                     appendix_number, is_informative_appendix, standard_name, status, remark)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    method_data['standard_number'],
                    method_data['standard_year'],
                    full_number,
                    method_data.get('is_appendix_method', False),
                    method_data.get('appendix_number'),
                    method_data.get('is_informative_appendix', False),
                    method_data['standard_name'],
                    method_data.get('status', 0),
                    method_data.get('remark', '')
                ))
                
                method_id = cursor.lastrowid
                
                # 关联适用范围
                if 'applicable_scopes' in method_data:
                    for scope_id in method_data['applicable_scopes']:
                        cursor.execute("""
                            INSERT OR IGNORE INTO standard_method_scopes 
                            (standard_method_id, applicable_scope_id)
                            VALUES (?, ?)
                        """, (method_id, scope_id))
                
                # 处理检测项目
                if 'detection_items' in method_data:
                    # 兼容旧的ID列表格式
                    for item_id in method_data['detection_items']:
                        cursor.execute("""
                            INSERT OR IGNORE INTO standard_method_items
                            (standard_method_id, detection_item_id)
                            VALUES (?, ?)
                        """, (method_id, item_id))
                elif 'detection_items_text' in method_data:
                    # 处理新的文本格式
                    items_text = method_data['detection_items_text'].strip()
                    if items_text:
                        item_names = [item.strip() for item in items_text.split('、') if item.strip()]

                        # 获取现有的检测项目
                        cursor.execute("SELECT id, name, display_name FROM detection_items")
                        existing_items = {name: id for id, name, display_name in cursor.fetchall()}

                        for item_name in item_names:
                            # 处理上下标格式
                            processed_name = self._process_subscript_superscript(item_name)

                            # 查找或创建检测项目
                            item_id = None
                            if processed_name in existing_items:
                                item_id = existing_items[processed_name]
                            else:
                                # 创建新的检测项目
                                display_name = self._format_chemical_formula(processed_name)
                                cursor.execute("""
                                    INSERT INTO detection_items (name, display_name) VALUES (?, ?)
                                """, (processed_name, display_name))
                                item_id = cursor.lastrowid
                                existing_items[processed_name] = item_id

                            # 关联检测项目
                            cursor.execute("""
                                INSERT OR IGNORE INTO standard_method_items
                                (standard_method_id, detection_item_id)
                                VALUES (?, ?)
                            """, (method_id, item_id))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': '标准方法添加成功',
                    'method_id': method_id
                }
                
        except sqlite3.IntegrityError as e:
            return {
                'success': False,
                'message': f'标准方法已存在或数据冲突: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'添加标准方法失败: {str(e)}'
            }
    
    def update_standard_method(self, method_id: int, method_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新标准方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建完整标准号
                if 'standard_number' in method_data and 'standard_year' in method_data:
                    full_number = f"{method_data['standard_number']}-{method_data['standard_year']}"
                    method_data['full_standard_number'] = full_number
                
                # 构建更新SQL
                update_fields = []
                params = []
                
                for field in ['standard_number', 'standard_year', 'full_standard_number',
                             'is_appendix_method', 'appendix_number', 'is_informative_appendix',
                             'standard_name', 'status', 'remark']:
                    if field in method_data:
                        update_fields.append(f"{field} = ?")
                        params.append(method_data[field])
                
                if update_fields:
                    update_fields.append("updated_at = CURRENT_TIMESTAMP")
                    params.append(method_id)
                    
                    cursor.execute(f"""
                        UPDATE standard_methods 
                        SET {', '.join(update_fields)}
                        WHERE id = ?
                    """, params)
                
                # 更新适用范围关联
                if 'applicable_scopes' in method_data:
                    cursor.execute("DELETE FROM standard_method_scopes WHERE standard_method_id = ?", (method_id,))
                    for scope_id in method_data['applicable_scopes']:
                        cursor.execute("""
                            INSERT INTO standard_method_scopes 
                            (standard_method_id, applicable_scope_id)
                            VALUES (?, ?)
                        """, (method_id, scope_id))
                
                # 更新检测项目关联
                if 'detection_items' in method_data:
                    # 兼容旧的ID列表格式
                    cursor.execute("DELETE FROM standard_method_items WHERE standard_method_id = ?", (method_id,))
                    for item_id in method_data['detection_items']:
                        cursor.execute("""
                            INSERT OR IGNORE INTO standard_method_items
                            (standard_method_id, detection_item_id)
                            VALUES (?, ?)
                        """, (method_id, item_id))
                elif 'detection_items_text' in method_data:
                    # 处理新的文本格式
                    cursor.execute("DELETE FROM standard_method_items WHERE standard_method_id = ?", (method_id,))

                    items_text = method_data['detection_items_text'].strip()
                    if items_text:
                        item_names = [item.strip() for item in items_text.split('、') if item.strip()]

                        # 获取现有的检测项目
                        cursor.execute("SELECT id, name, display_name FROM detection_items")
                        existing_items = {name: id for id, name, display_name in cursor.fetchall()}

                        for item_name in item_names:
                            # 处理上下标格式
                            processed_name = self._process_subscript_superscript(item_name)

                            # 查找或创建检测项目
                            item_id = None
                            if processed_name in existing_items:
                                item_id = existing_items[processed_name]
                            else:
                                # 创建新的检测项目
                                display_name = self._format_chemical_formula(processed_name)
                                cursor.execute("""
                                    INSERT INTO detection_items (name, display_name) VALUES (?, ?)
                                """, (processed_name, display_name))
                                item_id = cursor.lastrowid
                                existing_items[processed_name] = item_id

                            # 关联检测项目（使用OR IGNORE避免重复插入）
                            cursor.execute("""
                                INSERT OR IGNORE INTO standard_method_items
                                (standard_method_id, detection_item_id)
                                VALUES (?, ?)
                            """, (method_id, item_id))
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': '标准方法更新成功'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'更新标准方法失败: {str(e)}'
            }
    
    def delete_standard_method(self, method_id: int) -> Dict[str, Any]:
        """删除标准方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否被评价方法引用
                cursor.execute("""
                    SELECT COUNT(*) FROM evaluation_recommendations 
                    WHERE recommended_standard_method_id = ?
                """, (method_id,))
                
                if cursor.fetchone()[0] > 0:
                    return {
                        'success': False,
                        'message': '该标准方法正在被评价方法引用，无法删除'
                    }
                
                # 删除标准方法（级联删除关联数据）
                cursor.execute("DELETE FROM standard_methods WHERE id = ?", (method_id,))
                
                if cursor.rowcount == 0:
                    return {
                        'success': False,
                        'message': '标准方法不存在'
                    }
                
                conn.commit()
                
                return {
                    'success': True,
                    'message': '标准方法删除成功'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'删除标准方法失败: {str(e)}'
            }
    
    def get_applicable_scopes(self) -> List[Dict[str, Any]]:
        """
        动态获取适用范围
        从标准方法关联的适用范围中提取，确保数据实时性
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 从标准方法关联表中动态获取所有使用中的适用范围
                cursor.execute("""
                    SELECT DISTINCT aps.id, aps.name, aps.description,
                           COUNT(DISTINCT sms.standard_method_id) as method_count,
                           aps.created_at
                    FROM applicable_scopes aps
                    INNER JOIN standard_method_scopes sms ON aps.id = sms.applicable_scope_id
                    INNER JOIN standard_methods sm ON sms.standard_method_id = sm.id
                    WHERE sm.status = 0  -- 只包含有效的标准方法
                    GROUP BY aps.id, aps.name, aps.description, aps.created_at
                    ORDER BY aps.name
                """)

                scopes = []
                for row in cursor.fetchall():
                    scope_dict = dict(row)
                    scopes.append(scope_dict)

                return scopes

        except Exception as e:
            print(f"获取适用范围失败: {e}")
            # 如果动态获取失败，回退到静态获取
            try:
                with self.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT * FROM applicable_scopes ORDER BY name")
                    return [dict(row) for row in cursor.fetchall()]
            except Exception as fallback_error:
                print(f"回退获取适用范围也失败: {fallback_error}")
                return []

    def get_applicable_scopes_with_stats(self) -> Dict[str, Any]:
        """
        获取适用范围及其统计信息
        包括关联的标准方法数量、检测项目数量等
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取适用范围及其统计信息
                cursor.execute("""
                    SELECT
                        aps.id,
                        aps.name,
                        aps.description,
                        COUNT(sms.standard_method_id) as method_count
                    FROM applicable_scopes aps
                    INNER JOIN standard_method_scopes sms ON aps.id = sms.applicable_scope_id
                    INNER JOIN standard_methods sm ON sms.standard_method_id = sm.id
                    WHERE sm.status = 0
                    GROUP BY aps.id, aps.name, aps.description
                    ORDER BY method_count DESC, aps.name
                """)

                scopes_with_stats = []
                for row in cursor.fetchall():
                    scope_data = dict(row)

                    # 获取检测项目数量
                    cursor.execute("""
                        SELECT COUNT(DISTINCT smi.detection_item_id)
                        FROM standard_method_items smi
                        INNER JOIN standard_method_scopes sms ON smi.standard_method_id = sms.standard_method_id
                        WHERE sms.applicable_scope_id = ?
                    """, (scope_data['id'],))

                    item_count = cursor.fetchone()[0]
                    scope_data['item_count'] = item_count

                    # 获取示例方法
                    cursor.execute("""
                        SELECT sm.standard_name
                        FROM standard_methods sm
                        INNER JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                        WHERE sms.applicable_scope_id = ? AND sm.status = 0
                        LIMIT 3
                    """, (scope_data['id'],))

                    sample_methods = [row[0] for row in cursor.fetchall()]
                    scope_data['sample_methods'] = '、'.join(sample_methods)

                    scopes_with_stats.append(scope_data)

                # 获取总体统计
                stats = {
                    'total_scopes': len(scopes_with_stats),
                    'total_methods': sum(scope['method_count'] for scope in scopes_with_stats),
                    'total_items': sum(scope['item_count'] for scope in scopes_with_stats)
                }


                return {
                    'success': True,
                    'data': {
                        'scopes': scopes_with_stats,
                        'statistics': stats
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取适用范围统计失败: {str(e)}'
            }
    
    def get_detection_items(self) -> List[Dict[str, Any]]:
        """获取所有检测项目"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM detection_items ORDER BY name")
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取检测项目失败: {e}")
            return []

    def import_from_excel(self, file_path_or_stream) -> Dict[str, Any]:
        """
        从Excel文件或流导入标准方法数据

        Args:
            file_path_or_stream: 文件路径或文件流对象

        Excel列结构：
        第1列：标准号
        第2列：附录方法（空值表示否）
        第3列：资料性附录（非附录方法时填写"/"）
        第4列：标准名称
        第5列：适用范围（多个范围用逗号分隔）
        第6列：检测项目（需要识别和正确显示上下标格式）
        """
        df = None
        try:
            # 读取Excel文件或流
            df = pd.read_excel(file_path_or_stream, header=0, engine='openpyxl')

            if df.empty:
                return {
                    'success': False,
                    'message': 'Excel文件为空'
                }

            # 检查列数
            if len(df.columns) < 6:
                return {
                    'success': False,
                    'message': f'Excel文件列数不足，需要6列，实际{len(df.columns)}列'
                }

            # 重命名列
            df.columns = ['标准号', '附录方法', '资料性附录', '标准名称', '适用范围', '检测项目']

            success_count = 0
            error_count = 0
            errors = []

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取现有的适用范围和检测项目
                cursor.execute("SELECT id, name FROM applicable_scopes")
                existing_scopes = {name: id for id, name in cursor.fetchall()}

                cursor.execute("SELECT id, name, display_name FROM detection_items")
                existing_items = {name: id for id, name, display_name in cursor.fetchall()}

                for index, row in df.iterrows():
                    try:
                        # 解析标准号和年份
                        standard_full = str(row['标准号']).strip()
                        if '-' in standard_full:
                            standard_number, standard_year = standard_full.split('-', 1)
                        else:
                            # 尝试从标准号中提取年份
                            match = re.search(r'(\d{4})$', standard_full)
                            if match:
                                standard_year = match.group(1)
                                standard_number = standard_full[:-4].rstrip('-')
                            else:
                                standard_number = standard_full
                                standard_year = datetime.now().year

                        # 解析附录信息
                        appendix_text = str(row['附录方法']).strip() if pd.notna(row['附录方法']) else ''
                        is_appendix_method = bool(appendix_text and appendix_text != '/')
                        appendix_number = appendix_text if is_appendix_method else None

                        # 解析资料性附录
                        informative_text = str(row['资料性附录']).strip() if pd.notna(row['资料性附录']) else ''
                        is_informative_appendix = is_appendix_method and informative_text != '/'

                        # 标准名称
                        standard_name = str(row['标准名称']).strip()

                        # 解析适用范围 - 支持顿号和逗号分隔
                        scopes_text = str(row['适用范围']).strip() if pd.notna(row['适用范围']) else ''
                        # 先按逗号分割，再按顿号分割
                        scope_names = []
                        for scope_part in scopes_text.split('，'):
                            for scope_name in scope_part.split('、'):
                                scope_name = scope_name.strip()
                                if scope_name:
                                    scope_names.append(scope_name)

                        # 解析检测项目 - 支持顿号分隔
                        items_text = str(row['检测项目']).strip() if pd.notna(row['检测项目']) else ''
                        item_names = [s.strip() for s in items_text.split('、') if s.strip()]

                        # 处理上下标格式
                        processed_items = []
                        for item_name in item_names:
                            processed_name = self._process_subscript_superscript(item_name)
                            processed_items.append(processed_name)

                        # 构建完整标准号
                        full_standard_number = f"{standard_number}-{standard_year}"

                        # 检查是否已存在相同的标准方法（考虑附录方法）
                        cursor.execute("""
                            SELECT id FROM standard_methods
                            WHERE full_standard_number = ?
                            AND is_appendix_method = ?
                            AND (appendix_number = ? OR (appendix_number IS NULL AND ? IS NULL))
                        """, (full_standard_number, is_appendix_method, appendix_number, appendix_number))

                        existing_method = cursor.fetchone()

                        if existing_method:
                            # 如果已存在，更新现有记录
                            method_id = existing_method[0]
                            cursor.execute("""
                                UPDATE standard_methods
                                SET standard_number = ?, standard_year = ?, is_appendix_method = ?,
                                    appendix_number = ?, is_informative_appendix = ?, standard_name = ?,
                                    updated_at = CURRENT_TIMESTAMP
                                WHERE id = ?
                            """, (
                                standard_number, standard_year, is_appendix_method,
                                appendix_number, is_informative_appendix, standard_name, method_id
                            ))

                            # 清除现有的关联关系
                            cursor.execute("DELETE FROM standard_method_scopes WHERE standard_method_id = ?", (method_id,))
                            cursor.execute("DELETE FROM standard_method_items WHERE standard_method_id = ?", (method_id,))
                        else:
                            # 插入新的标准方法
                            cursor.execute("""
                                INSERT INTO standard_methods
                                (standard_number, standard_year, full_standard_number, is_appendix_method,
                                 appendix_number, is_informative_appendix, standard_name, status)
                                VALUES (?, ?, ?, ?, ?, ?, ?, 0)
                            """, (
                                standard_number, standard_year, full_standard_number,
                                is_appendix_method, appendix_number, is_informative_appendix, standard_name
                            ))

                            method_id = cursor.lastrowid

                        # 处理适用范围
                        for scope_name in scope_names:
                            if scope_name not in existing_scopes:
                                # 创建新的适用范围
                                cursor.execute("""
                                    INSERT INTO applicable_scopes (name) VALUES (?)
                                """, (scope_name,))
                                existing_scopes[scope_name] = cursor.lastrowid

                            # 关联适用范围
                            cursor.execute("""
                                INSERT OR IGNORE INTO standard_method_scopes
                                (standard_method_id, applicable_scope_id)
                                VALUES (?, ?)
                            """, (method_id, existing_scopes[scope_name]))

                        # 处理检测项目
                        for item_name in processed_items:
                            if item_name not in existing_items:
                                # 创建新的检测项目
                                display_name = self._format_chemical_formula(item_name)
                                cursor.execute("""
                                    INSERT INTO detection_items (name, display_name) VALUES (?, ?)
                                """, (item_name, display_name))
                                existing_items[item_name] = cursor.lastrowid

                            # 关联检测项目
                            cursor.execute("""
                                INSERT OR IGNORE INTO standard_method_items
                                (standard_method_id, detection_item_id)
                                VALUES (?, ?)
                            """, (method_id, existing_items[item_name]))

                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f"第{index+2}行: {str(e)}")

                conn.commit()

            return {
                'success': True,
                'message': f'导入完成：成功{success_count}条，失败{error_count}条',
                'data': {
                    'success_count': success_count,
                    'error_count': error_count,
                    'errors': errors[:10]  # 只返回前10个错误
                }
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导入Excel文件失败: {str(e)}'
            }

    def _process_subscript_superscript(self, text: str) -> str:
        """处理上下标格式"""
        # 处理常见的化学符号上下标
        replacements = {
            'Cr6+': 'Cr⁶⁺',
            'Cr(VI)': 'Cr⁶⁺',
            'BOD5': 'BOD₅',
            'O2': 'O₂',
            'CO2': 'CO₂',
            'SO2': 'SO₂',
            'NO2': 'NO₂',
            'H2S': 'H₂S',
            'NH3': 'NH₃',
            'pH': 'pH'
        }

        result = text
        for old, new in replacements.items():
            result = result.replace(old, new)

        return result

    def _format_chemical_formula(self, name: str) -> str:
        """格式化化学式显示"""
        # 这里可以添加更复杂的化学式格式化逻辑
        return name

    def export_to_excel(self) -> Dict[str, Any]:
        """
        导出标准方法数据到Excel格式
        
        Returns:
            包含Excel文件字节流的字典
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询所有标准方法及其关联数据
                cursor.execute("""
                    SELECT sm.full_standard_number,
                           CASE WHEN sm.is_appendix_method = 1 THEN sm.appendix_number ELSE '' END as appendix_method,
                           CASE WHEN sm.is_appendix_method = 1 AND sm.is_informative_appendix = 0 THEN '/' 
                                WHEN sm.is_appendix_method = 1 AND sm.is_informative_appendix = 1 THEN '资料性附录'
                                ELSE '/' END as informative_appendix,
                           sm.standard_name,
                           (SELECT GROUP_CONCAT(aps.name, '、')
                            FROM standard_method_scopes sms
                            JOIN applicable_scopes aps ON sms.applicable_scope_id = aps.id
                            WHERE sms.standard_method_id = sm.id) as applicable_scopes,
                           (SELECT GROUP_CONCAT(di.display_name, '、')
                            FROM standard_method_items smi
                            JOIN detection_items di ON smi.detection_item_id = di.id
                            WHERE smi.standard_method_id = sm.id) as detection_items
                    FROM standard_methods sm
                    WHERE sm.status = 0
                    ORDER BY sm.full_standard_number, sm.appendix_number
                """)
                
                methods = cursor.fetchall()
                
                if not methods:
                    return {
                        'success': False,
                        'message': '没有数据可导出'
                    }
                
                # 创建DataFrame
                data = []
                for method in methods:
                    data.append({
                        '标准号': method[0] or '',
                        '附录方法': method[1] or '',
                        '资料性附录': method[2] or '/',
                        '标准名称': method[3] or '',
                        '适用范围': method[4] or '',
                        '检测项目': method[5] or ''
                    })
                
                df = pd.DataFrame(data)
                
                # 创建Excel文件
                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='标准方法', index=False)
                    
                    # 获取工作表并设置列宽
                    worksheet = writer.sheets['标准方法']
                    
                    # 设置列宽
                    column_widths = {
                        'A': 20,  # 标准号
                        'B': 12,  # 附录方法
                        'C': 12,  # 资料性附录
                        'D': 40,  # 标准名称
                        'E': 30,  # 适用范围
                        'F': 40   # 检测项目
                    }
                    
                    for col, width in column_widths.items():
                        worksheet.column_dimensions[col].width = width
                
                output.seek(0)
                excel_data = output.getvalue()
                
                return {
                    'success': True,
                    'data': excel_data,
                    'filename': f'标准方法导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
                    'count': len(methods)
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'导出Excel失败: {str(e)}'
            }

    def create_evaluation_method(self, evaluation_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 插入评价方法
                cursor.execute("""
                    INSERT INTO evaluation_methods
                    (name, description, applicable_scope_id, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    evaluation_data['name'],
                    evaluation_data.get('description', ''),
                    evaluation_data.get('applicable_scope_id'),
                    evaluation_data.get('is_active', True),
                    evaluation_data.get('created_by', '')
                ))

                evaluation_id = cursor.lastrowid

                # 添加推荐配置
                if 'recommendations' in evaluation_data:
                    for rec in evaluation_data['recommendations']:
                        cursor.execute("""
                            INSERT INTO evaluation_recommendations
                            (evaluation_method_id, detection_item_id, recommended_standard_method_id,
                             priority, reason)
                            VALUES (?, ?, ?, ?, ?)
                        """, (
                            evaluation_id,
                            rec['detection_item_id'],
                            rec['recommended_standard_method_id'],
                            rec.get('priority', 1),
                            rec.get('reason', '')
                        ))

                conn.commit()

                return {
                    'success': True,
                    'message': '评价方法创建成功',
                    'evaluation_id': evaluation_id
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'创建评价方法失败: {str(e)}'
            }

    def get_evaluation_methods(self, limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """获取评价方法列表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 查询总数
                cursor.execute("SELECT COUNT(*) FROM evaluation_methods WHERE is_active = 1")
                total = cursor.fetchone()[0]

                # 查询数据
                cursor.execute("""
                    SELECT em.*, aps.name as scope_name,
                           COUNT(er.id) as recommendation_count
                    FROM evaluation_methods em
                    LEFT JOIN applicable_scopes aps ON em.applicable_scope_id = aps.id
                    LEFT JOIN evaluation_recommendations er ON em.id = er.evaluation_method_id
                    WHERE em.is_active = 1
                    GROUP BY em.id
                    ORDER BY em.created_at DESC
                    LIMIT ? OFFSET ?
                """, (limit, offset))

                methods = [dict(row) for row in cursor.fetchall()]

                return {
                    'success': True,
                    'data': {
                        'methods': methods,
                        'total': total,
                        'page': offset // limit + 1,
                        'per_page': limit,
                        'has_more': offset + limit < total
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取评价方法列表失败: {str(e)}',
                'data': {'methods': [], 'total': 0}
            }

    def delete_evaluation_method(self, evaluation_id: int) -> Dict[str, Any]:
        """删除评价方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查评价方法是否存在
                cursor.execute("SELECT id, name FROM evaluation_methods WHERE id = ? AND is_active = 1", (evaluation_id,))
                method = cursor.fetchone()

                if not method:
                    return {
                        'success': False,
                        'message': '评价方法不存在'
                    }

                # 删除推荐配置
                cursor.execute("DELETE FROM evaluation_recommendations WHERE evaluation_method_id = ?", (evaluation_id,))

                # 软删除评价方法（设置is_active为0）
                cursor.execute("UPDATE evaluation_methods SET is_active = 0 WHERE id = ?", (evaluation_id,))

                conn.commit()

                return {
                    'success': True,
                    'message': '评价方法删除成功'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'删除评价方法失败: {str(e)}'
            }

    def search_detection_items_optimized(self, query: str = '', limit: int = 20) -> Dict[str, Any]:
        """优化的检测项目搜索"""
        try:
            search_fields = ['name', 'display_name']
            results = self.execute_optimized_search('detection_items', search_fields, query, limit)

            return {
                'success': True,
                'data': results
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'搜索检测项目失败: {str(e)}'
            }

    def search_standard_methods_optimized(self, query: str = '', limit: int = 20) -> Dict[str, Any]:
        """优化的标准方法搜索"""
        try:
            search_fields = ['full_standard_number', 'standard_name']
            results = self.execute_optimized_search('standard_methods', search_fields, query, limit)

            # 添加显示文本
            for result in results:
                result['display_text'] = f"{result['full_standard_number']} - {result['standard_name']}"

            return {
                'success': True,
                'data': results
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'搜索标准方法失败: {str(e)}'
            }

    def clear_search_cache(self) -> None:
        """清除搜索缓存"""
        cache_manager.clear_cache('search')

    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return cache_manager.get_cache_stats()

    def get_evaluation_method_scopes(self, evaluation_method_id: int) -> Dict[str, Any]:
        """获取评价方法的所有适用范围"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 验证评价方法是否存在
                cursor.execute("SELECT id, name FROM evaluation_methods WHERE id = ? AND is_active = 1",
                             (evaluation_method_id,))
                method = cursor.fetchone()
                if not method:
                    return {
                        'success': False,
                        'message': '评价方法不存在'
                    }

                # 获取该评价方法名称的所有适用范围
                # 因为同名评价方法可能有多个适用范围，每个适用范围都是独立的评价方法记录
                cursor.execute("""
                    SELECT DISTINCT em.id, em.name, aps.id as scope_id, aps.name as scope_name
                    FROM evaluation_methods em
                    JOIN applicable_scopes aps ON em.applicable_scope_id = aps.id
                    WHERE em.name = (SELECT name FROM evaluation_methods WHERE id = ?)
                      AND em.is_active = 1
                    ORDER BY aps.name
                """, (evaluation_method_id,))

                scopes = []
                method_name = None
                for row in cursor.fetchall():
                    if method_name is None:
                        method_name = row[1]
                    scopes.append({
                        'evaluation_method_id': row[0],
                        'scope_id': row[2],
                        'scope_name': row[3]
                    })

                return {
                    'success': True,
                    'data': {
                        'method_name': method_name,
                        'scopes': scopes
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取评价方法适用范围失败: {str(e)}'
            }

    def get_evaluation_based_recommendations(self, evaluation_method_id: int,
                                           applicable_scope_id: int,
                                           detection_items_text: str) -> Dict[str, Any]:
        """基于评价方法的智能推荐 - 优化版本"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 验证评价方法和适用范围
                cursor.execute("""
                    SELECT em.id, em.name, aps.name as scope_name
                    FROM evaluation_methods em
                    JOIN applicable_scopes aps ON em.applicable_scope_id = aps.id
                    WHERE em.id = ? AND em.applicable_scope_id = ? AND em.is_active = 1
                """, (evaluation_method_id, applicable_scope_id))

                method_info = cursor.fetchone()
                if not method_info:
                    return {
                        'success': False,
                        'message': '评价方法和适用范围不匹配'
                    }

                # 解析检测项目文本（支持顿号分隔）
                detection_items = [item.strip() for item in detection_items_text.replace('、', ',').split(',') if item.strip()]

                if not detection_items:
                    return {
                        'success': False,
                        'message': '请输入有效的检测项目'
                    }

                # 获取优化的推荐方案
                recommendation_result = self._get_optimized_recommendations(
                    evaluation_method_id, applicable_scope_id, detection_items
                )

                return {
                    'success': True,
                    'data': {
                        'evaluation_method': {
                            'id': method_info[0],
                            'name': method_info[1],
                            'scope_name': method_info[2]
                        },
                        'input_items': detection_items,
                        'matched_items': recommendation_result['matched_items'],
                        'unmatched_items': recommendation_result['unmatched_items'],
                        'recommended_schemes': recommendation_result['recommended_schemes'],
                        'item_details': recommendation_result['item_details'],
                        'total_schemes': len(recommendation_result['recommended_schemes']),
                        'match_rate': len(recommendation_result['matched_items']) / len(detection_items) if detection_items else 0
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'智能推荐失败: {str(e)}'
            }

    def _get_optimized_recommendations(self, evaluation_method_id: int, applicable_scope_id: int,
                                     detection_items: List[str]) -> Dict[str, Any]:
        """获取优化的推荐方案 - 优先推荐能检测多个项目的方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 收集所有可用的推荐配置
                all_recommendations = {}  # {method_id: {method_info, covered_items: []}}
                matched_items = []
                unmatched_items = []
                item_details = {}  # {item_name: [all_available_methods]}

                for item_text in detection_items:
                    # 查找该项目的所有推荐配置
                    item_recommendations = self._find_evaluation_recommendations(
                        evaluation_method_id, item_text
                    )

                    if item_recommendations:
                        matched_items.append(item_text)
                        item_methods = []

                        for rec in item_recommendations:
                            method_id = rec['method_id']

                            # 检查资料性附录方法的可用性
                            is_available = self._check_method_availability(
                                method_id, applicable_scope_id, item_text
                            )

                            rec['is_available'] = is_available
                            item_methods.append(rec)

                            # 只有可用的方法才加入推荐方案
                            if is_available:
                                if method_id not in all_recommendations:
                                    all_recommendations[method_id] = {
                                        'method_info': rec,
                                        'covered_items': [],
                                        'covered_items_set': set(),  # 用于去重
                                        'priority': rec['priority']
                                    }

                                # 避免重复添加相同的检测项目
                                item_key = f"{item_text}_{rec['display_name']}"
                                if item_key not in all_recommendations[method_id]['covered_items_set']:
                                    all_recommendations[method_id]['covered_items_set'].add(item_key)
                                    all_recommendations[method_id]['covered_items'].append({
                                        'item_text': item_text,
                                        'display_name': rec['display_name'],
                                        'match_type': rec['match_type']
                                    })
                            else:
                                # 如果推荐的方法不可用（如资料性附录方法有更新），查找替代方法
                                alternative_methods = self._find_alternative_methods(
                                    method_id, applicable_scope_id, item_text
                                )

                                for alt_method in alternative_methods:
                                    alt_method_id = alt_method['method_id']
                                    if alt_method_id not in all_recommendations:
                                        all_recommendations[alt_method_id] = {
                                            'method_info': alt_method,
                                            'covered_items': [],
                                            'covered_items_set': set(),  # 用于去重
                                            'priority': rec['priority']  # 继承原推荐的优先级
                                        }

                                    # 避免重复添加相同的检测项目
                                    item_key = f"{item_text}_{rec['display_name']}"
                                    if item_key not in all_recommendations[alt_method_id]['covered_items_set']:
                                        all_recommendations[alt_method_id]['covered_items_set'].add(item_key)
                                        all_recommendations[alt_method_id]['covered_items'].append({
                                            'item_text': item_text,
                                            'display_name': rec['display_name'],
                                            'match_type': 'alternative'
                                        })

                        item_details[item_text] = item_methods
                    else:
                        unmatched_items.append(item_text)
                        item_details[item_text] = []

                # 生成推荐方案（按覆盖项目数量排序）
                recommended_schemes = []
                for method_id, method_data in all_recommendations.items():
                    # 清理去重用的set，只保留实际的covered_items
                    if 'covered_items_set' in method_data:
                        del method_data['covered_items_set']

                    scheme = {
                        'method_id': method_id,
                        'full_standard_number': method_data['method_info']['full_standard_number'],
                        'standard_name': method_data['method_info']['standard_name'],
                        'priority': method_data['priority'],
                        'covered_items_count': len(method_data['covered_items']),
                        'covered_items': method_data['covered_items'],
                        'is_informative_appendix': method_data['method_info'].get('is_informative_appendix', False),
                        'reason': method_data['method_info'].get('reason', '')
                    }
                    recommended_schemes.append(scheme)

                # 按覆盖项目数量（降序）和优先级（升序）排序
                recommended_schemes.sort(key=lambda x: (-x['covered_items_count'], x['priority']))

                return {
                    'matched_items': matched_items,
                    'unmatched_items': unmatched_items,
                    'recommended_schemes': recommended_schemes,
                    'item_details': item_details
                }

        except Exception as e:
            print(f"获取优化推荐失败: {e}")
            return {
                'matched_items': [],
                'unmatched_items': detection_items,
                'recommended_schemes': [],
                'item_details': {}
            }

    def _check_method_availability(self, method_id: int, applicable_scope_id: int, item_text: str) -> bool:
        """检查方法可用性 - 特别是资料性附录方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取方法信息
                cursor.execute("""
                    SELECT is_informative_appendix, standard_year
                    FROM standard_methods
                    WHERE id = ?
                """, (method_id,))

                method_info = cursor.fetchone()
                if not method_info:
                    return False

                is_informative_appendix = method_info[0]

                # 如果不是资料性附录方法，直接可用
                if not is_informative_appendix:
                    return True

                # 对于资料性附录方法，检查是否有更新的方法
                standard_year = int(method_info[1]) if method_info[1] and method_info[1].isdigit() else 0

                # 查找该检测项目在当前适用范围内是否有更新的方法
                normalized_names = self._normalize_item_name(item_text)
                placeholders = ','.join(['?'] * len(normalized_names))

                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM standard_methods sm
                    JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                    JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                    JOIN detection_items di ON smi.detection_item_id = di.id
                    WHERE sms.applicable_scope_id = ?
                      AND sm.id != ?
                      AND sm.status = 0
                      AND (di.name IN ({placeholders}) OR di.display_name IN ({placeholders}))
                      AND (
                          sm.is_informative_appendix = 0
                          OR (sm.is_informative_appendix = 1 AND CAST(sm.standard_year AS INTEGER) > ?)
                      )
                """, [applicable_scope_id, method_id] + normalized_names + normalized_names + [standard_year])

                newer_methods_count = cursor.fetchone()[0]

                # 如果有更新的方法，则该资料性附录方法不可用
                return newer_methods_count == 0

        except Exception as e:
            print(f"检查方法可用性失败: {e}")
            return True  # 出错时默认可用

    def _find_alternative_methods(self, original_method_id: int, applicable_scope_id: int, item_text: str) -> List[Dict[str, Any]]:
        """查找替代方法 - 当原方法不可用时"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 标准化项目名称
                normalized_names = self._normalize_item_name(item_text)
                placeholders = ','.join(['?'] * len(normalized_names))

                # 查找该检测项目在当前适用范围内的可用方法
                cursor.execute(f"""
                    SELECT DISTINCT sm.id as method_id, sm.full_standard_number, sm.standard_name,
                           sm.is_informative_appendix, sm.standard_year,
                           di.name as item_name, di.display_name
                    FROM standard_methods sm
                    JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                    JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                    JOIN detection_items di ON smi.detection_item_id = di.id
                    WHERE sms.applicable_scope_id = ?
                      AND sm.id != ?
                      AND sm.status = 0
                      AND (di.name IN ({placeholders}) OR di.display_name IN ({placeholders}))
                      AND sm.is_informative_appendix = 0  -- 优先选择非资料性附录方法
                    ORDER BY sm.standard_year DESC, sm.full_standard_number
                    LIMIT 3  -- 限制返回数量
                """, [applicable_scope_id, original_method_id] + normalized_names + normalized_names)

                alternatives = []
                for row in cursor.fetchall():
                    alt_data = dict(row)
                    alt_data['match_type'] = 'alternative'
                    alt_data['reason'] = f'替代不可用的方法'
                    alternatives.append(alt_data)

                return alternatives

        except Exception as e:
            print(f"查找替代方法失败: {e}")
            return []

    def get_item_alternative_methods(self, item_name: str, applicable_scope_id: int) -> Dict[str, Any]:
        """获取检测项目在指定适用范围内的所有可用标准方法"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 标准化项目名称
                normalized_names = self._normalize_item_name(item_name)
                placeholders = ','.join(['?'] * len(normalized_names))

                # 查找所有可用的标准方法
                cursor.execute(f"""
                    SELECT DISTINCT sm.id, sm.full_standard_number, sm.standard_name,
                           sm.is_informative_appendix, sm.standard_year,
                           di.name as item_name, di.display_name as item_display_name
                    FROM standard_methods sm
                    JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                    JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                    JOIN detection_items di ON smi.detection_item_id = di.id
                    WHERE sms.applicable_scope_id = ?
                      AND sm.status = 0
                      AND (di.name IN ({placeholders}) OR di.display_name IN ({placeholders}))
                    ORDER BY sm.is_informative_appendix ASC, sm.standard_year DESC
                """, [applicable_scope_id] + normalized_names + normalized_names)

                methods = []
                for row in cursor.fetchall():
                    method_data = dict(row)

                    # 检查资料性附录方法的可用性
                    if method_data['is_informative_appendix']:
                        is_available = self._check_method_availability(
                            method_data['id'], applicable_scope_id, item_name
                        )
                        method_data['is_available'] = is_available
                        method_data['availability_reason'] = '有更新方法可用' if not is_available else '可用'
                    else:
                        method_data['is_available'] = True
                        method_data['availability_reason'] = '可用'

                    methods.append(method_data)

                return {
                    'success': True,
                    'data': {
                        'item_name': item_name,
                        'methods': methods,
                        'total_methods': len(methods),
                        'available_methods': len([m for m in methods if m['is_available']])
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取检测项目可用方法失败: {str(e)}'
            }

    def _find_evaluation_recommendations(self, evaluation_method_id: int, item_text: str) -> List[Dict[str, Any]]:
        """查找评价方法中匹配的推荐配置（支持别名匹配）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                recommendations = []

                # 1. 查找系统检测项目的推荐配置
                normalized_names = self._normalize_item_name(item_text)

                for name in normalized_names:
                    cursor.execute("""
                        SELECT er.id, er.priority, er.reason,
                               di.id as item_id, di.name as item_name, di.display_name,
                               sm.id as method_id, sm.full_standard_number, sm.standard_name,
                               sm.is_informative_appendix
                        FROM evaluation_recommendations er
                        JOIN detection_items di ON er.detection_item_id = di.id
                        JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                        WHERE er.evaluation_method_id = ?
                          AND (di.name = ? OR di.display_name = ? OR di.aliases LIKE ?)
                          AND sm.status = 0
                        ORDER BY er.priority, sm.full_standard_number
                    """, (evaluation_method_id, name, name, f'%"{name}"%'))

                    for row in cursor.fetchall():
                        rec_data = dict(row)
                        rec_data['matched_text'] = item_text
                        rec_data['match_type'] = 'system_item'
                        rec_data['display_name'] = rec_data['display_name'] or rec_data['item_name']
                        recommendations.append(rec_data)

                # 2. 查找自定义检测项目的推荐配置
                cursor.execute("""
                    SELECT er.id, er.priority, er.reason,
                           er.custom_detection_item_name,
                           sm.id as method_id, sm.full_standard_number, sm.standard_name,
                           sm.is_informative_appendix
                    FROM evaluation_recommendations er
                    JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                    WHERE er.evaluation_method_id = ?
                      AND er.custom_detection_item_name = ?
                      AND sm.status = 0
                    ORDER BY er.priority, sm.full_standard_number
                """, (evaluation_method_id, item_text))

                for row in cursor.fetchall():
                    rec_data = dict(row)
                    rec_data['matched_text'] = item_text
                    rec_data['match_type'] = 'custom_item'
                    rec_data['item_name'] = rec_data['custom_detection_item_name']
                    rec_data['display_name'] = rec_data['custom_detection_item_name']
                    recommendations.append(rec_data)

                # 3. 模糊匹配自定义检测项目
                if not recommendations:
                    cursor.execute("""
                        SELECT er.id, er.priority, er.reason,
                               er.custom_detection_item_name,
                               sm.id as method_id, sm.full_standard_number, sm.standard_name,
                               sm.is_informative_appendix
                        FROM evaluation_recommendations er
                        JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                        WHERE er.evaluation_method_id = ?
                          AND er.custom_detection_item_name LIKE ?
                          AND sm.status = 0
                        ORDER BY er.priority, sm.full_standard_number
                    """, (evaluation_method_id, f'%{item_text}%'))

                    for row in cursor.fetchall():
                        rec_data = dict(row)
                        rec_data['matched_text'] = item_text
                        rec_data['match_type'] = 'fuzzy_custom'
                        rec_data['item_name'] = rec_data['custom_detection_item_name']
                        rec_data['display_name'] = rec_data['custom_detection_item_name']
                        recommendations.append(rec_data)

                return recommendations

        except Exception as e:
            print(f"查找评价方法推荐配置失败: {e}")
            return []

    def get_intelligent_recommendations(self, detection_type: str,
                                      detection_items: List[str]) -> Dict[str, Any]:
        """
        智能推荐分析方法

        Args:
            detection_type: 检测类型（适用范围）
            detection_items: 检测项目列表

        Returns:
            推荐的分析方法列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取适用范围ID
                cursor.execute("SELECT id FROM applicable_scopes WHERE name = ?", (detection_type,))
                scope_result = cursor.fetchone()
                if not scope_result:
                    return {
                        'success': False,
                        'message': f'未找到适用范围: {detection_type}'
                    }

                scope_id = scope_result[0]

                # 获取检测项目ID（支持别名匹配）
                item_ids = []
                unmatched_items = []  # 存储未匹配的检测项目名称

                for item_name in detection_items:
                    # 使用标准化名称进行匹配
                    normalized_names = self._normalize_item_name(item_name)

                    # 构建查询条件
                    conditions = []
                    params = []
                    found = False

                    for name in normalized_names:
                        conditions.append("(name = ? OR display_name = ? OR aliases LIKE ?)")
                        params.extend([name, name, f'%"{name}"%'])

                    if conditions:
                        query = f"SELECT DISTINCT id FROM detection_items WHERE {' OR '.join(conditions)}"
                        cursor.execute(query, params)
                        results = cursor.fetchall()

                        for result in results:
                            if result[0] not in item_ids:
                                item_ids.append(result[0])
                                found = True

                    # 如果在系统中未找到，记录为未匹配项目
                    if not found:
                        unmatched_items.append(item_name)

                # 从评价方法中查找自定义检测项目的推荐
                custom_recommendations = []
                if unmatched_items:
                    for custom_item in unmatched_items:
                        cursor.execute("""
                            SELECT DISTINCT sm.id, sm.full_standard_number, sm.standard_name,
                                   sm.is_informative_appendix, er.custom_detection_item_name,
                                   er.priority, er.reason
                            FROM evaluation_recommendations er
                            JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                            JOIN evaluation_methods em ON er.evaluation_method_id = em.id
                            JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                            WHERE er.custom_detection_item_name = ?
                              AND sms.applicable_scope_id = ?
                              AND sm.status = 0
                              AND em.is_active = 1
                            ORDER BY er.priority DESC, sm.created_at DESC
                        """, (custom_item, scope_id))

                        for row in cursor.fetchall():
                            custom_rec = dict(row)
                            custom_rec['covered_items'] = 1
                            custom_rec['covered_item_names'] = custom_item
                            custom_rec['priority_score'] = 3  # 评价方法推荐优先级最高
                            custom_rec['source'] = 'evaluation_method'
                            custom_rec['is_custom_item'] = True
                            custom_recommendations.append(custom_rec)

                # 查找能够检测多个项目的方法（仅针对系统中存在的检测项目）
                recommendations = []

                if item_ids:  # 只有当有系统检测项目时才执行查询
                    placeholders = ','.join(['?'] * len(item_ids))
                    cursor.execute(f"""
                        SELECT sm.id, sm.full_standard_number, sm.standard_name,
                               sm.is_informative_appendix,
                               COUNT(smi.detection_item_id) as covered_items,
                               GROUP_CONCAT(di.display_name, '、') as covered_item_names,
                               CASE
                                   WHEN sm.is_informative_appendix = 1 THEN
                                       CASE WHEN EXISTS(
                                           SELECT 1 FROM method_alternatives ma
                                           WHERE ma.old_method_id = sm.id
                                       ) THEN 0 ELSE 1 END
                                   ELSE 2
                               END as priority_score
                        FROM standard_methods sm
                        JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                        JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                        JOIN detection_items di ON smi.detection_item_id = di.id
                        WHERE sms.applicable_scope_id = ?
                          AND smi.detection_item_id IN ({placeholders})
                          AND sm.status = 0
                        GROUP BY sm.id
                        ORDER BY priority_score DESC, covered_items DESC, sm.created_at DESC
                    """, [scope_id] + item_ids)

                    for row in cursor.fetchall():
                        method_data = dict(row)
                        method_data['source'] = 'standard_method'
                        method_data['is_custom_item'] = False

                        # 检查是否有替代方法
                        if method_data['is_informative_appendix']:
                            alternatives = self.get_method_alternatives(method_data['id'])
                            method_data['alternatives'] = alternatives
                            method_data['recommended'] = len(alternatives) == 0
                        else:
                            method_data['alternatives'] = []
                            method_data['recommended'] = True

                        recommendations.append(method_data)

                # 合并自定义推荐和标准推荐
                all_recommendations = custom_recommendations + recommendations

                # 按优先级排序：评价方法推荐 > 标准方法推荐
                all_recommendations.sort(key=lambda x: (x['priority_score'], x['covered_items']), reverse=True)

                # 检查是否有任何推荐结果
                if not all_recommendations and not item_ids:
                    return {
                        'success': False,
                        'message': '未找到匹配的检测项目，且没有相关的评价方法推荐'
                    }

                return {
                    'success': True,
                    'data': {
                        'detection_type': detection_type,
                        'detection_items': detection_items,
                        'recommendations': all_recommendations,
                        'total_methods': len(all_recommendations),
                        'system_items': len(item_ids),
                        'custom_items': len(unmatched_items),
                        'custom_recommendations': len(custom_recommendations)
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'智能推荐失败: {str(e)}'
            }

    # ==================== 别名管理功能 ====================

    def get_detection_item_aliases(self, item_id: int) -> Dict[str, Any]:
        """
        获取检测项目的别名信息

        Args:
            item_id: 检测项目ID

        Returns:
            Dict: 包含别名信息的字典
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT id, name, display_name, aliases, show_aliases_in_list
                    FROM detection_items
                    WHERE id = ?
                """, (item_id,))

                result = cursor.fetchone()
                if not result:
                    return {
                        'success': False,
                        'message': '检测项目不存在'
                    }

                item_data = dict(result)

                # 解析别名JSON
                if item_data['aliases']:
                    try:
                        import json
                        item_data['aliases'] = json.loads(item_data['aliases'])
                    except (json.JSONDecodeError, TypeError):
                        item_data['aliases'] = []
                else:
                    item_data['aliases'] = []

                return {
                    'success': True,
                    'data': item_data
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'获取别名信息失败: {str(e)}'
            }

    def update_detection_item_aliases(self, item_id: int, aliases: List[str],
                                    show_in_list: bool = False) -> Dict[str, Any]:
        """
        更新检测项目的别名

        Args:
            item_id: 检测项目ID
            aliases: 别名列表
            show_in_list: 是否在列表中显示别名

        Returns:
            Dict: 操作结果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查项目是否存在
                cursor.execute("SELECT id FROM detection_items WHERE id = ?", (item_id,))
                if not cursor.fetchone():
                    return {
                        'success': False,
                        'message': '检测项目不存在'
                    }

                # 清理和验证别名
                cleaned_aliases = []
                for alias in aliases:
                    alias = alias.strip()
                    if alias and alias not in cleaned_aliases:
                        cleaned_aliases.append(alias)

                # 转换为JSON
                import json
                aliases_json = json.dumps(cleaned_aliases, ensure_ascii=False) if cleaned_aliases else None

                # 更新数据库
                cursor.execute("""
                    UPDATE detection_items
                    SET aliases = ?, show_aliases_in_list = ?
                    WHERE id = ?
                """, (aliases_json, show_in_list, item_id))

                conn.commit()

                return {
                    'success': True,
                    'message': '别名更新成功',
                    'data': {
                        'aliases': cleaned_aliases,
                        'show_in_list': show_in_list
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'更新别名失败: {str(e)}'
            }

    def search_detection_items_with_aliases(self, keyword: str = '',
                                          limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """
        搜索检测项目（包含别名搜索）

        Args:
            keyword: 搜索关键词
            limit: 每页数量
            offset: 偏移量

        Returns:
            Dict: 搜索结果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if keyword:
                    # 使用标准化名称进行搜索
                    normalized_names = self._normalize_item_name(keyword)

                    # 构建搜索条件
                    search_conditions = []
                    search_params = []

                    for name in normalized_names:
                        search_conditions.append("(name LIKE ? OR display_name LIKE ? OR aliases LIKE ?)")
                        search_params.extend([f"%{name}%", f"%{name}%", f"%{name}%"])

                    where_clause = "WHERE " + " OR ".join(search_conditions)

                    # 查询总数
                    count_query = f"SELECT COUNT(*) FROM detection_items {where_clause}"
                    cursor.execute(count_query, search_params)
                    total = cursor.fetchone()[0]

                    # 查询数据
                    data_query = f"""
                        SELECT id, name, display_name, chemical_formula, cas_number,
                               category, aliases, show_aliases_in_list, created_at
                        FROM detection_items
                        {where_clause}
                        ORDER BY name
                        LIMIT ? OFFSET ?
                    """
                    cursor.execute(data_query, search_params + [limit, offset])

                else:
                    # 查询总数
                    cursor.execute("SELECT COUNT(*) FROM detection_items")
                    total = cursor.fetchone()[0]

                    # 查询数据
                    cursor.execute("""
                        SELECT id, name, display_name, chemical_formula, cas_number,
                               category, aliases, show_aliases_in_list, created_at
                        FROM detection_items
                        ORDER BY name
                        LIMIT ? OFFSET ?
                    """, (limit, offset))

                items = []
                for row in cursor.fetchall():
                    item = dict(row)

                    # 解析别名
                    if item['aliases']:
                        try:
                            import json
                            item['aliases'] = json.loads(item['aliases'])
                        except (json.JSONDecodeError, TypeError):
                            item['aliases'] = []
                    else:
                        item['aliases'] = []

                    items.append(item)

                return {
                    'success': True,
                    'data': {
                        'items': items,
                        'total': total,
                        'page': offset // limit + 1,
                        'per_page': limit,
                        'has_more': offset + limit < total
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'搜索检测项目失败: {str(e)}',
                'data': {'items': [], 'total': 0}
            }

    # ==================== 评价方法推荐配置Excel导入功能 ====================

    def _parse_standard_method_format(self, method_text: str) -> Dict[str, Any]:
        """
        解析标准方法格式

        Args:
            method_text: 标准方法文本，如"GB 5085.3-2007"或"GB 5085.3-2007-附录A"

        Returns:
            Dict: 包含解析结果的字典
        """
        import re

        method_text = method_text.strip()

        # 匹配附录方法格式，支持多种附录名称
        # 格式1：标准号-年份-附录X (如：GB 5085.3-2007-附录A)
        appendix_pattern1 = r'^(.+?)-(\d{4})-附录([A-Z])$'
        appendix_match1 = re.match(appendix_pattern1, method_text)

        # 格式2：标准号-年份-参照 (如：GB/T 14204-1993-参照)
        appendix_pattern2 = r'^(.+?)-(\d{4})-(参照|参考|附表|附件|补充|修改单)$'
        appendix_match2 = re.match(appendix_pattern2, method_text)

        # 格式3：标准号-年份-附录名称 (如：GB 5085.3-2007-附录B.1)
        appendix_pattern3 = r'^(.+?)-(\d{4})-(附录[A-Z]\.?\d*)$'
        appendix_match3 = re.match(appendix_pattern3, method_text)

        if appendix_match1:
            # 标准附录格式
            standard_number = appendix_match1.group(1)
            year = appendix_match1.group(2)
            appendix_letter = appendix_match1.group(3)

            return {
                'standard_number': standard_number,
                'year': year,
                'full_standard_number': f"{standard_number}-{year}",
                'is_appendix': True,
                'appendix_number': f"附录{appendix_letter}",
                'original_text': method_text
            }
        elif appendix_match2:
            # 参照等特殊附录格式
            standard_number = appendix_match2.group(1)
            year = appendix_match2.group(2)
            appendix_name = appendix_match2.group(3)

            return {
                'standard_number': standard_number,
                'year': year,
                'full_standard_number': f"{standard_number}-{year}",
                'is_appendix': True,
                'appendix_number': appendix_name,
                'original_text': method_text
            }
        elif appendix_match3:
            # 复杂附录格式
            standard_number = appendix_match3.group(1)
            year = appendix_match3.group(2)
            appendix_full = appendix_match3.group(3)

            return {
                'standard_number': standard_number,
                'year': year,
                'full_standard_number': f"{standard_number}-{year}",
                'is_appendix': True,
                'appendix_number': appendix_full,
                'original_text': method_text
            }
        else:
            # 普通方法格式：标准号-年份
            normal_pattern = r'^(.+?)-(\d{4})$'
            normal_match = re.match(normal_pattern, method_text)

            if normal_match:
                standard_number = normal_match.group(1)
                year = normal_match.group(2)

                return {
                    'standard_number': standard_number,
                    'year': year,
                    'full_standard_number': f"{standard_number}-{year}",
                    'is_appendix': False,
                    'appendix_number': None,
                    'original_text': method_text
                }
            else:
                # 无法解析的格式
                return {
                    'standard_number': None,
                    'year': None,
                    'full_standard_number': method_text,
                    'is_appendix': False,
                    'appendix_number': None,
                    'original_text': method_text,
                    'parse_error': True
                }

    def _find_standard_method_by_format(self, parsed_method: Dict[str, Any]) -> int:
        """
        根据解析的标准方法格式查找数据库中的标准方法ID

        Args:
            parsed_method: 解析后的标准方法信息

        Returns:
            int: 标准方法ID，如果未找到返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if parsed_method.get('parse_error'):
                    return None

                if parsed_method['is_appendix']:
                    # 查找附录方法
                    cursor.execute("""
                        SELECT id FROM standard_methods
                        WHERE full_standard_number = ?
                        AND is_appendix_method = 1
                        AND appendix_number = ?
                    """, (
                        parsed_method['full_standard_number'],
                        parsed_method['appendix_number']
                    ))
                else:
                    # 查找普通方法
                    cursor.execute("""
                        SELECT id FROM standard_methods
                        WHERE full_standard_number = ?
                        AND is_appendix_method = 0
                    """, (parsed_method['full_standard_number'],))

                result = cursor.fetchone()
                return result[0] if result else None

        except Exception as e:
            print(f"查找标准方法失败: {str(e)}")
            return None

    def import_evaluation_recommendations_from_excel(self, evaluation_method_id: int,
                                                   file_path_or_stream) -> Dict[str, Any]:
        """
        从Excel文件导入评价方法推荐配置

        Args:
            evaluation_method_id: 评价方法ID
            file_path_or_stream: Excel文件路径或文件流

        Excel格式：
        第1列：检测项目名称（支持别名匹配）
        第2列：推荐的标准方法（支持多个方法，用顿号"、"分隔）

        Returns:
            Dict: 导入结果
        """
        import pandas as pd

        try:
            # 验证评价方法是否存在
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id, name FROM evaluation_methods WHERE id = ?",
                             (evaluation_method_id,))
                evaluation_method = cursor.fetchone()

                if not evaluation_method:
                    return {
                        'success': False,
                        'message': '评价方法不存在'
                    }

            # 读取Excel文件
            df = pd.read_excel(file_path_or_stream, header=0, engine='openpyxl')

            if df.empty:
                return {
                    'success': False,
                    'message': 'Excel文件为空'
                }

            # 检查列数
            if len(df.columns) < 2:
                return {
                    'success': False,
                    'message': f'Excel文件列数不足，需要2列，实际{len(df.columns)}列'
                }

            # 重命名列
            df.columns = ['检测项目名称', '推荐标准方法'] + list(df.columns[2:])

            # 处理数据
            success_count = 0
            error_count = 0
            errors = []
            processed_items = []

            with self.get_connection() as conn:
                cursor = conn.cursor()

                for index, row in df.iterrows():
                    row_num = index + 2  # Excel行号（从第2行开始）

                    try:
                        # 获取检测项目名称
                        item_name = str(row['检测项目名称']).strip() if pd.notna(row['检测项目名称']) else ''
                        if not item_name:
                            errors.append(f"第{row_num}行：检测项目名称为空")
                            error_count += 1
                            continue

                        # 获取推荐标准方法
                        methods_text = str(row['推荐标准方法']).strip() if pd.notna(row['推荐标准方法']) else ''
                        if not methods_text:
                            errors.append(f"第{row_num}行：推荐标准方法为空")
                            error_count += 1
                            continue

                        # 查找检测项目ID（支持别名匹配）
                        normalized_names = self._normalize_item_name(item_name)
                        item_id = None

                        for name in normalized_names:
                            cursor.execute("""
                                SELECT id FROM detection_items
                                WHERE name = ? OR display_name = ? OR aliases LIKE ?
                            """, (name, name, f'%"{name}"%'))

                            result = cursor.fetchone()
                            if result:
                                item_id = result[0]
                                break

                        # 如果未找到系统中的检测项目，将作为自定义检测项目处理
                        use_custom_item = item_id is None
                        custom_item_name = item_name if use_custom_item else None

                        # 解析推荐标准方法（支持多个方法）
                        method_names = [m.strip() for m in methods_text.split('、') if m.strip()]

                        for method_name in method_names:
                            # 解析标准方法格式
                            parsed_method = self._parse_standard_method_format(method_name)

                            # 查找标准方法ID
                            method_id = self._find_standard_method_by_format(parsed_method)

                            if not method_id:
                                errors.append(f"第{row_num}行：未找到标准方法 '{method_name}'")
                                error_count += 1
                                continue

                            # 检查是否已存在相同的推荐配置
                            if use_custom_item:
                                # 自定义检测项目的重复检查
                                cursor.execute("""
                                    SELECT id FROM evaluation_recommendations
                                    WHERE evaluation_method_id = ?
                                    AND custom_detection_item_name = ?
                                    AND recommended_standard_method_id = ?
                                """, (evaluation_method_id, custom_item_name, method_id))
                            else:
                                # 系统检测项目的重复检查
                                cursor.execute("""
                                    SELECT id FROM evaluation_recommendations
                                    WHERE evaluation_method_id = ?
                                    AND detection_item_id = ?
                                    AND recommended_standard_method_id = ?
                                """, (evaluation_method_id, item_id, method_id))

                            if cursor.fetchone():
                                # 已存在，跳过
                                continue

                            # 插入推荐配置（默认优先级为高=1）
                            if use_custom_item:
                                # 插入自定义检测项目的推荐配置
                                cursor.execute("""
                                    INSERT INTO evaluation_recommendations
                                    (evaluation_method_id, custom_detection_item_name, recommended_standard_method_id,
                                     priority, reason)
                                    VALUES (?, ?, ?, ?, ?)
                                """, (
                                    evaluation_method_id,
                                    custom_item_name,
                                    method_id,
                                    1,  # 高优先级（1=高，2=中，3=低）
                                    f"Excel批量导入：{method_name}（自定义检测项目）"
                                ))
                            else:
                                # 插入系统检测项目的推荐配置
                                cursor.execute("""
                                    INSERT INTO evaluation_recommendations
                                    (evaluation_method_id, detection_item_id, recommended_standard_method_id,
                                     priority, reason)
                                    VALUES (?, ?, ?, ?, ?)
                                """, (
                                    evaluation_method_id,
                                    item_id,
                                    method_id,
                                    1,  # 高优先级（1=高，2=中，3=低）
                                    f"Excel批量导入：{method_name}"
                                ))

                            success_count += 1
                            processed_items.append({
                                'item_name': item_name,
                                'method_name': method_name,
                                'row': row_num
                            })

                    except Exception as row_error:
                        errors.append(f"第{row_num}行处理失败：{str(row_error)}")
                        error_count += 1
                        continue

                conn.commit()

            return {
                'success': True,
                'message': f'导入完成：成功{success_count}条，失败{error_count}条',
                'data': {
                    'success_count': success_count,
                    'error_count': error_count,
                    'total_rows': len(df),
                    'errors': errors,
                    'processed_items': processed_items
                }
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导入失败: {str(e)}'
            }

    def generate_evaluation_recommendations_template(self) -> Dict[str, Any]:
        """
        生成评价方法推荐配置Excel模板

        Returns:
            Dict: 包含模板数据的字典
        """
        try:
            # 示例数据
            template_data = [
                {
                    '检测项目名称': '镉',
                    '推荐标准方法': 'GB 5085.3-2007'
                },
                {
                    '检测项目名称': 'Pb',
                    '推荐标准方法': 'GB 5085.3-2007'
                },
                {
                    '检测项目名称': '铬',
                    '推荐标准方法': 'GB 5085.3-2007、GB 5085.3-2007-附录A'
                },
                {
                    '检测项目名称': 'Cu',
                    '推荐标准方法': 'GB 5085.3-2007'
                },
                {
                    '检测项目名称': '汞',
                    '推荐标准方法': 'GB 5085.3-2007'
                }
            ]

            return {
                'success': True,
                'data': template_data,
                'headers': ['检测项目名称', '推荐标准方法'],
                'instructions': [
                    '1. 检测项目名称：支持中文名称和英文别名（如：镉、Cd、铅、Pb等）',
                    '2. 推荐标准方法：标准格式如"GB 5085.3-2007"，附录方法格式如"GB 5085.3-2007-附录A"',
                    '3. 多个推荐方法用顿号"、"分隔',
                    '4. 所有导入的推荐方法默认优先级为"高"',
                    '5. 系统会自动跳过已存在的重复配置'
                ]
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'生成模板失败: {str(e)}'
            }
