#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计汇总表创建和优化脚本
用于提升变动数据分析模块的性能
"""

import sqlite3
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any


class StatisticsSummaryManager:
    """统计汇总表管理器"""
    
    def __init__(self, db_path: str = 'kangda_prices.db'):
        self.db_path = db_path
        
    def create_statistics_summary_table(self):
        """创建统计汇总表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建统计汇总表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS statistics_summary (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stat_type TEXT NOT NULL,  -- 'new_methods', 'qualification_changes', 'price_changes'
                        period_type TEXT NOT NULL, -- 'year', 'quarter', 'month'
                        period_value TEXT NOT NULL, -- '2024-01', '2024-Q1', '2024'
                        count_value INTEGER NOT NULL DEFAULT 0,
                        method_ids TEXT, -- JSON格式存储方法ID列表
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stat_type, period_type, period_value)
                    )
                ''')
                
                # 创建索引以优化查询性能
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_statistics_summary_type_period ON statistics_summary(stat_type, period_type)",
                    "CREATE INDEX IF NOT EXISTS idx_statistics_summary_period_value ON statistics_summary(period_value)",
                    "CREATE INDEX IF NOT EXISTS idx_statistics_summary_last_updated ON statistics_summary(last_updated)"
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                conn.commit()
                print("✅ statistics_summary 表创建成功")
                return True
                
        except Exception as e:
            print(f"❌ 创建 statistics_summary 表失败: {e}")
            return False
    
    def optimize_existing_indexes(self):
        """优化现有数据库索引"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 为统计查询添加复合索引
                optimization_indexes = [
                    # method_change_history表优化
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_composite ON method_change_history(change_date, change_type, method_id)",
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_date_type ON method_change_history(change_date, change_type)",
                    
                    # price_history表优化
                    "CREATE INDEX IF NOT EXISTS idx_price_history_fetch_time_method ON price_history(fetch_time, check_method_id)",
                    "CREATE INDEX IF NOT EXISTS idx_price_history_method_fetch ON price_history(check_method_id, fetch_time)",
                    
                    # check_methods表优化
                    "CREATE INDEX IF NOT EXISTS idx_check_methods_last_date ON check_methods(last_date)",
                    "CREATE INDEX IF NOT EXISTS idx_check_methods_status_last_date ON check_methods(status, last_date)"
                ]
                
                for index_sql in optimization_indexes:
                    try:
                        cursor.execute(index_sql)
                        print(f"✅ 索引创建成功: {index_sql.split('idx_')[1].split(' ')[0]}")
                    except sqlite3.OperationalError as e:
                        if "already exists" not in str(e):
                            print(f"⚠️  索引创建警告: {e}")
                
                conn.commit()
                print("✅ 数据库索引优化完成")
                return True
                
        except Exception as e:
            print(f"❌ 优化数据库索引失败: {e}")
            return False
    
    def populate_statistics_summary(self):
        """填充统计汇总表数据"""
        try:
            print("开始填充统计汇总表数据...")
            
            # 导入价格管理器
            from price_manager import PriceDataManager
            price_manager = PriceDataManager(self.db_path)
            
            # 清空现有数据
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM statistics_summary")
                conn.commit()
            
            # 生成统计数据的时间范围和配置
            time_configs = [
                ('month', 6), ('month', 12), ('month', 24),
                ('quarter', 8), ('quarter', 12), 
                ('year', 24)
            ]
            
            total_records = 0
            
            for time_range, months in time_configs:
                try:
                    print(f"处理时间范围: {time_range}, 月数: {months}")
                    
                    # 获取详细变动统计（直接调用实时计算方法避免循环）
                    detailed_stats = price_manager._get_detailed_change_statistics_realtime(time_range, months)
                    
                    # 处理新增方法统计
                    if detailed_stats.get('new_methods'):
                        total_records += self._save_statistics_data(
                            'new_methods', time_range, detailed_stats['new_methods'],
                            detailed_stats.get('method_details', {}).get('new_methods', {})
                        )
                    
                    # 处理资质变更统计
                    if detailed_stats.get('qualification_changes'):
                        qual_data = detailed_stats['qualification_changes']
                        method_details = detailed_stats.get('method_details', {}).get('qualification_changes', {})
                        
                        # CMA变更
                        if qual_data.get('cma'):
                            total_records += self._save_statistics_data(
                                'qualification_changes_cma', time_range, qual_data['cma'],
                                method_details.get('cma', {})
                            )
                        
                        # CNAS变更
                        if qual_data.get('cnas'):
                            total_records += self._save_statistics_data(
                                'qualification_changes_cnas', time_range, qual_data['cnas'],
                                method_details.get('cnas', {})
                            )
                        
                        # NHC变更
                        if qual_data.get('nhc'):
                            total_records += self._save_statistics_data(
                                'qualification_changes_nhc', time_range, qual_data['nhc'],
                                method_details.get('nhc', {})
                            )
                    
                    # 处理价格变更统计
                    if detailed_stats.get('price_changes'):
                        total_records += self._save_statistics_data(
                            'price_changes', time_range, detailed_stats['price_changes'],
                            detailed_stats.get('method_details', {}).get('price_changes', {})
                        )
                    
                except Exception as e:
                    print(f"❌ 处理时间范围 {time_range}_{months} 时出错: {e}")
            
            print(f"✅ 统计汇总表数据填充完成，共插入 {total_records} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 填充统计汇总表数据失败: {e}")
            return False
    
    def _save_statistics_data(self, stat_type: str, period_type: str, 
                            period_data: List[Dict], method_details: Dict) -> int:
        """保存统计数据到汇总表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                records_inserted = 0
                
                for item in period_data:
                    period_value = item.get('period')
                    count_value = item.get('count', 0)
                    
                    # 获取对应的方法ID列表
                    method_ids = method_details.get(period_value, [])
                    method_ids_json = json.dumps(method_ids) if method_ids else None
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO statistics_summary
                        (stat_type, period_type, period_value, count_value, method_ids, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        stat_type, period_type, period_value, count_value,
                        method_ids_json, datetime.now()
                    ))
                    
                    records_inserted += 1
                
                conn.commit()
                return records_inserted
                
        except Exception as e:
            print(f"❌ 保存统计数据失败 [{stat_type}]: {e}")
            return 0
    
    def get_statistics_from_summary(self, stat_type: str, period_type: str, 
                                  months: int = None) -> Dict[str, Any]:
        """从汇总表获取统计数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = ["stat_type = ?", "period_type = ?"]
                params = [stat_type, period_type]
                
                # 如果指定了月数，添加时间范围限制
                if months:
                    cutoff_date = datetime.now() - timedelta(days=months * 30)
                    where_conditions.append("period_value >= ?")
                    params.append(cutoff_date.strftime('%Y-%m'))
                
                query = f"""
                    SELECT period_value, count_value, method_ids
                    FROM statistics_summary
                    WHERE {' AND '.join(where_conditions)}
                    ORDER BY period_value
                """
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                # 转换为标准格式
                statistics = []
                method_details = {}
                
                for row in results:
                    period = row['period_value']
                    count = row['count_value']
                    method_ids = json.loads(row['method_ids']) if row['method_ids'] else []
                    
                    statistics.append({
                        'period': period,
                        'count': count,
                        'period_name': self._get_period_name(period_type)
                    })
                    
                    if method_ids:
                        method_details[period] = method_ids
                
                return {
                    'statistics': statistics,
                    'method_details': method_details,
                    'time_range': period_type,
                    'total_periods': len(statistics),
                    'data_source': 'statistics_summary'
                }
                
        except Exception as e:
            print(f"❌ 从汇总表获取统计数据失败: {e}")
            return {}
    
    def _get_period_name(self, period_type: str) -> str:
        """获取时间周期名称"""
        period_names = {
            'year': '年',
            'quarter': '季度',
            'month': '月'
        }
        return period_names.get(period_type, '时间')
    
    def check_summary_data_freshness(self) -> bool:
        """检查汇总数据是否需要更新"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查最近更新时间
                cursor.execute("""
                    SELECT MAX(last_updated) as last_update
                    FROM statistics_summary
                """)
                
                result = cursor.fetchone()
                if not result or not result[0]:
                    return False  # 没有数据，需要更新
                
                last_update = datetime.fromisoformat(result[0])
                now = datetime.now()
                
                # 如果超过6小时没有更新，认为需要刷新
                return (now - last_update).total_seconds() < 6 * 3600
                
        except Exception as e:
            print(f"❌ 检查汇总数据新鲜度失败: {e}")
            return False


def main():
    """主函数"""
    print("开始统计汇总表创建和优化...")
    
    manager = StatisticsSummaryManager()
    
    # 1. 创建统计汇总表
    if not manager.create_statistics_summary_table():
        return False
    
    # 2. 优化数据库索引
    if not manager.optimize_existing_indexes():
        return False
    
    # 3. 填充统计汇总表数据
    if not manager.populate_statistics_summary():
        return False
    
    print("🎉 统计汇总表创建和优化完成！")
    return True


if __name__ == "__main__":
    main()
