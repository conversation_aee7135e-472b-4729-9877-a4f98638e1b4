#!/usr/bin/env python3
"""
标准方法管理模块数据库初始化脚本
"""

import sqlite3
import os
from datetime import datetime

def init_standard_methods_database():
    """初始化标准方法管理数据库表"""
    
    # 数据库文件路径
    db_path = 'kangda_prices.db'
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return False
    
    try:
        # 读取SQL脚本
        with open('standard_methods_schema.sql', 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # 连接数据库并执行脚本
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 分割SQL语句并逐个执行
            statements = sql_script.split(';')
            
            for statement in statements:
                statement = statement.strip()
                if statement:  # 跳过空语句
                    try:
                        cursor.execute(statement)
                        print(f"执行成功: {statement[:50]}...")
                    except sqlite3.Error as e:
                        # 如果表已存在，跳过错误
                        if "already exists" in str(e):
                            print(f"跳过已存在的表: {statement[:50]}...")
                        else:
                            print(f"执行失败: {statement[:50]}...")
                            print(f"错误信息: {e}")
            
            conn.commit()
            print("标准方法管理数据库表初始化完成")
            
            # 验证表是否创建成功
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name LIKE '%standard%' OR name LIKE '%detection%' 
                OR name LIKE '%applicable%' OR name LIKE '%evaluation%'
            """)
            
            tables = cursor.fetchall()
            print(f"创建的表: {[table[0] for table in tables]}")
            
            return True
            
    except FileNotFoundError:
        print("错误：找不到 standard_methods_schema.sql 文件")
        return False
    except Exception as e:
        print(f"初始化数据库时发生错误: {e}")
        return False

def check_standard_methods_tables():
    """检查标准方法管理相关表是否存在"""
    
    db_path = 'kangda_prices.db'
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件 {db_path} 不存在")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查所有相关表
            required_tables = [
                'standard_methods',
                'applicable_scopes', 
                'detection_items',
                'standard_method_scopes',
                'standard_method_items',
                'evaluation_methods',
                'evaluation_recommendations',
                'method_alternatives'
            ]
            
            existing_tables = []
            missing_tables = []
            
            for table in required_tables:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table,))
                
                if cursor.fetchone():
                    existing_tables.append(table)
                else:
                    missing_tables.append(table)
            
            print(f"已存在的表: {existing_tables}")
            if missing_tables:
                print(f"缺失的表: {missing_tables}")
                return False
            else:
                print("所有标准方法管理表都已存在")
                return True
                
    except Exception as e:
        print(f"检查表时发生错误: {e}")
        return False

def insert_sample_data():
    """插入一些示例数据"""
    
    db_path = 'kangda_prices.db'
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 插入示例标准方法
            sample_methods = [
                ('GB', '5085.3', 'GB-5085.3', False, None, False, '危险废物鉴别标准 浸出毒性鉴别'),
                ('HJ', '557', 'HJ-557', False, None, False, '固体废物 浸出毒性浸出方法 水平振荡法'),
                ('GB/T', '15555.12', 'GB/T-15555.12', False, None, False, '固体废物 浸出毒性浸出方法 醋酸缓冲溶液法'),
                ('HJ', '299', 'HJ-299', True, '附录A', True, '固体废物 浸出毒性浸出方法 硫酸硝酸法'),
                ('GB', '15618', 'GB-15618', False, None, False, '土壤环境质量 农用地土壤污染风险管控标准'),
            ]
            
            for method in sample_methods:
                try:
                    cursor.execute("""
                        INSERT OR IGNORE INTO standard_methods 
                        (standard_number, standard_year, full_standard_number, is_appendix_method, 
                         appendix_number, is_informative_appendix, standard_name)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, method)
                except sqlite3.Error as e:
                    print(f"插入标准方法失败: {e}")
            
            # 获取适用范围和检测项目ID
            cursor.execute("SELECT id, name FROM applicable_scopes")
            scopes = dict(cursor.fetchall())
            
            cursor.execute("SELECT id, name FROM detection_items")
            items = dict(cursor.fetchall())
            
            # 为标准方法关联适用范围和检测项目
            cursor.execute("SELECT id, full_standard_number FROM standard_methods")
            methods = cursor.fetchall()
            
            for method_id, method_number in methods:
                if 'GB-5085.3' in method_number or 'HJ-557' in method_number:
                    # 关联固体废物浸出范围
                    scope_id = None
                    for sid, sname in scopes.items():
                        if '固体废物浸出' in sname:
                            scope_id = sid
                            break
                    
                    if scope_id:
                        cursor.execute("""
                            INSERT OR IGNORE INTO standard_method_scopes 
                            (standard_method_id, applicable_scope_id)
                            VALUES (?, ?)
                        """, (method_id, scope_id))
                    
                    # 关联重金属检测项目
                    for item_id, item_name in items.items():
                        if item_name in ['铅', '镉', '汞', '砷', '铬', '六价铬']:
                            cursor.execute("""
                                INSERT OR IGNORE INTO standard_method_items 
                                (standard_method_id, detection_item_id)
                                VALUES (?, ?)
                            """, (method_id, item_id))
            
            conn.commit()
            print("示例数据插入完成")
            
    except Exception as e:
        print(f"插入示例数据时发生错误: {e}")

if __name__ == "__main__":
    print("开始初始化标准方法管理数据库...")
    
    # 检查表是否已存在
    if check_standard_methods_tables():
        print("标准方法管理表已存在，跳过初始化")
    else:
        # 初始化数据库
        if init_standard_methods_database():
            print("数据库初始化成功")
            
            # 插入示例数据
            insert_sample_data()
        else:
            print("数据库初始化失败")
    
    print("初始化完成")
