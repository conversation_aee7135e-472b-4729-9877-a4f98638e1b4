{"permissions": {"allow": ["Bash(\"D:/yuan shi dai ma/康达价格管理/venv/Scripts/python.exe\" -m pip install seaborn matplotlib pandas openpyxl)", "Bash(\"D:/yuan shi dai ma/康达价格管理/venv/Scripts/python.exe\" main.py --mode stats)", "Bash(\"D:/yuan shi dai ma/康达价格管理/venv/Scripts/python.exe\" migrate_database.py)", "<PERSON><PERSON>(python:*)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "mcp__ide__executeCode", "Bash(rm \"D:\\yuan shi dai ma\\康达价格管理\\templates\\data_export.html\")", "<PERSON><PERSON>(find . -name \"*.pyc\" -delete)", "Bash(find . -name \"__pycache__\" -type d -exec rm -rf {} +)", "<PERSON><PERSON>(true)", "Bash(sqlite3 \"D:\\代码\\康达价格管理\\kangda_prices.db\" \"SELECT COUNT(*) FROM evaluation_recommendations;\")"], "deny": []}}