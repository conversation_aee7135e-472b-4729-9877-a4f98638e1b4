-- 别名管理功能数据库迁移脚本
-- 为检测项目表添加别名管理字段

-- 1. 为检测项目表添加别名相关字段
ALTER TABLE detection_items ADD COLUMN aliases TEXT;
ALTER TABLE detection_items ADD COLUMN show_aliases_in_list BOOLEAN DEFAULT FALSE;

-- 2. 创建别名管理相关的索引
CREATE INDEX IF NOT EXISTS idx_detection_items_aliases ON detection_items(aliases);

-- 3. 初始化一些示例别名数据
-- 为现有的检测项目添加常用别名

UPDATE detection_items SET aliases = '["Pb"]' WHERE name = '铅';
UPDATE detection_items SET aliases = '["Cd"]' WHERE name = '镉';
UPDATE detection_items SET aliases = '["Hg"]' WHERE name = '汞';
UPDATE detection_items SET aliases = '["As"]' WHERE name = '砷';
UPDATE detection_items SET aliases = '["Cr"]' WHERE name = '铬';
UPDATE detection_items SET aliases = '["Cr⁶⁺", "Cr(VI)", "Cr6+"]' WHERE name = '六价铬';
UPDATE detection_items SET aliases = '["Cu"]' WHERE name = '铜';
UPDATE detection_items SET aliases = '["Zn"]' WHERE name = '锌';
UPDATE detection_items SET aliases = '["Ni"]' WHERE name = '镍';
UPDATE detection_items SET aliases = '["TP"]' WHERE name = '总磷';
UPDATE detection_items SET aliases = '["TN"]' WHERE name = '总氮';
UPDATE detection_items SET aliases = '["COD"]' WHERE name = '化学需氧量';
UPDATE detection_items SET aliases = '["BOD₅", "BOD5"]' WHERE name = '生化需氧量';
UPDATE detection_items SET aliases = '["SS"]' WHERE name = '悬浮物';
UPDATE detection_items SET aliases = '["pH"]' WHERE name = 'pH值';
UPDATE detection_items SET aliases = '["DO"]' WHERE name = '溶解氧';

-- 4. 创建别名管理相关的视图
CREATE VIEW IF NOT EXISTS detection_items_with_aliases AS
SELECT 
    id,
    name,
    display_name,
    chemical_formula,
    cas_number,
    category,
    aliases,
    show_aliases_in_list,
    CASE 
        WHEN aliases IS NOT NULL AND aliases != '' THEN 
            json_array_length(aliases)
        ELSE 0 
    END as alias_count,
    created_at
FROM detection_items;

-- 5. 创建别名搜索的辅助函数（通过触发器实现）
-- 注意：SQLite不支持自定义函数，所以我们在应用层处理别名搜索

-- 6. 添加注释说明
-- aliases字段存储格式：JSON数组，例如：["别名1", "别名2", "别名3"]
-- show_aliases_in_list字段：控制是否在标准方法管理界面的检测项目列表中显示别名
