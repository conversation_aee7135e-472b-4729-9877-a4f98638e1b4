import os
import json
import time
import random
import requests
from datetime import datetime, timedelta
import openpyxl
import traceback # Import traceback for detailed error logging
import hashlib # Import hashlib for MD5 encryption
from urllib.parse import urlencode
import urllib.parse # 添加urllib.parse引用

# 有条件导入pandas
try:
    import pandas as pd
except ImportError:
    pd = None

# 有条件导入python-docx
try:
    from docx import Document
except ImportError:
    print("警告: 未找到 'python-docx' 库。维护记录日期筛选功能将不可用。")
    print("请运行 'pip install python-docx' 来安装。")
    Document = None

# 有条件导入PyPDF2
try:
    from PyPDF2 import PdfMerger
except ImportError:
    try:
        # 尝试旧版本的导入方式
        from PyPDF2 import PdfFileMerger as PdfMerger
    except ImportError:
        print("警告: 未找到 'PyPDF2' 库。PDF合并功能将不可用。")
        print("请运行 'pip install PyPDF2' 来安装。")
        PdfMerger = None # 设置为 None 以便稍后检查

class HttpRequestManager:
    """处理HTTP请求的管理类 (Refactored based on guide)"""

    def __init__(self):
        self.base_url = "http://*************/ehscare"
        self.session = requests.Session()

        # 明确禁用代理设置，避免代理连接问题
        self.session.proxies = {
            'http': None,
            'https': None
        }

        self._set_default_headers()

        # 创建调试目录
        # Use a relative path from the script location if possible, or ensure the main script defines it
        try:
            # Try to get script dir, fallback to current working dir
            script_dir = os.path.dirname(os.path.abspath(__file__))
        except NameError:
            script_dir = os.getcwd() # Fallback if __file__ is not defined (e.g., interactive)

        self.debug_dir = os.path.join(script_dir, "debug")
        if not os.path.exists(self.debug_dir):
             try:
                 os.makedirs(self.debug_dir)
             except OSError as e:
                  print(f"警告: 无法创建调试目录 {self.debug_dir}: {e}")
                  self.debug_dir = script_dir # Fallback to script/working dir

    @property
    def cookies(self):
        """提供对会话cookie的直接访问"""
        return self.session.cookies

    def _set_default_headers(self):
        """设置一个最小化的默认请求头集合，完全匹配用户提供的请求格式"""
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Content-Type": "application/json;charset=UTF-8",
            "Host": "*************",
            "Referer": "http://*************/"
        })

    def get_captcha_image(self):
        """获取验证码图片和关联的JSESSIONID
        
        Returns:
            tuple: (是否成功, 图片数据, jsessionid, 消息)
        """
        try:
            timestamp = int(time.time() * 1000)
            captcha_url = f"{self.base_url}/system/verifycode.jpg?width=180&height=40&t={timestamp}"
            self._log(f"获取验证码: {captcha_url}")
            
            # 使用临时会话获取验证码，确保JSESSIONID是干净的
            temp_session = requests.Session()
            temp_session.headers.update(self.session.headers)
            temp_session.headers['Referer'] = self.base_url + "/"

            response = temp_session.get(captcha_url, timeout=30)
            response.raise_for_status()
            
            jsessionid = temp_session.cookies.get('JSESSIONID')
            image_data = response.content
            
            if image_data and jsessionid:
                self._log(f"成功获取验证码图片，JSESSIONID: {jsessionid}")
                return True, image_data, jsessionid, "验证码获取成功"
            else:
                self._log("验证码图片为空或未获取到JSESSIONID")
                return False, None, None, "验证码图片为空或未获取到JSESSIONID"
                
        except requests.RequestException as e:
            error_msg = f"获取验证码失败: {str(e)}"
            self._log(error_msg)
            return False, None, None, error_msg
        except Exception as e:
            error_msg = f"获取验证码时发生未知错误: {str(e)}"
            self._log(error_msg)
            return False, None, None, error_msg

    def login(self, username, password, captcha, captcha_jsessionid=None):
        """通过账号密码登录获取cookie
        
        Args:
            username: 用户名
            password: 密码
            captcha: 验证码
            captcha_jsessionid: 获取验证码时得到的JSESSIONID

        Returns:
            tuple: (success, message, user_info)
        """
        if not captcha_jsessionid:
            return False, "登录失败，缺少验证码会话ID", None

        try:
            login_url = f"{self.base_url}/pms/login"
            
            # 1. 对密码进行MD5加密 (关键修复)
            password_md5 = hashlib.md5(password.encode('utf-8')).hexdigest()

            # 2. 准备登录数据，使用正确的键名和`autoLogin`字段
            login_data = {
                "userName": username if username else "15150417135",
                "password": password_md5, # 发送加密后的密码
                "verifyCode": captcha,
                "autoLogin": False # 添加autoLogin字段
            }

            self._log(f"开始登录，用户名: {login_data['userName']}")
            self._log(f"登录URL: {login_url}")
            self._log(f"登录数据 (MD5): {login_data}")

            # 3. 创建一个临时的、干净的登录会话
            login_session = requests.Session()
            
            # 为登录API调用设置一个干净、专用的请求头
            login_headers = {
                "User-Agent": self.session.headers.get("User-Agent"),
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json;charset=UTF-8",
                "Origin": "http://*************",
                "Referer": "http://*************/",
            }
            login_session.headers.update(login_headers)
            login_session.cookies.set("JSESSIONID", captcha_jsessionid)

            response = login_session.post(login_url, json=login_data, timeout=30)
            response.raise_for_status()

            result = response.json()
            self._log(f"登录响应内容: {result}")
            
            # 保存详细的调试信息到文件
            debug_info = {
                "request_url": login_url,
                "request_headers": dict(response.request.headers),
                "request_body": login_data,
                "response_status": response.status_code,
                "response_headers": dict(response.headers),
                "response_body": result,
                "response_cookies": [{"name": c.name, "value": c.value} for c in response.cookies]
            }

            debug_file = os.path.join(self.debug_dir, f"login_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            try:
                with open(debug_file, 'w', encoding='utf-8') as f:
                    json.dump(debug_info, f, ensure_ascii=False, indent=2)
                self._log(f"调试信息已保存到: {debug_file}")
            except Exception as e:
                self._log(f"保存调试信息失败: {e}")

            # 检查登录是否成功
            if result.get("rc") == 0 and "ret" in result:
                user_info = result["ret"]
                self._log("登录成功，正在更新主会话COOKIE")

                # 4. 关键修复：确保JSESSIONID被正确转移
                # 首先，从登录响应中获取新的JSESSIONID
                if 'JSESSIONID' in response.cookies:
                    new_jsessionid = response.cookies['JSESSIONID']
                    # 将其设置到主会话中
                    self.session.cookies.set('JSESSIONID', new_jsessionid)
                    self._log(f"已将新的 JSESSIONID: {new_jsessionid[:15]}... 更新到主会话")
                else:
                    # 如果响应中没有JSESSIONID，使用原始的captcha_jsessionid
                    self.session.cookies.set('JSESSIONID', captcha_jsessionid)
                    self._log(f"使用原始的 captcha_jsessionid 作为 JSESSIONID: {captcha_jsessionid[:15]}...")

                # a. 先用HTTP响应头中的`Set-Cookie`更新 (保留这一步以防有其他cookie)
                for cookie in response.cookies:
                    if cookie.name != 'JSESSIONID':  # 跳过JSESSIONID，因为我们已经单独处理了
                        self.session.cookies.set(cookie.name, cookie.value)
                        self._log(f"从响应中设置Cookie: {cookie.name} = {cookie.value[:30]}...")
                
                # b. 再用响应体中的用户信息模拟JS行为更新Cookie
                user_cookies = {
                    "userId": str(user_info.get("userId", "")),
                    "userName": user_info.get("userName", ""),
                    "nickName": user_info.get("staffName", ""),
                    "departmentId": str(user_info.get("departmentId", "")),
                    "departmentName": user_info.get("departmentName", ""),
                    "isSuperManager": str(user_info.get("isSuperManager", 0)),
                    "isDepManager": str(user_info.get("isDepManager", 0)),
                    "customerId": str(user_info.get("customerId", "null")),
                    "userType": user_info.get("userType", "user"),
                    "companyIdentity": str(user_info.get("companyIdentity", 1)),
                    "loginSubtractCompanyToken": user_info.get("loginSubtractCompanyToken", ""),
                    "phone": user_info.get("userName", "")
                }
                # 使用安全的方法添加包含中文字符的Cookie，不重新创建session
                self._add_cookies_safely(user_cookies)

                # 验证最终的Cookie状态（安全方式）
                final_count = len(list(self.session.cookies))
                self._log(f"最终Cookie列表:")
                for cookie in self.session.cookies:
                    display_value = cookie.value[:30] + '...' if len(cookie.value) > 30 else cookie.value
                    self._log(f"  {cookie.name}: {display_value}")

                self._log(f"主会话Cookie已更新，总数: {final_count}")

                return True, "登录成功", user_info
            else:
                error_msg = result.get("err", result.get("msg", "登录失败"))
                self._log(f"登录失败: {error_msg}")
                return False, error_msg, None

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self._log(error_msg)
            return False, error_msg, None
        except json.JSONDecodeError as e:
            error_msg = f"响应解析失败: {str(e)}"
            self._log(error_msg)
            return False, error_msg, None
        except Exception as e:
            error_msg = f"登录过程中发生错误: {str(e)}"
            self._log(error_msg)
            return False, error_msg, None

    def _add_cookies_safely(self, new_cookies):
        """安全地添加Cookie到现有session，处理中文字符编码

        Args:
            new_cookies: 新的Cookie字典
        """
        try:
            # 记录添加前的Cookie状态（安全方式）
            existing_count = len(list(self.session.cookies))
            self._log(f"添加前的Cookie数量: {existing_count}")
            
            # 保存现有的JSESSIONID（如果有的话）
            jsessionid = None
            for cookie in self.session.cookies:
                if cookie.name == 'JSESSIONID':
                    jsessionid = cookie.value
                    self._log(f"保存现有的JSESSIONID: {jsessionid[:15]}...")
                    break

            cookie_count = 0
            for name, value in new_cookies.items():
                if value is not None and value != "":
                    value_str = str(value)

                    # 强制确保Cookie值是ASCII兼容的
                    safe_name = name
                    safe_value = value_str

                    # 检查并处理Cookie名称
                    try:
                        safe_name.encode('ascii')
                    except UnicodeEncodeError:
                        safe_name = urllib.parse.quote(name, safe='')
                        self._log(f"Cookie名称包含非ASCII字符，已编码: {name} -> {safe_name}")

                    # 检查并处理Cookie值
                    try:
                        safe_value.encode('ascii')
                    except UnicodeEncodeError:
                        safe_value = urllib.parse.quote(value_str, safe='')
                        self._log(f"Cookie值包含非ASCII字符，已编码: {name}={value_str} -> {safe_value}")

                    # 在设置新Cookie前，先删除可能存在的同名Cookie
                    # 这样可以避免重复Cookie的问题
                    try:
                        # 尝试删除现有的同名Cookie，但跳过JSESSIONID
                        for cookie in list(self.session.cookies):
                            if cookie.name == safe_name and cookie.name != 'JSESSIONID':
                                self.session.cookies.clear(cookie.domain, cookie.path, cookie.name)
                                self._log(f"已删除重复Cookie: {safe_name}")
                    except Exception as e:
                        # 如果删除失败，记录但继续
                        self._log(f"删除重复Cookie时出错: {str(e)}")

                    # 使用requests.cookies.set方法，它会自动处理编码
                    if safe_name != 'JSESSIONID':  # 不覆盖JSESSIONID
                        self.session.cookies.set(safe_name, safe_value)
                        cookie_count += 1

                        # 截断长token的日志输出
                        log_value = (safe_value[:50] + '...') if len(safe_value) > 50 else safe_value
                        self._log(f"已安全设置Cookie [{cookie_count}]: {safe_name} = {log_value}")

            # 如果之前有JSESSIONID，确保它仍然存在
            if jsessionid:
                self.session.cookies.set('JSESSIONID', jsessionid)
                self._log(f"已恢复JSESSIONID: {jsessionid[:15]}...")

            # 验证重要Cookie是否存在（安全方式）
            final_count = len(list(self.session.cookies))
            self._log(f"安全添加了 {cookie_count} 个Cookie，当前总数: {final_count}")

            # 检查关键Cookie（安全方式）
            jsessionid_found = False
            for cookie in self.session.cookies:
                if cookie.name == "JSESSIONID":
                    jsessionid_found = True
                    self._log(f"[OK] JSESSIONID已保留: {cookie.value[:20]}...")
                    break

            if not jsessionid_found:
                self._log("[WARN] 警告: JSESSIONID缺失")

            return True

        except Exception as e:
            self._log(f"安全添加Cookie时出错: {str(e)}")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}")
            return False

    def update_cookies(self, new_cookies):
        """更新会话的Cookie，确保正确应用

        Args:
            new_cookies: 新的Cookie字典
        """
        try:
            # 记录更新前的Cookie情况
            try:
                old_cookies = dict(self.session.cookies)
            except Exception:
                old_cookies = {}
            self._log(f"更新前的Cookie: {old_cookies}")

            # 保存重要的配置
            old_headers = dict(self.session.headers)

            # 创建一个全新的session来确保干净的状态
            self._log("创建新的session以确保Cookie状态干净...")
            self.session = requests.Session()
            self.session.headers.update(old_headers)

            # 逐个添加cookie到新session，确保所有值都是ASCII兼容的
            cookie_count = 0
            for name, value in new_cookies.items():
                if value is not None and value != "":
                    value_str = str(value)

                    # 强制确保Cookie值是ASCII兼容的
                    # 对于已经URL编码的值（如nickName），保持原样
                    # 对于包含中文的值，进行URL编码
                    safe_name = name
                    safe_value = value_str

                    # 检查并处理Cookie名称
                    try:
                        safe_name.encode('ascii')
                    except UnicodeEncodeError:
                        safe_name = urllib.parse.quote(name, safe='')
                        self._log(f"Cookie名称包含非ASCII字符，已编码: {name} -> {safe_name}")

                    # 检查并处理Cookie值
                    try:
                        safe_value.encode('ascii')
                    except UnicodeEncodeError:
                        safe_value = urllib.parse.quote(value_str, safe='')
                        self._log(f"Cookie值包含非ASCII字符，已编码: {name}={value_str} -> {safe_value}")

                    # 使用requests.cookies.set方法，它会自动处理编码
                    self.session.cookies.set(safe_name, safe_value)
                    cookie_count += 1

                    # 截断长token的日志输出
                    log_value = (safe_value[:50] + '...') if len(safe_value) > 50 else safe_value
                    self._log(f"已设置Cookie [{cookie_count}]: {safe_name} = {log_value}")

            # 验证更新是否成功
            current_cookies = dict(self.session.cookies)
            self._log(f"更新后的Cookie总数: {len(current_cookies)}")

            # 检查是否包含关键Cookie
            required_cookies = ["JSESSIONID", "loginSubtractCompanyToken"]
            missing_cookies = [field for field in required_cookies if field not in current_cookies]

            if not missing_cookies:
                self._log("Cookie更新成功：包含所有必要字段")
                return True
            else:
                self._log(f"警告：Cookie更新后缺少关键字段: {missing_cookies}")
                return False

        except Exception as e:
            self._log(f"更新Cookie时出错: {str(e)}")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}")
            return False

    def _cleanup_duplicate_cookies(self):
        """清理重复的Cookie"""
        try:
            self._log("开始清理重复的Cookie...")
            
            # 收集所有Cookie，确保每个名称只保留一个值
            unique_cookies = {}
            duplicate_count = 0
            
            for cookie in self.session.cookies:
                if cookie.name in unique_cookies:
                    self._log(f"发现重复Cookie: {cookie.name}")
                    duplicate_count += 1
                # 总是保留最新的值
                unique_cookies[cookie.name] = cookie.value
                self._log(f"收集Cookie: {cookie.name} = {cookie.value[:50]}...")
            
            if duplicate_count > 0:
                self._log(f"发现 {duplicate_count} 个重复Cookie，正在清理...")
                
                # 保存重要的配置
                old_headers = dict(self.session.headers)
                
                # 创建新的session
                self.session = requests.Session()
                self.session.headers.update(old_headers)
                
                # 重新设置Cookie，确保每个名称只有一个值
                for name, value in unique_cookies.items():
                    self.session.cookies.set(name, str(value))
                    
                self._log(f"清理完成，当前Cookie数量: {len(unique_cookies)}")
                
                # 验证清理结果
                final_cookies = {}
                for cookie in self.session.cookies:
                    if cookie.name in final_cookies:
                        self._log(f"错误：清理后仍然存在重复Cookie: {cookie.name}")
                        return False
                    final_cookies[cookie.name] = cookie.value
                
                self._log("Cookie清理成功，无重复Cookie")
                return True
            else:
                self._log("未发现重复Cookie，无需清理")
                return True
                
        except Exception as e:
            self._log(f"清理重复Cookie时出错: {str(e)}")
            import traceback
            self._log(f"错误详情: {traceback.format_exc()}")
            return False

    def _log(self, message):
        """记录日志消息

        Args:
            message: 要记录的日志消息
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        # 将日志写入文件 (Handle potential write errors)
        try:
             # Check if debug_dir exists and is writable
             if os.path.exists(self.debug_dir) and os.access(self.debug_dir, os.W_OK):
                 log_file = os.path.join(self.debug_dir, "http_request.log")
                 with open(log_file, 'a', encoding='utf-8') as f:
                      f.write(log_message + '\\n')
             else:
                  print(f"警告: 调试目录 {self.debug_dir} 不存在或不可写。日志仅输出到控制台。")
        except Exception as e:
            print(f"警告: 写入日志文件失败: {e}")

    def get_device_info(self, device_params):
        """获取设备信息"""
        url = f"{self.base_url}/applianceWorkStatus/pageListByApplianceId"

        # 添加随机延时
        self._random_delay()

        try:
            response = self.session.get(url, params=device_params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"获取设备信息失败: {str(e)}")
            return None

    def download_device_record(self, params, save_path):
        """下载设备使用记录"""
        url = f"{self.base_url}/exportDoc/useRecord"

        # 添加随机延时
        self._random_delay()

        print(f"开始下载设备记录: {params}")
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")

        try:
            # 较长的请求，需要增加超时
            response = self.session.get(url, params=params, timeout=120)
            response.raise_for_status()

            # 记录响应内容的前50个字符（用于调试）
            print(f"响应状态码: {response.status_code}")
            content_type = response.headers.get('Content-Type', '')
            print(f"响应Content-Type: {content_type}")

            # 先检查是否是JSON响应
            if content_type.startswith('application/json') or content_type.startswith('text/plain'):
                try:
                    json_content = response.json()
                    print(f"JSON响应: {json.dumps(json_content, ensure_ascii=False)[:200]}...")

                    # 首先明确检查是否会话超时
                    if 'ret' in json_content and isinstance(json_content.get('ret'), dict) and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        print("登录已超时，请更新Cookie信息")
                        return None, 2, "登录已超时，请更新Cookie信息"

                    # 保存JSON响应到调试文件
                    debug_json_path = os.path.join(self.debug_dir, f"debug_json_{params.get('applianceId')}.json")
                    with open(debug_json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_content, f, ensure_ascii=False, indent=2)

                    # 检查是否有错误信息
                    if 'rc' in json_content and json_content['rc'] != 0:
                        print(f"服务器返回错误: {json_content}")
                        return None, 2, f"服务器返回错误: {json_content.get('msg', '未知错误')}"

                    # 其他未知的JSON响应，视为错误
                    return None, 2, "服务器返回未知的JSON响应"

                except json.JSONDecodeError as json_err:
                    print(f"无法解析服务器返回的JSON: {json_err}")

                    # 检查响应文本中是否包含超时信息
                    if "ifTimeOut" in response.text or "已超时" in response.text:
                        return None, 2, "登录已超时，请更新Cookie信息"

                    return None, 2, f"无法解析服务器返回的JSON: {response.text[:500]}..."
            else:
                # 检查是否为HTML响应但包含超时信息
                if 'html' in content_type.lower() or '<html' in response.text[:100].lower():
                    if "ifTimeOut" in response.text or "已超时" in response.text:
                        print("检测到HTML响应中包含登录超时信息")
                        return None, 2, "登录已超时，请更新Cookie信息"

                # 假设是文件流
                print("响应Content-Type不是JSON，尝试作为文件下载")

                # 尝试从Content-Disposition头获取文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                filename = None

                if 'filename=' in content_disposition:
                    # 提取文件名
                    try:
                        filename = content_disposition.split('filename=')[1]
                        # 处理可能的引号
                        if filename.startswith('"') and filename.endswith('"'):
                            filename = filename[1:-1]
                        # 处理文件名编码
                        filename = self._sanitize_filename(filename)
                        print(f"从Content-Disposition提取的文件名: {filename}")
                    except Exception as e:
                        print(f"解析Content-Disposition时出错: {str(e)}")

                # 如果无法从Content-Disposition获取文件名，使用自定义名称
                if not filename:
                    device_name = params.get('name', 'unknown')
                    device_id = params.get('applianceId', 'unknown')
                    start_date = params.get('startDate', '').replace('-', '')
                    end_date = params.get('endDate', '').replace('-', '')
                    filename = f"{device_name}_{device_id}_{start_date}至{end_date}.docx"
                    filename = self._sanitize_filename(filename)

                # 构建完整的文件路径
                file_path = os.path.join(save_path, filename)
                print(f"保存文件到: {file_path}")

                # 完整写入文件流
                try:
                    with open(file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk: # 过滤掉 keep-alive 新块
                                f.write(chunk)
                except Exception as write_err:
                    print(f"写入文件时出错: {write_err}")
                    return None, 2, f"写入文件时出错: {str(write_err)}"

                # --- ADDED: Check for 0KB file ---
                try:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        if file_size == 0:
                            self._log(f"文件大小为 0KB: {file_path}. 删除文件并标记为无记录。")
                            os.remove(file_path)
                            return None, 1, "服务器返回空文件" # Status 1: No Records
                    else:
                         # This case should ideally not happen if writing succeeded, but handle defensively
                         self._log(f"警告: 文件写入后未找到: {file_path}")
                         return None, 2, "文件写入后丢失"
                except OSError as e:
                     self._log(f"检查文件大小或删除 0KB 文件时出错: {e}")
                     return None, 2, f"处理文件时出错: {e}"
                # --- END ADDED ---

                # 检查文件大小
                try:
                    file_size = os.path.getsize(file_path)
                    print(f"文件大小: {file_size} 字节")
                except OSError as size_err:
                    print(f"检查文件大小时出错: {size_err}")
                    # 文件可能未能成功创建
                    return None, 2, f"无法检查文件大小: {str(size_err)}"

                if file_size == 0:
                    print("警告: 保存的文件大小为0")
                    # 0字节文件通常表示没有使用记录，而不是下载错误
                    return None, 1, "所选时间范围内无使用记录"

                # 检查文件内容的前几个字节来确定是否为Word文档
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(8)
                except IOError as read_err:
                    print(f"读取文件头时出错: {read_err}")
                    return None, 2, f"无法读取文件头: {str(read_err)}"

                # Word文档(docx)的幻数为: PK\x03\x04 (ZIP文件格式)
                # 旧版Word文档(doc)的幻数为: D0 CF 11 E0 A1 B1 1A E1
                if not (header.startswith(b'PK\\x03\\x04') or header.startswith(b'\\xD0\\xCF\\x11\\xE0')):
                    print(f"警告: 文件头部不是标准Word文档格式: {header.hex()}")
                    # 尝试读取更多内容判断是否为HTML
                    try:
                        with open(file_path, 'rb') as f:
                            content_sample = f.read(1024) # 读取前1KB
                        if b'<html' in content_sample or b'<!DOCTYPE HTML' in content_sample.upper():
                            print(f"警告: 文件内容看起来是HTML而不是Word文档")
                            # 保存一个HTML版本用于调试
                            html_path = os.path.join(self.debug_dir, f"{device_name}_{device_id}_{params.get('startDate')}_{params.get('endDate')}.html")
                            try:
                                os.rename(file_path, html_path) # 尝试重命名
                                print(f"将文件重命名并保存HTML内容到: {html_path}")
                            except OSError as rename_err:
                                print(f"重命名文件失败: {rename_err}. 尝试复制...")
                                try:
                                    with open(html_path, 'wb') as hf, open(file_path, 'rb') as sf:
                                        hf.write(sf.read())
                                    print(f"复制HTML内容到: {html_path}")
                                except Exception as copy_err:
                                     print(f"复制HTML文件失败: {copy_err}")
                            # 可能是错误页面，如登录超时
                            return None, 2, "响应内容是HTML而不是Word文档，可能是登录已超时"
                    except IOError as read_html_err:
                         print(f"读取文件内容判断HTML时出错: {read_html_err}")

                # 成功下载文件 (或至少看起来是文件)
                return file_path, 0, "成功"

        except requests.RequestException as e:
            print(f"下载设备记录失败: {str(e)}")
            # 保存错误信息到调试文件
            debug_error_path = os.path.join(self.debug_dir, f"debug_error_{params.get('applianceId')}.txt")
            with open(debug_error_path, 'w', encoding='utf-8') as f:
                f.write(f"Error: {str(e)}\\n")
                f.write(f"URL: {url}\\n")
                f.write(f"Params: {params}\\n")
            return None, 2, f"下载设备记录失败: {str(e)}"
        except Exception as e:
            print(f"处理响应时发生错误: {str(e)}")
            return None, 2, f"处理响应时发生错误: {str(e)}"

    def get_verify_id_by_appliance_no(self, appliance_no):
        """根据仪器编号获取检定ID

        Args:
            appliance_no: 仪器编号，如X-029-13

        Returns:
            str: 检定ID，如果未找到则返回None
        """
        try:
            # 构建URL
            url = f"{self.base_url}/applianceVerify/pageList"

            # 构建参数
            params = {
                'applianceNo': appliance_no,
                'name': '',
                'model': '',
                'warnDays': '',
                'page': 1,
                'rows': 20,
                'type': 1
            }

            self._log(f"获取仪器 {appliance_no} 的检定ID")
            self._log(f"请求URL: {url}")
            self._log(f"请求参数: {params}")

            # 构建完整URL并打印
            from urllib.parse import urlencode
            full_url = f"{url}?{urlencode(params)}"
            self._log(f"完整请求URL: {full_url}")

            # 添加随机延时
            self._random_delay()

            # 发送请求
            response = self.session.get(
                url,
                params=params,
                headers=self.session.headers,
                timeout=30
            )

            self._log(f"实际请求URL: {response.url}")
            response.raise_for_status()
            data = response.json()

            self._log(f"获取检定ID响应: {data}")

            # 检查响应是否有效
            if 'rc' in data and data['rc'] == 0 and 'ret' in data and 'rows' in data['ret']:
                rows = data['ret']['rows']

                # 精确匹配仪器编号
                for row in rows:
                    if row.get('applianceNo') == appliance_no:
                        verify_id = str(row.get('id'))
                        appliance_id = str(row.get('applianceId'))
                        self._log(f"找到仪器 {appliance_no} 的检定ID: {verify_id}, 仪器ID: {appliance_id}")
                        return verify_id

                self._log(f"未找到仪器 {appliance_no} 的精确匹配记录")
                return None
            else:
                self._log(f"获取检定ID失败: {data.get('err', '未知错误')}")
                return None

        except Exception as e:
            self._log(f"获取检定ID时出错: {str(e)}")
            return None

    def _random_delay(self):
        """添加随机延时以避免请求过快"""
        delay = random.uniform(0.5, 2.0)
        time.sleep(delay)

    def _sanitize_filename(self, filename, file_type='usage', device_id_or_no=None):
        """确保文件名是安全的，去除非法字符并处理编码

        Args:
            filename: 原始文件名
            file_type: 文件类型，可选值: 'loan' (出入库记录), 'lab' (实验室记录), 'usage' (使用记录)
            device_id_or_no: 设备ID或编号，用于在备用文件名中包含设备标识
        """
        try:
            # 尝试解码可能的URL编码
            filename = requests.utils.unquote(filename)

            # 尝试检测并转换编码
            try:
                # 如果filename是字节串，尝试解码
                if isinstance(filename, bytes):
                    try:
                        filename = filename.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            filename = filename.decode('gbk')
                        except UnicodeDecodeError:
                            # 最后尝试忽略错误用utf-8
                            filename = filename.decode('utf-8', errors='ignore')
                else:
                    # 如果是字符串，尝试检测是否有错误编码的utf-8或gbk
                    try:
                        filename_bytes = filename.encode('utf-8')
                        filename = filename_bytes.decode('utf-8')
                    except (UnicodeEncodeError, UnicodeDecodeError):
                        try:
                            filename_bytes = filename.encode('gbk')
                            filename = filename_bytes.decode('gbk')
                        except (UnicodeEncodeError, UnicodeDecodeError):
                            pass
            except Exception:
                # 如果所有编码尝试都失败，使用原始文件名
                pass

            # 替换Windows文件名中不允许的字符
            invalid_chars = '<>:"/\\|?*' # Corrected invalid_chars for Windows
            for char in invalid_chars:
                filename = filename.replace(char, '_')

            # --- 统一尝试将 Latin-1 误解码的字符串还原为 UTF-8 ---
            try:
                filename = filename.encode('latin1').decode('utf-8')
            except (UnicodeEncodeError, UnicodeDecodeError):
                # 若转换失败（包含无法用 latin1 表示的字符），保持原样并在后续流程尝试 GBK 等
                pass

            # 确保文件名不过长
            if len(filename) > 150:
                base, ext = os.path.splitext(filename)
                filename = base[:146] + ext

            return filename
        except Exception as e:
            print(f"处理文件名时出错: {str(e)}")
            # 如果处理失败，生成备用文件名
            timestamp = int(time.time())

            # 仅对出入库记录使用特定格式
            if file_type == 'loan':
                device_str = f"_{device_id_or_no}" if device_id_or_no else ""
                return f"LOAN{device_str}_仪器出入库记录_{timestamp}.xls"
            else:
                # 其他类型使用通用备用格式
                return f"device_record_{timestamp}.docx"

    def download_lab_record(self, params, save_path):
        """下载实验室仪器使用记录

        针对大数据量实验室记录增加了超时和重试机制，
        提高下载稳定性。
        """
        url = f"{self.base_url}/exportDoc/useRecord"

        # 添加随机延时
        self._random_delay()

        print(f"开始下载实验室仪器记录: {params}") # 修改日志
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")

        # 设置最大重试次数和超时时间
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 重试时增加延时
                if retry_count > 0:
                    delay = 1 + retry_count * 2 # 递增延时：3秒，5秒
                    print(f"第 {retry_count} 次重试，等待 {delay} 秒...")
                    time.sleep(delay)

                # 增加超时时间，防止大数据量下载超时
                timeout = 180 + retry_count * 60 # 递增超时：3分钟，4分钟，5分钟
                response = self.session.get(url, params=params, timeout=timeout)
                response.raise_for_status() # 如果状态码不是200，这里会抛出异常

                print(f"响应状态码: {response.status_code}")
                content_type = response.headers.get('Content-Type', '')
                print(f"响应Content-Type: {content_type}")

                # 先检查是否是JSON响应
                if content_type.startswith('application/json') or content_type.startswith('text/plain'):
                    try:
                        json_content = response.json()
                        print(f"JSON响应: {json.dumps(json_content, ensure_ascii=False)[:200]}...")

                        # 首先明确检查是否会话超时
                        if 'ret' in json_content and isinstance(json_content.get('ret'), dict) and json_content.get('ret', {}).get('ifTimeOut') == "1":
                            print("登录已超时，请更新Cookie信息")
                            return None, 2, "登录已超时，请更新Cookie信息"

                        # 保存JSON响应到调试文件
                        debug_json_path = os.path.join(self.debug_dir, f"debug_json_lab_{int(time.time())}.json")
                        with open(debug_json_path, 'w', encoding='utf-8') as f:
                            json.dump(json_content, f, ensure_ascii=False, indent=2)

                        # 检查是否有错误信息
                        if 'rc' in json_content and json_content['rc'] != 0:
                            print(f"服务器返回错误: {json_content}")
                            if "departmentId" in str(json_content):
                                return None, 2, f"部门ID错误：{json_content.get('msg', '未知错误')}，请检查Cookie中的部门权限"
                            else:
                                return None, 2, f"服务器返回错误: {json_content.get('msg', '未知错误')}"

                        # 其他未知的JSON响应，视为错误 (或者无记录? 根据实际情况调整)
                        # 暂时视为无记录
                        print("服务器返回未知的JSON响应，可能无记录")
                        return None, 1, "服务器返回未知的JSON响应，可能无记录"

                    except json.JSONDecodeError as json_err:
                        print(f"无法解析服务器返回的JSON: {json_err}")
                        # 记录原始文本用于调试
                        raw_text = response.text[:500] # 获取前500字符
                        print(f"原始响应文本 (前500字符): {raw_text}")
                        # 检查响应文本中是否包含超时信息
                        if "ifTimeOut" in response.text or "已超时" in response.text:
                            return None, 2, "登录已超时，请更新Cookie信息"

                        return None, 2, f"无法解析服务器返回的JSON: {raw_text}..."
                else:
                    # *** 修改日志和检查 ***
                    print(f"响应不是预期的JSON/Text格式。")
                    print(f"完整响应头: {response.headers}")
                    raw_text_sample = response.text[:200] # 查看响应体样本
                    print(f"响应体样本 (前200字符): {raw_text_sample}")

                    # 检查是否为HTML响应但包含超时信息
                    if 'html' in content_type.lower() or '<html' in response.text[:100].lower():
                        if "ifTimeOut" in response.text or "已超时" in response.text:
                            print("检测到HTML响应中包含登录超时信息")
                            return None, 2, "登录已超时，请更新Cookie信息"

                    # *** 修改判断逻辑：主要检查 Content-Disposition ***
                    content_disposition = response.headers.get('Content-Disposition', '')
                    is_file_attachment = 'attachment' in content_disposition.lower() and 'filename=' in content_disposition.lower()

                    if is_file_attachment:
                        # 确认是文件附件，继续处理
                        print("检测到 Content-Disposition 表明是文件附件，尝试下载 (LAB)")

                        filename = None
                        # 尝试从Content-Disposition头获取文件名
                        if 'filename=' in content_disposition:
                            try:
                                filename_part = content_disposition.split('filename=')[1]
                                # 处理可能的引号和编码
                                if filename_part.startswith('"') and filename_part.endswith('"'):
                                    filename_part = filename_part[1:-1]
                                filename = self._sanitize_filename(filename_part, file_type='lab')
                                print(f"从Content-Disposition提取的文件名 (LAB): {filename}")
                            except Exception as e:
                                print(f"解析Content-Disposition时出错 (LAB): {str(e)}")

                        # 如果无法从Content-Disposition获取文件名，使用自定义名称
                        if not filename:
                            device_name = params.get('name', 'unknown_lab')
                            device_id = params.get('applianceId', 'unknown_id')
                            start_date = params.get('startDate', '').replace('-', '')
                            end_date = params.get('endDate', '').replace('-', '')
                            filename = f"LAB_{device_name}_{device_id}_{start_date}至{end_date}.docx"
                            filename = self._sanitize_filename(filename, file_type='lab')

                        file_path = os.path.join(save_path, filename)
                        print(f"保存实验室文件到: {file_path}")

                        # 写入文件并检查
                        try:
                            with open(file_path, 'wb') as f:
                                for chunk in response.iter_content(chunk_size=8192):
                                    if chunk:
                                        f.write(chunk)
                            file_size = os.path.getsize(file_path)
                            print(f"实验室文件大小: {file_size} 字节")

                            if file_size == 0:
                                print("警告: 保存的实验室文件大小为0")
                                try:
                                    os.remove(file_path)
                                    print("已删除空的实验室文件")
                                except OSError as del_err:
                                    print(f"删除空文件失败: {del_err}")
                                return None, 1, "无使用记录 (文件大小为0)"

                            # 可选的文件头检查
                            # try:
                            #     with open(file_path, 'rb') as f:
                            #         header = f.read(8)
                            #     if not header.startswith(b'PK\\x03\\x04'):
                            #         print(f"警告: 文件头部不是标准Word(DOCX)格式: {header.hex()}")
                            # except IOError as read_err:
                            #      print(f"读取文件头时出错: {read_err}")
                            #      return None, 2, f"无法读取文件头: {str(read_err)}"

                            return file_path, 0, "成功"

                        except Exception as write_err:
                            print(f"写入实验室文件时出错: {write_err}")
                            return None, 2, f"写入文件时出错: {str(write_err)}"

                    else:
                        # 如果 Content-Disposition 不表明是文件，则认为是错误或无记录
                        print("警告：Content-Disposition 未表明是文件附件，或响应体过短。可能无有效记录或发生错误。")
                        # 可以根据需要返回 status 1 (无记录) 或 2 (错误)
                        # 沿用之前的逻辑，返回 status 1
                        return None, 1, "服务器响应异常，可能无有效记录 (Content-Disposition未指明文件或响应体过短)"

            except requests.RequestException as e:
                print(f"下载实验室记录失败 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1

                if retry_count >= max_retries:
                    print(f"已达到最大重试次数，将返回错误")
                    return None, 2, f"下载实验室记录失败 (已重试 {retry_count} 次): {str(e)}"

                continue  # 重试

            except Exception as e:
                print(f"处理实验室记录响应时发生未知错误: {str(e)}")
                print(traceback.format_exc())
                return None, 2, f"处理实验室记录响应时发生未知错误: {str(e)}"

        # 如果循环结束仍未成功 (理论上不应发生，因为前面有返回)
        return None, 2, "下载失败，原因未知 (已重试多次)"

    def download_loan_record(self, params, save_path, appliance_no=None):
        """下载设备出入库记录"""
        try:
            # 构建URL
            url = f"{self.base_url}/applianceLoanRecord/historyExcelExport"

            # 确保保存路径存在
            os.makedirs(save_path, exist_ok=True)

            # 发送请求
            response = self.session.get(
                url,
                params=params,
                headers=self.session.headers,
                stream=True
            )

            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"下载失败: HTTP {response.status_code}"
                self._log(f"下载出入库记录失败: {error_msg}")
                return None, 2, error_msg

            # 检查响应类型
            content_type = response.headers.get('Content-Type', '')
            if 'application/vnd.ms-excel' not in content_type and 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' not in content_type:
                error_msg = f"响应不是Excel文件: {content_type}"
                self._log(f"下载出入库记录失败: {error_msg}")
                return None, 2, error_msg

            # 获取设备编号
            device_no = appliance_no if appliance_no else params.get('applianceNo', '')
            if not device_no:
                # 如果没有直接提供的设备编号，尝试从参数中获取
                device_no = str(params.get('applianceNo', '未知设备'))

            # 先保存原始文件
            temp_filename = f"temp_{int(time.time())}.xls"
            temp_file_path = os.path.join(save_path, temp_filename)
            with open(temp_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            self._log(f"原始出入库记录文件已保存: {temp_file_path}")

            # 构建最终文件名：设备编号_出入库记录.xls
            filename = f"{device_no}_出入库记录.xls"
            file_path = os.path.join(save_path, filename)

            try:
                # 使用pandas读取Excel文件
                import pandas as pd

                # 尝试读取Excel文件 - 先不指定引擎，让pandas自动选择
                try:
                    # 首先尝试不指定引擎
                    self._log("尝试使用自动检测引擎读取Excel")
                    df = pd.read_excel(temp_file_path)
                except Exception as e1:
                    self._log(f"自动检测引擎失败: {str(e1)}")
                    try:
                        # 尝试使用openpyxl引擎(适用于xlsx)
                        self._log("尝试使用openpyxl引擎读取Excel")
                        df = pd.read_excel(temp_file_path, engine='openpyxl')
                    except Exception as e2:
                        self._log(f"openpyxl引擎失败: {str(e2)}")
                        try:
                            # 最后尝试使用xlrd引擎(适用于xls)
                            self._log("尝试使用xlrd引擎读取Excel")
                            df = pd.read_excel(temp_file_path, engine='xlrd')
                        except Exception as e3:
                            self._log(f"所有引擎尝试失败: {str(e3)}")
                            # 无法处理Excel文件，直接使用原始文件
                            os.rename(temp_file_path, file_path)
                            return file_path, 0, f"成功 (无法解析Excel: {str(e3)})"

                # 检查是否有数据
                if df.empty:
                    self._log("Excel文件中没有数据")
                    # 直接使用原始文件
                    os.rename(temp_file_path, file_path)
                    return file_path, 0, "成功 (无数据)"

                # 在日志中记录原始记录数
                original_count = len(df)
                self._log(f"原始Excel文件中包含 {original_count} 条记录")

                # 查找包含设备编号列的列名
                appliance_no_column = None
                for col in df.columns:
                    if '编号' in col:
                        appliance_no_column = col
                        break

                if not appliance_no_column:
                    self._log("未找到设备编号列，无法筛选，使用原始文件")
                    os.rename(temp_file_path, file_path)
                    return file_path, 0, "成功 (未筛选)"

                # 筛选出与当前设备编号完全匹配的记录
                self._log(f"根据设备编号 '{device_no}' 筛选记录")
                filtered_df = df[df[appliance_no_column] == device_no]

                # 在日志中记录筛选后的记录数
                filtered_count = len(filtered_df)
                self._log(f"筛选后有 {filtered_count} 条记录")

                if filtered_count == 0:
                    self._log("筛选后没有匹配的记录，使用原始文件")
                    os.rename(temp_file_path, file_path)
                    return file_path, 1, "没有匹配的出入库记录"

                # 查找序号列并重新编号
                sequence_column = None
                for col in filtered_df.columns:
                    # 检查列名中是否包含"序号"或"序"，或者列是否就是数字1,2,3...
                    if ('序号' in col) or (col.strip() == '序'):
                        sequence_column = col
                        break
                    # 检查第一行是否为1，并且是连续的数字
                    elif filtered_df.shape[0] >= 2:
                        try:
                            # 检查前两行是否为1和2
                            if (filtered_df[col].iloc[0] == 1 and
                                filtered_df[col].iloc[1] == 2):
                                sequence_column = col
                                break
                        except:
                            # 如果无法检查，则继续
                            pass

                # 如果找到序号列，进行重新编号
                if sequence_column:
                    self._log(f"找到序号列: {sequence_column}，进行重新编号")
                    # 重置索引并添加1，因为序号通常从1开始
                    filtered_df[sequence_column] = range(1, filtered_count + 1)
                else:
                    self._log("未找到序号列，跳过重新编号")

                # 保存筛选后的数据到新的Excel文件
                try:
                    # 将文件保存为xlsx格式，这种格式更现代且兼容性更好
                    xlsx_file_path = file_path.replace('.xls', '.xlsx')

                    # 保存为Excel文件
                    with pd.ExcelWriter(xlsx_file_path, engine='openpyxl') as writer:
                        filtered_df.to_excel(writer, index=False, sheet_name='出入库记录')

                        # 获取工作簿和工作表
                        workbook = writer.book
                        worksheet = writer.sheets['出入库记录']

                        # 调整列宽
                        for i, column in enumerate(filtered_df.columns):
                            # 获取列的最大宽度
                            column_width = 0

                            # 检查列标题长度
                            column_title = str(column)
                            title_width = len(column_title)

                            # 计算此列所有值的最大宽度
                            for value in filtered_df[column].astype(str):
                                # 计算中文字符数量（一个中文字符需要两个英文字符的宽度）
                                chinese_char_count = sum(1 for char in value if '\\u4e00' <= char <= '\\u9fff')
                                english_char_count = len(value) - chinese_char_count

                                # 计算总宽度：中文字符*2 + 英文字符
                                total_width = chinese_char_count * 2 + english_char_count
                                column_width = max(column_width, total_width)

                            # 比较列标题宽度和数据宽度
                            chinese_char_count_title = sum(1 for char in column_title if '\\u4e00' <= char <= '\\u9fff')
                            english_char_count_title = len(column_title) - chinese_char_count_title
                            title_width = chinese_char_count_title * 2 + english_char_count_title

                            # 取列标题和数据的最大宽度
                            final_width = max(column_width, title_width) + 4  # 额外留些空间

                            # 设置最小和最大列宽
                            final_width = max(10, min(50, final_width))  # 最小10，最大50

                            # 设置列宽
                            column_letter = openpyxl.utils.get_column_letter(i+1)
                            worksheet.column_dimensions[column_letter].width = final_width

                            self._log(f"列 {column_letter} ({column}) 宽度设置为: {final_width}")

                    self._log(f"筛选后的出入库记录已保存为xlsx格式 (自动调整列宽): {xlsx_file_path}")

                    # 删除临时文件
                    try:
                        os.remove(temp_file_path)
                        self._log(f"已删除临时文件: {temp_file_path}")
                    except Exception as del_e:
                        self._log(f"删除临时文件失败: {str(del_e)}")

                    # 返回xlsx格式的文件路径
                    return xlsx_file_path, 0, f"成功 (已从{original_count}条记录中筛选出{filtered_count}条)"
                except Exception as e:
                    self._log(f"保存Excel时出错: {str(e)}，使用原始文件")
                    os.rename(temp_file_path, file_path)
                    return file_path, 0, f"成功 (筛选成功但保存出错: {str(e)})"

            except ImportError:
                self._log("未安装pandas库，无法处理Excel文件，使用原始文件")
                os.rename(temp_file_path, file_path)
                return file_path, 0, "成功 (未筛选，需安装pandas)"
            except Exception as e:
                self._log(f"处理Excel文件时出错: {str(e)}，使用原始文件")
                os.rename(temp_file_path, file_path)
                return file_path, 0, f"成功 (处理出错: {str(e)})"

        except Exception as e:
            error_msg = f"下载出入库记录时发生错误: {str(e)}"
            self._log(error_msg)

            # 保存调试信息
            debug_path = os.path.join(save_path, 'debug')
            os.makedirs(debug_path, exist_ok=True)

            debug_info = {
                'error': str(e),
                'params': params,
                'url': url,
                'headers': dict(self.session.headers),
                'response_headers': dict(response.headers) if 'response' in locals() else None,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            debug_file = os.path.join(debug_path, f"loan_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(debug_info, f, ensure_ascii=False, indent=2)

            return None, 2, error_msg

    def download_maintenance_record(self, params, save_path, appliance_no, start_date_str=None, end_date_str=None):
        """下载设备维护记录 (Modified to accept dates and trigger filtering)"""
        try:
            # Build URL
            url = f"{self.base_url}/exportDoc/maintenanceRecord"

            # Get applianceId
            device_id = params.get('applianceId')
            if not device_id:
                return None, 2, "缺少必要的参数: applianceId"

            download_params = {'applianceId': str(device_id)}

            # Ensure save path exists
            os.makedirs(save_path, exist_ok=True)

            # Add random delay
            self._random_delay()

            self._log(f"开始下载维护记录: 设备ID={device_id}, 编号={appliance_no}")
            self._log(f"请求URL: {url}")
            self._log(f"请求参数: {download_params}")

            # Send request
            response = self.session.get(
                url,
                params=download_params,
                headers=self.session.headers, # Use standard headers
                stream=True,
                timeout=60 # Add a timeout
            )

            # Check response status
            if response.status_code != 200:
                error_msg = f"下载失败: HTTP {response.status_code}"
                self._log(f"下载维护记录失败: {error_msg}")
                # Try to read potential JSON error message
                try:
                    json_content = response.json()
                    error_msg += f" - {json_content.get('msg', response.text[:100])}"
                    self._log(f"服务器错误详情: {json_content}")
                     # Check for timeout specifically
                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        return None, 2, "登录已超时，请更新Cookie信息"
                except json.JSONDecodeError:
                    pass # Not a JSON error
                return None, 2, error_msg

            # Check response type
            content_type = response.headers.get('Content-Type', '').lower()

            # Handle JSON response (likely an error or empty data indication)
            if content_type.startswith('application/json'):
                print("服务器返回JSON响应 (维护记录)")
                try:
                    json_content = response.json()
                    print(f"JSON内容 (维护记录): {json_content}")
                    debug_json_path = os.path.join(self.debug_dir, f"debug_json_maintenance_{device_id}.json")
                    with open(debug_json_path, 'w', encoding='utf-8') as f:
                       json.dump(json_content, f, ensure_ascii=False, indent=2)

                    if 'rc' in json_content and json_content['rc'] != 0:
                       print(f"服务器返回错误 (维护记录): {json_content}")
                       return None, 2, f"服务器返回错误: {json_content.get('msg', '未知错误')}"

                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                       print("登录已超时 (维护记录)")
                       return None, 2, "登录已超时，请更新Cookie信息"

                    # Assuming other JSON means no record found? Or treat as error?
                    # Let's treat it as 'no record' for now, similar to 0-byte file.
                    print("服务器返回未知JSON，可能无记录 (维护记录)")
                    return None, 1, "服务器返回JSON，可能无维护记录"
                except json.JSONDecodeError as json_err:
                    print(f"无法解析服务器返回的JSON (维护记录): {json_err}")
                    return None, 2, f"无法解析服务器返回的JSON (维护记录): {response.text[:500]}..."

            # Handle file stream (assume DOCX)
            else:
                # Build filename: {设备编号}_仪器维护记录.docx
                device_no_str = str(appliance_no if appliance_no else device_id)
                # Clean the device number part specifically
                invalid_chars = '<>:"/\\\\|?*' # Use double backslash for literal backslash
                for char in invalid_chars:
                     device_no_str = device_no_str.replace(char, '_')

                filename = f"{device_no_str}_仪器维护记录.docx"
                # Basic cleaning for the whole filename (redundant but safe)
                for char in invalid_chars:
                     filename = filename.replace(char, '_')

                file_path = os.path.join(save_path, filename)
                self._log(f"准备保存维护记录到: {file_path}")

                try:
                    with open(file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                except Exception as write_err:
                    self._log(f"写入维护记录文件时出错: {write_err}")
                    return None, 2, f"写入文件时出错: {str(write_err)}"

                # --- ADDED: Check for 0KB file ---
                try:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        if file_size == 0:
                            self._log(f"文件大小为 0KB: {file_path}. 删除文件并标记为无记录。")
                            os.remove(file_path)
                            return None, 1, "服务器返回空文件" # Status 1: No Records
                    else:
                         # This case should ideally not happen if writing succeeded, but handle defensively
                         self._log(f"警告: 文件写入后未找到: {file_path}")
                         return None, 2, "文件写入后丢失"
                except OSError as e:
                     self._log(f"检查文件大小或删除 0KB 文件时出错: {e}")
                     return None, 2, f"处理文件时出错: {e}"
                # --- END ADDED ---

                # Proceed with filtering only if file exists and is not empty
                if start_date_str and end_date_str and Document:
                    self._log(f"开始对文件进行日期筛选: {file_path}")
                    filter_success, filter_message = self._filter_maintenance_docx_by_date(
                        file_path, start_date_str, end_date_str
                    )
                    if not filter_success:
                        self._log(f"文件下载成功，但日期筛选失败: {filter_message}. 文件 '{filename}' 可能包含日期范围外的数据。")
                        return file_path, 0, "下载成功 (筛选失败)" # Simpler message
                    else:
                         self._log(f"文件下载并筛选成功: {filter_message}")
                         return file_path, 0, f"下载成功 ({filter_message})"

                # Success return (if no filtering was needed/possible or if filtering succeeded without specific message)
                self._log(f"维护记录下载成功（无需筛选或筛选完成）: {file_path}")
                return file_path, 0, "下载成功"

        except requests.exceptions.RequestException as e:
            self._log(f"下载维护记录时发生网络错误: {e}")
            # Try to determine if it's a timeout
            if "timed out" in str(e).lower() or "timeout" in str(e).lower():
                return None, 2, "网络请求超时"
            return None, 2, f"网络错误: {e}"
        except Exception as e:
            # Log the full traceback for unexpected errors
            self._log(f"处理维护记录下载时发生未知错误: {traceback.format_exc()}")
            return None, 2, f"未知错误: {e}"

    def download_verify_certificate(self, verify_result_id, save_path, appliance_no):
        """下载检定校准证书确认表

        Args:
            verify_result_id: 检定结果ID
            save_path: 保存路径
            appliance_no: 仪器编号，用于重命名文件

        Returns:
            tuple: (file_path, status, message)
                file_path: 保存的文件路径，如果下载失败则为None
                status: 0-成功，1-无记录，2-错误
                message: 状态消息
        """
        try:
            # 构建URL
            url = f"{self.base_url}/exportDoc/V4_2/verifyResult"

            # 构建参数
            params = {'verifyResultId': str(verify_result_id)}

            # 确保保存路径存在
            os.makedirs(save_path, exist_ok=True)

            # 添加随机延时
            self._random_delay()

            self._log(f"开始下载检定校准证书确认表: 结果ID={verify_result_id}, 仪器编号={appliance_no}")
            self._log(f"请求URL: {url}")
            self._log(f"请求参数: {params}")

            # 发送请求
            response = self.session.get(
                url,
                params=params,
                headers=self.session.headers,
                stream=True,
                timeout=60
            )

            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"下载失败: HTTP {response.status_code}"
                self._log(f"下载检定校准证书确认表失败: {error_msg}")
                # 尝试读取可能的JSON错误消息
                try:
                    json_content = response.json()
                    error_msg += f" - {json_content.get('msg', response.text[:100])}"
                    self._log(f"服务器错误详情: {json_content}")
                    # 检查是否超时
                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        return None, 2, "登录已超时，请更新Cookie信息"
                except json.JSONDecodeError:
                    pass  # 不是JSON错误
                return None, 2, error_msg

            # 检查响应类型
            content_type = response.headers.get('Content-Type', '').lower()

            # 处理JSON响应（可能是错误或空数据指示）
            if content_type.startswith('application/json'):
                self._log("服务器返回JSON响应 (检定校准证书确认表)")
                try:
                    json_content = response.json()
                    self._log(f"JSON内容 (检定校准证书确认表): {json_content}")
                    debug_json_path = os.path.join(self.debug_dir, f"debug_json_verify_{verify_result_id}.json")
                    with open(debug_json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_content, f, ensure_ascii=False, indent=2)

                    if 'rc' in json_content and json_content['rc'] != 0:
                        self._log(f"服务器返回错误 (检定校准证书确认表): {json_content}")
                        return None, 2, f"服务器返回错误: {json_content.get('msg', '未知错误')}"

                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        self._log("登录已超时 (检定校准证书确认表)")
                        return None, 2, "登录已超时，请更新Cookie信息"

                    # 假设其他JSON意味着没有找到记录
                    self._log("服务器返回未知JSON，可能无记录 (检定校准证书确认表)")
                    return None, 1, "服务器返回JSON，可能无检定校准证书确认表记录"
                except json.JSONDecodeError as json_err:
                    self._log(f"无法解析服务器返回的JSON (检定校准证书确认表): {json_err}")
                    return None, 2, f"无法解析服务器返回的JSON (检定校准证书确认表): {response.text[:500]}..."

            # 处理文件流（假设是DOCX）
            else:
                # 从Content-Disposition获取原始文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                original_filename = None

                if 'filename=' in content_disposition:
                    try:
                        original_filename = content_disposition.split('filename=')[1]
                        # 处理可能的引号
                        if original_filename.startswith('"') and original_filename.endswith('"'):
                            original_filename = original_filename[1:-1]
                        # 处理文件名编码
                        original_filename = self._sanitize_filename(original_filename)
                        self._log(f"从Content-Disposition提取的原始文件名: {original_filename}")
                    except Exception as e:
                        self._log(f"解析Content-Disposition时出错: {str(e)}")

                # 如果无法从Content-Disposition获取文件名，使用默认名称
                if not original_filename:
                    original_filename = "检定校准证书确认表.docx"

                # 构建新文件名：{仪器编号}-检定校准证书确认表.docx
                appliance_no_str = str(appliance_no) if appliance_no else f"ID{verify_result_id}"
                # 清理仪器编号部分
                invalid_chars = '<>:"/\\|?*'
                for char in invalid_chars:
                    appliance_no_str = appliance_no_str.replace(char, '_')

                # 构建最终文件名
                filename = f"{appliance_no_str}-检定校准证书确认表.docx"

                # 构建完整的文件路径
                file_path = os.path.join(save_path, filename)
                self._log(f"准备保存检定校准证书确认表到: {file_path}")

                try:
                    with open(file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                except Exception as write_err:
                    self._log(f"写入检定校准证书确认表文件时出错: {write_err}")
                    return None, 2, f"写入文件时出错: {str(write_err)}"

                # 检查是否为0KB文件
                try:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        if file_size == 0:
                            self._log(f"文件大小为 0KB: {file_path}. 删除文件并标记为无记录。")
                            os.remove(file_path)
                            return None, 1, "服务器返回空文件"  # Status 1: No Records
                    else:
                        # 这种情况理论上不应该发生，但防御性处理
                        self._log(f"警告: 文件写入后未找到: {file_path}")
                        return None, 2, "文件写入后丢失"
                except OSError as e:
                    self._log(f"检查文件大小或删除 0KB 文件时出错: {e}")
                    return None, 2, f"处理文件时出错: {e}"

                # 下载成功
                self._log(f"检定校准证书确认表下载成功: {file_path}")
                return file_path, 0, "下载成功"

        except requests.exceptions.RequestException as e:
            self._log(f"下载检定校准证书确认表时发生网络错误: {e}")
            # 尝试确定是否是超时
            if "timed out" in str(e).lower() or "timeout" in str(e).lower():
                return None, 2, "网络请求超时"
            return None, 2, f"网络错误: {e}"
        except Exception as e:
            # 记录完整的错误堆栈
            self._log(f"处理检定校准证书确认表下载时发生未知错误: {traceback.format_exc()}")
            return None, 2, f"未知错误: {e}"

    def download_calibration_certificate(self, appliance_verify_id, save_path, appliance_no, appliance_name=None):
        """下载检定校准证书PDF文件

        Args:
            appliance_verify_id: 设备检定ID
            save_path: 保存路径
            appliance_no: 仪器编号，用于重命名文件
            appliance_name: 仪器名称，用于重命名文件

        Returns:
            tuple: (file_path, status, message)
                file_path: 保存的文件路径，如果下载失败则为None
                status: 0-成功，1-无记录，2-错误
                message: 状态消息
        """
        try:
            # 构建获取历史数据的URL
            history_url = f"{self.base_url}/applianceVerify/historyData"

            # 构建参数
            history_params = {
                'applianceVerifyId': str(appliance_verify_id),
                'rows': 10,
                'page': 1
            }

            self._log(f"开始获取设备 {appliance_no} 的检定历史数据")
            self._log(f"请求URL: {history_url}")
            self._log(f"请求参数: {history_params}")

            # 添加随机延时
            self._random_delay()

            # 发送请求获取历史数据
            response = self.session.get(history_url, params=history_params)
            response.raise_for_status()
            history_data = response.json()

            # 检查响应是否有效
            if 'rc' in history_data and history_data['rc'] == 0 and 'ret' in history_data and 'rows' in history_data['ret']:
                history_rows = history_data['ret']['rows']

                if not history_rows:
                    self._log(f"设备 {appliance_no} 没有检定历史记录")
                    return None, 1, "没有检定历史记录"

                # 查找第一个有fileUploadId的记录
                file_upload_ids = []
                verify_date = None

                for row in history_rows:
                    if row.get('fileUploadIds') and row.get('fileArrays'):
                        # 可能有多个文件ID
                        if isinstance(row.get('fileUploadIds'), list):
                            file_upload_ids = row.get('fileUploadIds')
                        else:
                            # 如果是字符串，可能是逗号分隔的ID列表
                            file_upload_id_str = row.get('fileUploadId')
                            if file_upload_id_str and ',' in file_upload_id_str:
                                file_upload_ids = file_upload_id_str.split(',')
                            else:
                                file_upload_ids = [row.get('fileUploadId')]

                        verify_date = row.get('verifyDate')
                        break

                if not file_upload_ids:
                    self._log(f"设备 {appliance_no} 的检定历史记录中没有找到文件ID")
                    return None, 1, "检定历史记录中没有找到文件ID"

                # 构建下载文件的URL
                download_url = f"http://*************/fss/download"

                # 创建临时目录用于存放下载的文件
                temp_dir = os.path.join(save_path, "temp_" + str(int(time.time())))
                os.makedirs(temp_dir, exist_ok=True)

                # 用于存储下载的临时文件路径
                temp_files = []

                # 逐个下载每个文件
                for i, file_id in enumerate(file_upload_ids):
                    # 构建参数
                    download_params = {
                        'fileId': file_id
                    }

                    self._log(f"开始下载设备 {appliance_no} 的检定校准证书 ({i+1}/{len(file_upload_ids)})，文件ID: {file_id}")
                    self._log(f"请求URL: {download_url}")
                    self._log(f"请求参数: {download_params}")

                    # 添加随机延时
                    self._random_delay()

                    # 发送请求下载文件
                    try:
                        download_response = self.session.get(
                            download_url,
                            params=download_params,
                            stream=True,
                            timeout=60
                        )

                        # 检查响应状态
                        if download_response.status_code != 200:
                            error_msg = f"下载失败: HTTP {download_response.status_code}"
                            self._log(f"下载检定校准证书文件 {i+1} 失败: {error_msg}")
                            continue  # 继续下载其他文件

                        # 从Content-Disposition获取原始文件名
                        content_disposition = download_response.headers.get('Content-Disposition', '')
                        original_filename = None

                        if 'filename=' in content_disposition:
                            try:
                                original_filename = content_disposition.split('filename=')[1]
                                # 处理可能的引号
                                if original_filename.startswith('"') and original_filename.endswith('"'):
                                    original_filename = original_filename[1:-1]
                                # 处理文件名编码
                                original_filename = self._sanitize_filename(original_filename)
                                self._log(f"从Content-Disposition提取的原始文件名: {original_filename}")
                            except Exception as e:
                                self._log(f"解析Content-Disposition时出错: {str(e)}")

                        # 如果无法获取原始文件名，使用临时文件名
                        if not original_filename:
                            original_filename = f"temp_{i+1}.pdf"

                        # 如果文件名不是以.pdf结尾，添加.pdf后缀
                        if not original_filename.lower().endswith('.pdf'):
                            original_filename += '.pdf'

                        # 构建临时文件路径
                        temp_file_path = os.path.join(temp_dir, original_filename)

                        # 保存文件
                        try:
                            with open(temp_file_path, 'wb') as f:
                                for chunk in download_response.iter_content(chunk_size=8192):
                                    if chunk:
                                        f.write(chunk)

                            # 检查文件大小
                            file_size = os.path.getsize(temp_file_path)
                            if file_size == 0:
                                self._log(f"文件大小为 0KB: {temp_file_path}. 删除文件。")
                                os.remove(temp_file_path)
                            else:
                                # 添加到临时文件列表
                                temp_files.append(temp_file_path)
                                self._log(f"成功下载文件 {i+1}: {temp_file_path}")
                        except Exception as write_err:
                            self._log(f"写入临时文件时出错: {write_err}")
                    except requests.RequestException as e:
                        self._log(f"下载文件 {i+1} 时发生网络错误: {e}")

                # 检查是否有成功下载的文件
                if not temp_files:
                    self._log(f"设备 {appliance_no} 的所有检定校准证书文件下载失败")
                    # 清理临时目录
                    try:
                        import shutil
                        shutil.rmtree(temp_dir)
                    except Exception as e:
                        self._log(f"清理临时目录时出错: {e}")
                    return None, 2, "所有文件下载失败"

                # 构建最终文件名：{仪器编号}-检定校准证书.pdf
                final_filename = f"{appliance_no}-检定校准证书.pdf"
                final_file_path = os.path.join(save_path, final_filename)

                # 如果只有一个文件，直接重命名
                if len(temp_files) == 1:
                    try:
                        import shutil
                        shutil.copy2(temp_files[0], final_file_path)
                        self._log(f"单个文件已复制并重命名为: {final_file_path}")
                    except Exception as e:
                        self._log(f"复制单个文件时出错: {e}")
                        return None, 2, f"复制文件时出错: {str(e)}"
                else:
                    # 如果有多个文件，需要合并PDF
                    try:
                        # 尝试导入PyPDF2
                        try:
                            from PyPDF2 import PdfMerger

                            # 创建PDF合并器
                            merger = PdfMerger()

                            # 添加所有PDF文件
                            for temp_file in temp_files:
                                if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                                    try:
                                        merger.append(temp_file)
                                    except Exception as e:
                                        self._log(f"添加PDF文件 {temp_file} 到合并器时出错: {e}")

                            # 写入合并后的PDF
                            merger.write(final_file_path)
                            merger.close()

                            self._log(f"成功合并 {len(temp_files)} 个PDF文件: {final_file_path}")
                        except ImportError:
                            # 如果没有PyPDF2，尝试使用其他方法
                            self._log("未安装PyPDF2库，无法合并PDF文件。将复制第一个文件作为结果。")
                            import shutil
                            shutil.copy2(temp_files[0], final_file_path)
                            self._log(f"已复制第一个文件: {final_file_path}")
                    except Exception as e:
                        self._log(f"合并PDF文件时出错: {e}")
                        # 如果合并失败，复制第一个文件
                        try:
                            import shutil
                            shutil.copy2(temp_files[0], final_file_path)
                            self._log(f"合并失败，已复制第一个文件: {final_file_path}")
                        except Exception as copy_err:
                            self._log(f"复制第一个文件时出错: {copy_err}")
                            return None, 2, f"处理文件时出错: {str(e)}"

                # 清理临时目录
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                    self._log(f"已清理临时目录: {temp_dir}")
                except Exception as e:
                    self._log(f"清理临时目录时出错: {e}")

                # 检查最终文件
                try:
                    if os.path.exists(final_file_path):
                        file_size = os.path.getsize(final_file_path)
                        if file_size == 0:
                            self._log(f"最终文件大小为 0KB: {final_file_path}. 删除文件并标记为无记录。")
                            os.remove(final_file_path)
                            return None, 1, "生成的文件为空"
                    else:
                        self._log(f"警告: 最终文件未找到: {final_file_path}")
                        return None, 2, "生成的文件丢失"
                except OSError as e:
                    self._log(f"检查最终文件时出错: {e}")
                    return None, 2, f"处理文件时出错: {e}"

                # 下载成功
                verify_date_str = f"({verify_date})" if verify_date else ""
                self._log(f"检定校准证书下载成功: {final_file_path} {verify_date_str}")
                return final_file_path, 0, f"下载成功 {verify_date_str} (共 {len(temp_files)}/{len(file_upload_ids)} 个文件)"
            else:
                error_msg = f"获取检定历史数据失败: {history_data.get('err', '未知错误')}"
                self._log(error_msg)
                return None, 2, error_msg

        except requests.exceptions.RequestException as e:
            self._log(f"下载检定校准证书时发生网络错误: {e}")
            # 尝试确定是否是超时
            if "timed out" in str(e).lower() or "timeout" in str(e).lower():
                return None, 2, "网络请求超时"
            return None, 2, f"网络错误: {e}"
        except Exception as e:
            # 记录完整的错误堆栈
            self._log(f"处理检定校准证书下载时发生未知错误: {traceback.format_exc()}")
            return None, 2, f"未知错误: {e}"

    def download_field_verify_certificate(self, verify_result_id, save_path, appliance_no):
        """下载现场仪器检定校准证书确认表

        Args:
            verify_result_id: 检定结果ID
            save_path: 保存路径
            appliance_no: 仪器编号，用于重命名文件

        Returns:
            tuple: (file_path, status, message)
                file_path: 保存的文件路径，如果下载失败则为None
                status: 0-成功，1-无记录，2-错误
                message: 状态消息
        """
        try:
            # 构建URL
            url = f"{self.base_url}/exportDoc/V2_7/verifyResult"

            # 构建参数
            params = {'verifyResultId': str(verify_result_id)}

            # 确保保存路径存在
            os.makedirs(save_path, exist_ok=True)

            # 添加随机延时
            self._random_delay()

            self._log(f"开始下载现场仪器检定校准证书确认表: 结果ID={verify_result_id}, 仪器编号={appliance_no}")
            self._log(f"请求URL: {url}")
            self._log(f"请求参数: {params}")

            # 构建完整URL并打印
            from urllib.parse import urlencode
            full_url = f"{url}?{urlencode(params)}"
            self._log(f"完整请求URL: {full_url}")

            # 发送请求
            response = self.session.get(
                url,
                params=params,
                headers=self.session.headers,
                stream=True,
                timeout=60
            )

            self._log(f"实际请求URL: {response.url}")

            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"下载失败: HTTP {response.status_code}"
                self._log(f"下载现场仪器检定校准证书确认表失败: {error_msg}")
                # 尝试读取可能的JSON错误消息
                try:
                    json_content = response.json()
                    error_msg += f" - {json_content.get('msg', response.text[:100])}"
                    self._log(f"服务器错误详情: {json_content}")
                    # 检查是否超时
                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        return None, 2, "登录已超时，请更新Cookie信息"
                except json.JSONDecodeError:
                    pass  # 不是JSON错误
                return None, 2, error_msg

            # 检查响应类型
            content_type = response.headers.get('Content-Type', '').lower()

            # 处理JSON响应（可能是错误或空数据指示）
            if content_type.startswith('application/json'):
                self._log("服务器返回JSON响应 (现场仪器检定校准证书确认表)")
                try:
                    json_content = response.json()
                    self._log(f"JSON内容 (现场仪器检定校准证书确认表): {json_content}")
                    debug_json_path = os.path.join(self.debug_dir, f"debug_json_field_verify_{verify_result_id}.json")
                    with open(debug_json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_content, f, ensure_ascii=False, indent=2)

                    if 'rc' in json_content and json_content['rc'] != 0:
                        self._log(f"服务器返回错误 (现场仪器检定校准证书确认表): {json_content}")
                        return None, 2, f"服务器返回错误: {json_content.get('msg', '未知错误')}"

                    if 'ret' in json_content and json_content.get('ret', {}).get('ifTimeOut') == "1":
                        self._log("登录已超时 (现场仪器检定校准证书确认表)")
                        return None, 2, "登录已超时，请更新Cookie信息"

                    # 假设其他JSON意味着没有找到记录
                    self._log("服务器返回未知JSON，可能无记录 (现场仪器检定校准证书确认表)")
                    return None, 1, "服务器返回JSON，可能无现场仪器检定校准证书确认表记录"
                except json.JSONDecodeError as json_err:
                    self._log(f"无法解析服务器返回的JSON (现场仪器检定校准证书确认表): {json_err}")
                    return None, 2, f"无法解析服务器返回的JSON (现场仪器检定校准证书确认表): {response.text[:500]}..."

            # 处理文件流（假设是DOCX或PDF）
            else:
                # 从Content-Disposition获取原始文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                original_filename = None

                if 'filename=' in content_disposition:
                    try:
                        original_filename = content_disposition.split('filename=')[1]
                        # 处理可能的引号
                        if original_filename.startswith('"') and original_filename.endswith('"'):
                            original_filename = original_filename[1:-1]
                        # 处理文件名编码
                        original_filename = self._sanitize_filename(original_filename)
                        self._log(f"从Content-Disposition提取的原始文件名: {original_filename}")
                    except Exception as e:
                        self._log(f"解析Content-Disposition时出错: {str(e)}")

                # 如果无法从Content-Disposition获取文件名，使用默认名称
                if not original_filename:
                    original_filename = "现场仪器检定校准证书确认表.docx"

                # 获取文件扩展名
                file_ext = os.path.splitext(original_filename)[1].lower()
                if not file_ext:
                    # 如果没有扩展名，根据Content-Type判断
                    if 'pdf' in content_type:
                        file_ext = '.pdf'
                    else:
                        file_ext = '.docx'  # 默认为docx

                # 构建新文件名：{仪器编号}-检定校准证书确认表{扩展名}
                appliance_no_str = str(appliance_no) if appliance_no else f"ID{verify_result_id}"
                # 清理仪器编号部分
                invalid_chars = '<>:"/\\|?*'
                for char in invalid_chars:
                    appliance_no_str = appliance_no_str.replace(char, '_')

                # 构建最终文件名
                filename = f"{appliance_no_str}-检定校准证书确认表{file_ext}"

                # 构建完整的文件路径
                file_path = os.path.join(save_path, filename)
                self._log(f"准备保存现场仪器检定校准证书确认表到: {file_path}")

                try:
                    with open(file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                except Exception as write_err:
                    self._log(f"写入现场仪器检定校准证书确认表文件时出错: {write_err}")
                    return None, 2, f"写入文件时出错: {str(write_err)}"

                # 检查是否为0KB文件
                try:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        if file_size == 0:
                            self._log(f"文件大小为 0KB: {file_path}. 删除文件并标记为无记录。")
                            os.remove(file_path)
                            return None, 1, "服务器返回空文件"  # Status 1: No Records
                    else:
                        # 这种情况理论上不应该发生，但防御性处理
                        self._log(f"警告: 文件写入后未找到: {file_path}")
                        return None, 2, "文件写入后丢失"
                except OSError as e:
                    self._log(f"检查文件大小或删除 0KB 文件时出错: {e}")
                    return None, 2, f"处理文件时出错: {e}"

                # 下载成功
                self._log(f"现场仪器检定校准证书确认表下载成功: {file_path}")
                return file_path, 0, "下载成功"

        except requests.exceptions.RequestException as e:
            self._log(f"下载现场仪器检定校准证书确认表时发生网络错误: {e}")
            # 尝试确定是否是超时
            if "timed out" in str(e).lower() or "timeout" in str(e).lower():
                return None, 2, "网络请求超时"
            return None, 2, f"网络错误: {e}"
        except Exception as e:
            # 记录完整的错误堆栈
            self._log(f"处理现场仪器检定校准证书确认表下载时发生未知错误: {traceback.format_exc()}")
            return None, 2, f"未知错误: {e}"

    def download_field_calibration_certificate(self, verify_id, save_path, appliance_no, appliance_name=None):
        """下载现场仪器检定校准证书PDF文件

        Args:
            verify_id: 检定记录ID
            save_path: 保存路径
            appliance_no: 仪器编号，用于重命名文件
            appliance_name: 仪器名称，用于重命名文件（可选）

        Returns:
            tuple: (file_path, status, message)
                file_path: 保存的文件路径，如果下载失败则为None
                status: 0-成功，1-无记录，2-错误
                message: 状态消息
        """
        try:
            # 首先获取检定历史数据
            history_url = f"{self.base_url}/applianceVerify/historyData"
            history_params = {
                'applianceVerifyId': verify_id,
                'rows': 10,
                'page': 1
            }

            self._log(f"获取现场仪器检定历史数据: 检定ID={verify_id}, 仪器编号={appliance_no}")
            self._log(f"请求URL: {history_url}")
            self._log(f"请求参数: {history_params}")

            # 构建完整URL并打印
            from urllib.parse import urlencode
            full_url = f"{history_url}?{urlencode(history_params)}"
            self._log(f"完整请求URL: {full_url}")

            # 添加随机延时
            self._random_delay()

            # 发送请求获取历史数据
            history_response = self.session.get(
                history_url,
                params=history_params,
                headers=self.session.headers,
                timeout=30
            )

            self._log(f"实际请求URL: {history_response.url}")

            history_response.raise_for_status()
            history_data = history_response.json()

            self._log(f"检定历史数据响应: {history_data}")

            # 检查响应是否有效
            if 'rc' in history_data and history_data['rc'] == 0 and 'ret' in history_data and 'rows' in history_data['ret']:
                history_rows = history_data['ret']['rows']

                if not history_rows:
                    self._log(f"设备 {appliance_no} 没有检定历史记录")
                    return None, 1, "没有检定历史记录"

                # 只获取最新日期的文件ID
                all_file_ids = []
                latest_date = None
                latest_rows = []

                # 找出最新的检定日期
                for row in history_rows:
                    if 'verifyDate' in row:
                        try:
                            verify_date = datetime.strptime(row['verifyDate'], '%Y-%m-%d').date()
                            if latest_date is None or verify_date > latest_date:
                                latest_date = verify_date
                        except (ValueError, TypeError):
                            self._log(f"无法解析检定日期: {row.get('verifyDate')}")

                if latest_date:
                    self._log(f"找到最新检定日期: {latest_date}")
                    # 收集最新日期的所有记录
                    for row in history_rows:
                        if 'verifyDate' in row:
                            try:
                                verify_date = datetime.strptime(row['verifyDate'], '%Y-%m-%d').date()
                                if verify_date == latest_date:
                                    latest_rows.append(row)
                            except (ValueError, TypeError):
                                pass

                    # 从最新日期的记录中获取文件ID
                    for row in latest_rows:
                        # 检查是否有fileArrays字段
                        if 'fileArrays' in row and row['fileArrays']:
                            for file_info in row['fileArrays']:
                                if 'remoteRelativeUrl' in file_info:
                                    all_file_ids.append(file_info['remoteRelativeUrl'])
                        # 检查是否有fileUploadIds字段
                        elif 'fileUploadIds' in row and row['fileUploadIds']:
                            all_file_ids.extend(row['fileUploadIds'])
                else:
                    # 如果无法确定最新日期，则使用第一条记录
                    self._log("无法确定最新检定日期，使用第一条记录")
                    row = history_rows[0]
                    # 检查是否有fileArrays字段
                    if 'fileArrays' in row and row['fileArrays']:
                        for file_info in row['fileArrays']:
                            if 'remoteRelativeUrl' in file_info:
                                all_file_ids.append(file_info['remoteRelativeUrl'])
                    # 检查是否有fileUploadIds字段
                    elif 'fileUploadIds' in row and row['fileUploadIds']:
                        all_file_ids.extend(row['fileUploadIds'])

                if not all_file_ids:
                    self._log(f"设备 {appliance_no} 的检定历史记录中没有找到文件ID")
                    return None, 1, "检定历史记录中没有找到文件ID"

                self._log(f"找到 {len(all_file_ids)} 个文件ID: {all_file_ids}")

                # 下载所有文件
                temp_files = []
                for file_id in all_file_ids:
                    # 构建下载URL - 注意：直接使用不带/ehscare的URL
                    download_url = f"http://*************/fss/download"
                    download_params = {'fileId': file_id}

                    self._log(f"开始下载文件: ID={file_id}")
                    self._log(f"下载URL: {download_url}")
                    self._log(f"下载参数: {download_params}")

                    # 添加随机延时
                    self._random_delay()

                    # 发送请求下载文件
                    file_response = self.session.get(
                        download_url,
                        params=download_params,
                        headers=self.session.headers,
                        stream=True,
                        timeout=60
                    )

                    # 检查响应状态
                    if file_response.status_code != 200:
                        self._log(f"下载文件失败: HTTP {file_response.status_code}")
                        continue

                    # 检查响应类型
                    content_type = file_response.headers.get('Content-Type', '').lower()

                    # 处理JSON响应（可能是错误或空数据指示）
                    if content_type.startswith('application/json'):
                        self._log(f"服务器返回JSON响应，跳过此文件: {file_id}")
                        continue

                    # 从Content-Disposition获取原始文件名
                    content_disposition = file_response.headers.get('Content-Disposition', '')
                    original_filename = None

                    if 'filename=' in content_disposition:
                        try:
                            original_filename = content_disposition.split('filename=')[1]
                            # 处理可能的引号
                            if original_filename.startswith('"') and original_filename.endswith('"'):
                                original_filename = original_filename[1:-1]
                            # 处理文件名编码
                            original_filename = self._sanitize_filename(original_filename)
                            self._log(f"从Content-Disposition提取的原始文件名: {original_filename}")
                        except Exception as e:
                            self._log(f"解析Content-Disposition时出错: {str(e)}")

                    # 如果无法从Content-Disposition获取文件名，使用默认名称
                    if not original_filename:
                        original_filename = f"检定校准证书_{file_id}.pdf"

                    # 获取文件扩展名
                    file_ext = os.path.splitext(original_filename)[1].lower()
                    if not file_ext:
                        # 如果没有扩展名，根据Content-Type判断
                        if 'pdf' in content_type:
                            file_ext = '.pdf'
                        else:
                            file_ext = '.pdf'  # 默认为pdf

                    # 构建临时文件名
                    temp_filename = f"temp_{file_id}{file_ext}"
                    temp_file_path = os.path.join(save_path, temp_filename)

                    # 确保保存路径存在
                    os.makedirs(save_path, exist_ok=True)

                    # 保存文件
                    try:
                        with open(temp_file_path, 'wb') as f:
                            for chunk in file_response.iter_content(chunk_size=8192):
                                if chunk:
                                    f.write(chunk)

                        # 检查文件大小
                        if os.path.getsize(temp_file_path) > 0:
                            temp_files.append(temp_file_path)
                            self._log(f"成功下载临时文件: {temp_file_path}")
                        else:
                            self._log(f"下载的文件为空: {temp_file_path}")
                            os.remove(temp_file_path)
                    except Exception as write_err:
                        self._log(f"写入文件时出错: {write_err}")
                        if os.path.exists(temp_file_path):
                            os.remove(temp_file_path)

                # 检查是否有成功下载的文件
                if not temp_files:
                    self._log(f"没有成功下载任何文件")
                    return None, 1, "没有成功下载任何文件"

                # 如果只有一个文件，直接重命名
                if len(temp_files) == 1:
                    # 构建最终文件名
                    appliance_no_str = str(appliance_no) if appliance_no else f"ID{verify_id}"
                    # 清理仪器编号部分
                    invalid_chars = '<>:"/\\|?*'
                    for char in invalid_chars:
                        appliance_no_str = appliance_no_str.replace(char, '_')

                    # 获取文件扩展名
                    file_ext = os.path.splitext(temp_files[0])[1].lower()

                    # 构建最终文件名
                    filename = f"{appliance_no_str}-检定校准证书{file_ext}"
                    final_file_path = os.path.join(save_path, filename)

                    # 重命名文件
                    if os.path.exists(final_file_path):
                        os.remove(final_file_path)
                    os.rename(temp_files[0], final_file_path)

                    self._log(f"现场仪器检定校准证书下载成功: {final_file_path}")
                    return final_file_path, 0, "下载成功"

                # 如果有多个文件，需要合并PDF
                else:
                    # 构建最终文件名
                    appliance_no_str = str(appliance_no) if appliance_no else f"ID{verify_id}"
                    # 清理仪器编号部分
                    invalid_chars = '<>:"/\\|?*'
                    for char in invalid_chars:
                        appliance_no_str = appliance_no_str.replace(char, '_')

                    # 构建最终文件名
                    filename = f"{appliance_no_str}-检定校准证书.pdf"
                    final_file_path = os.path.join(save_path, filename)

                    # 尝试合并PDF
                    try:
                        # 检查是否有PyPDF2库
                        if not PdfMerger:
                            self._log("缺少PyPDF2库，无法合并PDF文件")
                            # 如果没有PyPDF2库，只保留第一个文件
                            if os.path.exists(final_file_path):
                                os.remove(final_file_path)
                            os.rename(temp_files[0], final_file_path)

                            # 删除其他临时文件
                            for temp_file in temp_files[1:]:
                                if os.path.exists(temp_file):
                                    os.remove(temp_file)

                            self._log(f"由于无法合并PDF，只保留第一个文件: {final_file_path}")
                            return final_file_path, 0, "下载成功 (无法合并多个PDF，只保留第一个)"

                        # 合并PDF
                        merger = PdfMerger()
                        for temp_file in temp_files:
                            try:
                                merger.append(temp_file)
                            except Exception as pdf_err:
                                self._log(f"添加PDF文件时出错: {pdf_err}")

                        # 保存合并后的PDF
                        if os.path.exists(final_file_path):
                            os.remove(final_file_path)
                        merger.write(final_file_path)
                        merger.close()

                        # 删除临时文件
                        for temp_file in temp_files:
                            if os.path.exists(temp_file):
                                os.remove(temp_file)

                        self._log(f"成功合并 {len(temp_files)} 个PDF文件: {final_file_path}")
                        return final_file_path, 0, f"下载成功 (合并了 {len(temp_files)} 个文件)"

                    except Exception as merge_err:
                        self._log(f"合并PDF文件时出错: {merge_err}")
                        # 如果合并失败，只保留第一个文件
                        if os.path.exists(final_file_path):
                            os.remove(final_file_path)
                        os.rename(temp_files[0], final_file_path)

                        # 删除其他临时文件
                        for temp_file in temp_files[1:]:
                            if os.path.exists(temp_file):
                                os.remove(temp_file)

                        self._log(f"由于合并失败，只保留第一个文件: {final_file_path}")
                        return final_file_path, 0, "下载成功 (合并PDF失败，只保留第一个)"

            else:
                error_msg = f"获取检定历史数据失败: {history_data.get('err', '未知错误')}"
                self._log(error_msg)
                return None, 2, error_msg

        except requests.exceptions.RequestException as e:
            self._log(f"下载现场仪器检定校准证书时发生网络错误: {e}")
            # 尝试确定是否是超时
            if "timed out" in str(e).lower() or "timeout" in str(e).lower():
                return None, 2, "网络请求超时"
            return None, 2, f"网络错误: {e}"
        except Exception as e:
            # 记录完整的错误堆栈
            self._log(f"处理现场仪器检定校准证书下载时发生未知错误: {traceback.format_exc()}")
            return None, 2, f"未知错误: {e}"

    def _filter_maintenance_docx_by_date(self, file_path, start_date_str, end_date_str):
        """Filters the table rows in a DOCX file based on date in the first column."""
        if not Document: # Check again if library is available
            return False, "python-docx库不可用"
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()

            doc = Document(file_path)

            if not doc.tables:
                self._log(f"警告: 文件 {os.path.basename(file_path)} 中未找到表格，跳过筛选。")
                return True, "成功 (无表格可筛选)" # Treat as success, no filtering needed

            table = doc.tables[0] # Assume the first table

            rows_to_delete = []
            deleted_count = 0

            # Iterate rows, skipping potential header
            for i, row in enumerate(table.rows):
                if i == 0: # Simple way to skip header, adjust if header is multi-row or absent
                    continue

                try:
                    # Get text from the first cell and try parsing the date
                    cell_text = row.cells[0].text.strip()
                    if not cell_text: # Skip empty cells
                       continue
                    record_date = datetime.strptime(cell_text, "%Y-%m-%d").date()

                    # Check if the date is outside the desired range
                    if not (start_date <= record_date <= end_date):
                        rows_to_delete.append(row._tr) # Add the row's XML element to the list
                        deleted_count += 1

                except ValueError:
                    # If the first cell doesn't contain a valid date in YYYY-MM-DD, skip the row
                    self._log(f"警告: 在文件 {os.path.basename(file_path)} 表格行 {i+1} 的第一列未找到有效日期格式 YYYY-MM-DD ('{cell_text}'). 跳过此行筛选。")
                    continue
                except IndexError:
                     # Handle rows with fewer cells than expected
                     self._log(f"警告: 文件 {os.path.basename(file_path)} 表格行 {i+1} 格式异常 (单元格数量不足). 跳过此行筛选。")
                     continue

            # Delete the collected rows from the table's XML structure
            if rows_to_delete:
                self._log(f"正在从 {os.path.basename(file_path)} 删除 {deleted_count} 条超出日期范围的记录...")
                for tr_element in rows_to_delete:
                    try:
                        table._tbl.remove(tr_element)
                    except ValueError as ve:
                         # This might happen if the element was already removed or structure is unexpected
                         self._log(f"警告: 删除表格行时出现异常: {ve}。可能是重复删除或结构问题。")

                # Save the modified document
                doc.save(file_path)
                self._log(f"文件 {os.path.basename(file_path)} 已保存，共删除 {deleted_count} 行。")
                return True, f"成功 (已删除 {deleted_count} 条范围外记录)"
            else:
                self._log(f"文件 {os.path.basename(file_path)} 中没有需要删除的行。")
                return True, "成功 (无需删除行)"

        except FileNotFoundError:
             self._log(f"错误: 筛选时未找到文件 {file_path}")
             return False, "文件未找到"
        except ImportError: # Should be caught by the initial check, but good practice
             self._log(f"错误: 需要 'python-docx' 库来进行维护记录筛选。")
             return False, "缺少python-docx库"
        except Exception as e:
            self._log(f"筛选维护记录文件 {os.path.basename(file_path)} 时出错: {str(e)}")
            self._log(traceback.format_exc())
            return False, f"处理Word文件时出错: {str(e)}"