"""
康达价格管理系统 - Web应用主程序
提供Web界面进行价格数据管理和分析
"""

import os
import json
import io
import sqlite3
import threading
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, send_file, send_from_directory
from flask_session import Session
from werkzeug.utils import secure_filename

# 导入自定义模块
from web_login_manager import web_login_manager
from price_fetcher import PriceDataFetcher
from price_manager import PriceDataManager
from standard_methods_manager import StandardMethodsManager



app = Flask(__name__)
app.config['SECRET_KEY'] = 'kangda_price_management_2025'
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_PERMANENT'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = 3600 * 24  # 24小时
app.config['SESSION_FILE_DIR'] = 'flask_session'
app.config['SESSION_FILE_THRESHOLD'] = 500

# 确保会话目录存在
os.makedirs('flask_session', exist_ok=True)

# 初始化Session
Session(app)

# 创建全局实例
price_manager = PriceDataManager()
standard_methods_manager = StandardMethodsManager()

# 初始化数据库（确保fetch_status表存在）
try:
    fetcher = PriceDataFetcher()
except Exception as e:
    pass

# 全局变量存储抓取任务状态
fetch_tasks = {}

# 抓取超时时间（分钟）
FETCH_TIMEOUT_MINUTES = 30


def clear_cache_data():
    """清理所有缓存数据（包括统计缓存和汇总表）"""
    try:
        import sqlite3
        total_deleted = 0

        # 清理price_management.db中的缓存
        cache_db_path = 'price_management.db'

        with sqlite3.connect(cache_db_path) as conn:
            cursor = conn.cursor()

            # 清理价格变动缓存表
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='price_change_cache'
            """)

            if cursor.fetchone():
                cursor.execute('DELETE FROM price_change_cache')
                deleted_count = cursor.rowcount
                total_deleted += deleted_count

            conn.commit()

        # 清理主数据库中的统计汇总表
        main_db_path = 'kangda_prices.db'

        with sqlite3.connect(main_db_path) as conn:
            cursor = conn.cursor()

            # 清理统计汇总表
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='statistics_summary'
            """)

            if cursor.fetchone():
                cursor.execute('DELETE FROM statistics_summary')
                deleted_count = cursor.rowcount
                total_deleted += deleted_count

            conn.commit()

        return total_deleted

    except Exception as e:
        return 0


def require_login(f):
    """登录验证装饰器"""
    def decorated_function(*args, **kwargs):
        session_key = session.get('session_key')
        logged_in = session.get('logged_in', False)
        
        if not session_key or not logged_in:
            return redirect(url_for('login'))
        
        # 检查web_login_manager中是否有会话，如果没有则重新创建
        if not web_login_manager.is_session_valid(session_key):
            # 从Flask session恢复会话信息
            username = session.get('username')
            user_info = session.get('user_info')
            http_cookies = session.get('http_cookies')
            
            if username and user_info and http_cookies:
                
                # 重新创建HTTP管理器并设置cookies
                from network_manager import HttpRequestManager
                http_manager = HttpRequestManager()
                
                # 恢复cookies
                for name, value in http_cookies.items():
                    http_manager.session.cookies.set(name, value)
                
                # 重新创建web_login_manager会话
                web_login_manager.login_sessions[session_key] = {
                    'username': username,
                    'user_info': user_info,
                    'login_time': datetime.now(),
                    'http_manager': http_manager,
                    'cookies': http_cookies
                }
            else:
                return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function


# 数据抓取状态管理函数
def get_fetch_status():
    """获取当前抓取状态"""
    try:
        with sqlite3.connect('kangda_prices.db', timeout=1.0) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT is_running, task_id, user_session_key, username,
                       start_time, timeout_time, progress, message
                FROM fetch_status WHERE id = 1
            """)
            row = cursor.fetchone()

            if row:
                return {
                    'is_running': bool(row[0]),
                    'task_id': row[1],
                    'user_session_key': row[2],
                    'username': row[3],
                    'start_time': row[4],
                    'timeout_time': row[5],
                    'progress': row[6] or 0,
                    'message': row[7] or '空闲'
                }
            else:
                # 如果没有记录，创建一个默认记录
                cursor.execute("INSERT OR IGNORE INTO fetch_status (id) VALUES (1)")
                conn.commit()
                return {
                    'is_running': False,
                    'task_id': None,
                    'user_session_key': None,
                    'username': None,
                    'start_time': None,
                    'timeout_time': None,
                    'progress': 0,
                    'message': '空闲'
                }
    except Exception as e:
        return {
            'is_running': False,
            'task_id': None,
            'user_session_key': None,
            'username': None,
            'start_time': None,
            'timeout_time': None,
            'progress': 0,
            'message': '状态获取失败'
        }


def set_fetch_status(is_running, task_id=None, user_session_key=None, username=None,
                    start_time=None, timeout_time=None, progress=0, message='空闲'):
    """设置抓取状态"""
    try:
        # 使用更短的超时时间，避免长时间锁定
        with sqlite3.connect('kangda_prices.db', timeout=1.0) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE fetch_status SET
                    is_running = ?, task_id = ?, user_session_key = ?, username = ?,
                    start_time = ?, timeout_time = ?, progress = ?, message = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            """, (is_running, task_id, user_session_key, username,
                  start_time, timeout_time, progress, message))

            if cursor.rowcount == 0:
                # 如果没有记录，插入一条
                cursor.execute("""
                    INSERT INTO fetch_status
                    (id, is_running, task_id, user_session_key, username,
                     start_time, timeout_time, progress, message)
                    VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (is_running, task_id, user_session_key, username,
                      start_time, timeout_time, progress, message))

            conn.commit()
            return True
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            # 数据库锁定时，静默跳过，不影响主流程
            return False
        else:
            return False
    except Exception as e:
        return False


def clear_fetch_status():
    """清除抓取状态"""
    return set_fetch_status(False, None, None, None, None, None, 0, '空闲')


def check_and_clear_timeout_status():
    """检查并清理超时的抓取状态"""
    try:
        status = get_fetch_status()
        if status['is_running'] and status['timeout_time']:
            timeout_time = datetime.fromisoformat(status['timeout_time'])
            if datetime.now() > timeout_time:
                clear_fetch_status()
                return True
        return False
    except Exception as e:
        return False


@app.route('/')
def index():
    """首页 - 重定向到登录或控制台"""
    session_key = session.get('session_key')
    if session_key and web_login_manager.is_session_valid(session_key):
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))


@app.route('/login')
def login():
    """登录页面"""
    return render_template('login.html')


@app.route('/api/get_captcha')
def get_captcha():
    """获取验证码API"""
    result = web_login_manager.get_captcha_for_web()
    
    if result['success']:
        # 将jsessionid存储在session中
        session['captcha_jsessionid'] = result['jsessionid']
    
    return jsonify(result)


@app.route('/api/login', methods=['POST'])
def api_login():
    """登录API"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    captcha = data.get('captcha')
    jsessionid = session.get('captcha_jsessionid')
    
    if not all([username, password, captcha, jsessionid]):
        return jsonify({
            'success': False,
            'message': '请填写完整的登录信息'
        })
    
    result = web_login_manager.login_for_web(username, password, captcha, jsessionid)
    
    if result['success']:
        # 保存到Flask session和web_login_manager
        session['session_key'] = result['session_key']
        session['username'] = username
        session['user_info'] = result['user_info']
        session['logged_in'] = True
        session.permanent = True  # 设置为持久会话
        
        # 同时保存HTTP管理器状态到session中
        session_info = web_login_manager.get_session_info(result['session_key'])
        if session_info and 'http_manager' in session_info:
            # 保存cookies到session
            cookies_dict = dict(session_info['http_manager'].session.cookies)
            session['http_cookies'] = cookies_dict
    
    return jsonify(result)


@app.route('/logout')
def logout():
    """登出"""
    session_key = session.get('session_key')
    if session_key:
        web_login_manager.logout_session(session_key)

    session.clear()
    return redirect(url_for('login'))


@app.route('/api/logout_settings')
@require_login
def logout_settings():
    """退出设置验证"""
    session.pop('settings_verified', None)
    return jsonify({
        'success': True,
        'message': '已退出设置验证'
    })


@app.route('/api/method_counts')
@require_login
def get_method_counts():
    """获取方法数量统计API"""
    try:
        # 获取有效方法数量
        active_result = price_manager.get_methods_by_status(
            status=0,  # 有效方法
            limit=1,   # 只需要总数，不需要具体数据
            offset=0
        )

        # 获取废止方法数量
        disabled_result = price_manager.get_methods_by_status(
            status=1,  # 废止方法
            limit=1,   # 只需要总数，不需要具体数据
            offset=0
        )

        return jsonify({
            'success': True,
            'data': {
                'active_count': active_result.get('data', {}).get('total', 0),
                'disabled_count': disabled_result.get('data', {}).get('total', 0)
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取方法计数时出错: {str(e)}'
        })


@app.route('/dashboard')
@require_login
def dashboard():
    """控制台首页"""
    # 获取系统统计信息
    stats = price_manager.get_statistics()
    
    # 获取用户信息
    user_info = session.get('user_info', {})
    
    return render_template('dashboard.html', stats=stats, user_info=user_info)


@app.route('/data_fetch')
@require_login
def data_fetch():
    """数据抓取页面"""
    return render_template('data_fetch.html')


@app.route('/api/start_fetch', methods=['POST'])
@require_login
def start_fetch():
    """开始数据抓取API"""
    # 检查并清理超时状态
    check_and_clear_timeout_status()

    # 检查当前是否有抓取任务在进行
    current_status = get_fetch_status()
    if current_status['is_running']:
        return jsonify({
            'success': False,
            'message': f'当前有其他用户正在进行数据抓取，请稍后再试。\n执行用户：{current_status["username"]}\n开始时间：{current_status["start_time"]}'
        })

    session_key = session.get('session_key')
    username = session.get('username', '未知用户')
    http_manager = web_login_manager.get_http_manager(session_key)

    if not http_manager:
        return jsonify({
            'success': False,
            'message': '会话已过期，请重新登录'
        })

    # 创建抓取任务
    task_id = f"fetch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    start_time = datetime.now()
    timeout_time = start_time + timedelta(minutes=FETCH_TIMEOUT_MINUTES)

    # 设置抓取状态为进行中
    if not set_fetch_status(
        is_running=True,
        task_id=task_id,
        user_session_key=session_key,
        username=username,
        start_time=start_time.isoformat(),
        timeout_time=timeout_time.isoformat(),
        progress=0,
        message='正在初始化...'
    ):
        return jsonify({
            'success': False,
            'message': '无法启动抓取任务：系统正忙，可能有其他抓取任务正在进行中。请稍后重试或刷新页面查看当前状态。'
        })

    def fetch_task():
        try:
            # 初始化任务状态（兼容旧系统）
            fetch_tasks[task_id] = {
                'status': 'running',
                'progress': 0,
                'message': '正在初始化...',
                'start_time': start_time.isoformat()
            }

            # 创建抓取器并使用已登录的HTTP管理器
            fetcher = PriceDataFetcher()
            fetcher.http_manager = http_manager

            # 更新任务状态
            set_fetch_status(True, task_id, session_key, username,
                           start_time.isoformat(), timeout_time.isoformat(),
                           5, '正在获取数据总数...')
            fetch_tasks[task_id]['message'] = '正在获取数据总数...'
            fetch_tasks[task_id]['progress'] = 5
            
            total_count = fetcher.fetch_total_count()

            if total_count == 0:
                # 使用fetcher中的详细错误信息
                error_msg = getattr(fetcher, 'last_error_message', None) or '无法获取数据总数或会话已过期，请重新登录'
                fetch_tasks[task_id] = {
                    'status': 'failed',
                    'progress': 0,
                    'message': error_msg,
                    'end_time': datetime.now().isoformat()
                }
                # 清除抓取状态
                clear_fetch_status()
                return

            fetch_tasks[task_id]['total_count'] = total_count
            fetch_tasks[task_id]['message'] = f'开始抓取 {total_count} 条数据...'
            fetch_tasks[task_id]['progress'] = 10
            set_fetch_status(True, task_id, session_key, username,
                           start_time.isoformat(), timeout_time.isoformat(),
                           10, f'开始抓取 {total_count} 条数据...')

            # 创建进度回调函数
            last_db_update_progress = 0  # 记录上次数据库更新的进度

            def progress_callback(current_page, total_pages, message=None):
                nonlocal last_db_update_progress

                if current_page > 0 and total_pages > 0:
                    # 计算进度：10% (初始化) + 80% (抓取进度) + 10% (完成)
                    progress = 10 + int((current_page / total_pages) * 80)
                    fetch_tasks[task_id]['progress'] = min(90, progress)

                    # 更新策略：每2%或每5页更新一次数据库状态，平衡性能和用户体验
                    should_update = (
                        progress - last_db_update_progress >= 2 or  # 进度变化超过2%
                        current_page % 5 == 0 or  # 每5页更新一次
                        current_page == 1 or  # 第一页必须更新
                        current_page == total_pages  # 最后一页必须更新
                    )

                    if should_update:
                        current_message = message or f'正在抓取第 {current_page}/{total_pages} 页数据...'
                        # 异步更新状态，不阻塞主流程
                        try:
                            set_fetch_status(True, task_id, session_key, username,
                                           start_time.isoformat(), timeout_time.isoformat(),
                                           min(90, progress), current_message)
                            last_db_update_progress = progress
                        except:
                            # 忽略状态更新失败，不影响数据抓取
                            pass

                if message:
                    fetch_tasks[task_id]['message'] = message
            
            # 开始抓取，传入进度回调
            success = fetcher.fetch_all_data_with_progress(progress_callback)
            
            if success:
                fetch_tasks[task_id] = {
                    'status': 'completed',
                    'progress': 100,
                    'message': '数据抓取完成',
                    'end_time': datetime.now().isoformat(),
                    'total_count': total_count
                }
                # 清除抓取状态
                clear_fetch_status()
            else:
                # 使用fetcher中的详细错误信息
                error_msg = getattr(fetcher, 'last_error_message', None) or '数据抓取失败'
                fetch_tasks[task_id] = {
                    'status': 'failed',
                    'progress': 0,
                    'message': error_msg,
                    'end_time': datetime.now().isoformat()
                }
                # 清除抓取状态
                clear_fetch_status()

        except Exception as e:
            error_msg = str(e)
            # 优先使用fetcher的错误信息，如果没有则使用异常信息
            user_error_msg = getattr(fetcher, 'last_error_message', None) or f'抓取过程中发生错误: {error_msg}'
            fetch_tasks[task_id] = {
                'status': 'failed',
                'progress': 0,
                'message': user_error_msg,
                'end_time': datetime.now().isoformat()
            }
            # 清除抓取状态
            clear_fetch_status()
    
    # 启动后台任务
    thread = threading.Thread(target=fetch_task)
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': '数据抓取任务已启动'
    })


@app.route('/api/fetch_status/<task_id>')
@require_login
def fetch_status(task_id):
    """获取抓取任务状态"""
    task = fetch_tasks.get(task_id)
    if not task:
        return jsonify({
            'success': False,
            'message': '任务不存在'
        })
    
    return jsonify({
        'success': True,
        'task': task
    })


@app.route('/api/current_fetch_status')
@require_login
def current_fetch_status():
    """获取当前抓取状态"""
    # 检查并清理超时状态
    check_and_clear_timeout_status()

    status = get_fetch_status()
    return jsonify({
        'success': True,
        'status': status
    })





@app.route('/analysis_methods')
@require_login
def analysis_methods():
    """分析方法页面"""
    # 获取URL参数
    method_ids = request.args.get('method_ids', '')
    change_type = request.args.get('change_type', '')
    change_date = request.args.get('change_date', '')

    return render_template('analysis_methods.html',
                         method_ids=method_ids,
                         change_type=change_type,
                         change_date=change_date)


@app.route('/method_price_overview')
@require_login
def method_price_overview():
    """方法价格全览页面"""
    return render_template('method_price_overview.html')

# 保持向后兼容性
@app.route('/data_search')
@require_login
def data_search():
    """数据搜索页面 - 重定向到分析方法页面"""
    return redirect(url_for('analysis_methods'))


@app.route('/api/search_items')
@require_login
def search_items():
    """搜索项目API - 支持分页和全局搜索"""
    keyword = request.args.get('keyword', '')
    category = request.args.get('category', '')
    limit = int(request.args.get('limit', 20))
    offset = int(request.args.get('offset', 0))
    
    try:
        # 获取符合条件的实际显示行数（方法×采样类型组合数）
        if keyword or category:
            # 有搜索条件时，计算符合条件的实际显示行数
            total_count = price_manager.get_total_display_rows_count(keyword, category)
        else:
            # 无搜索条件时，计算所有数据的实际显示行数
            total_count = price_manager.get_total_display_rows_count()

        # 获取按实际显示行数分页的项目数据
        items = price_manager.search_items_by_display_rows(keyword, category, limit, offset)

        return jsonify({
            'success': True,
            'items': items,
            'total': total_count,
            'page': offset // limit + 1,
            'per_page': limit,
            'has_more': offset + limit < total_count
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索时出错: {str(e)}',
            'total': 0,
            'items': []
        })


@app.route('/api/get_item_details/<int:item_id>')
@require_login
def get_item_details(item_id):
    """获取项目详情API"""
    try:
        details = price_manager.get_item_details(item_id)
        return jsonify({
            'success': True,
            'details': details
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取项目详情时出错: {str(e)}'
        })


@app.route('/api/get_price_changes')
@require_login
def get_price_changes():
    """获取价格变动API"""
    days = int(request.args.get('days', 7))

    try:
        changes = price_manager.get_price_changes(days)
        return jsonify({
            'success': True,
            'changes': changes,
            'count': len(changes)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取价格变动时出错: {str(e)}'
        })


@app.route('/api/get_detailed_price_changes')
@require_login
def get_detailed_price_changes():
    """获取详细价格变动API"""
    days = int(request.args.get('days', 7))

    try:
        changes = price_manager.get_detailed_price_changes(days)
        return jsonify({
            'success': True,
            'changes': changes,
            'count': len(changes)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取详细价格变动时出错: {str(e)}'
        })


@app.route('/api/get_comprehensive_price_changes')
@require_login
def get_comprehensive_price_changes():
    """获取综合价格变动API（近期变动 + 基准变动）- 支持分页"""
    days = int(request.args.get('days', 7))
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    try:
        # 直接调用price_manager的方法，支持分页
        changes = price_manager.get_comprehensive_price_changes(days, page, per_page)

        return jsonify({
            'success': True,
            'data': changes
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取综合价格变动时出错: {str(e)}'
        })


@app.route('/api/method_price_trend')
@require_login
def get_method_price_trend():
    """获取方法价格趋势数据API"""
    try:
        method_id = request.args.get('method_id', type=int)
        if not method_id:
            return jsonify({
                'success': False,
                'message': '缺少方法ID参数'
            })

        trend_data = price_manager.get_method_price_trend(method_id)

        return jsonify({
            'success': True,
            'data': trend_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取价格趋势数据失败: {str(e)}'
        })


@app.route('/api/fetch_history')
@require_login
def get_fetch_history():
    """获取抓取历史记录API"""
    try:
        limit = request.args.get('limit', 20, type=int)

        # 使用较短的超时时间，避免长时间等待
        try:
            with sqlite3.connect('kangda_prices.db', timeout=2.0) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取抓取历史记录，按会话分组显示
                cursor.execute("""
                SELECT
                    session_id,
                    MIN(session_start_time) as session_start,
                    MAX(session_end_time) as session_end,
                    COUNT(*) as total_pages,
                    SUM(fetched_records) as total_fetched,
                    SUM(total_records) as total_records,
                    CASE
                        WHEN COUNT(CASE WHEN status = 'failed' THEN 1 END) > 0 THEN 'partial'
                        WHEN COUNT(CASE WHEN status = 'success' THEN 1 END) = COUNT(*) THEN 'success'
                        ELSE 'failed'
                    END as session_status,
                    GROUP_CONCAT(CASE WHEN status = 'failed' THEN error_message END, '; ') as error_messages
                FROM fetch_logs
                WHERE session_id IS NOT NULL
                GROUP BY session_id
                ORDER BY session_start DESC
                LIMIT ?
                """, (limit,))

                fetch_logs = []
                for row in cursor.fetchall():
                    log_dict = dict(row)
                    # 格式化时间
                    if log_dict['session_start']:
                        log_dict['session_start'] = log_dict['session_start']
                    if log_dict['session_end']:
                        log_dict['session_end'] = log_dict['session_end']
                    # 为了兼容前端，添加旧字段名
                    log_dict['fetch_time'] = log_dict['session_start']
                    log_dict['created_at'] = log_dict['session_start']
                    log_dict['page'] = f"1-{log_dict['total_pages']}"  # 显示页面范围
                    log_dict['status'] = log_dict['session_status']
                    log_dict['error_message'] = log_dict['error_messages']
                    fetch_logs.append(log_dict)

                # 获取汇总统计 - 按会话统计
                cursor.execute("""
                    SELECT
                        COUNT(DISTINCT session_id) as total_sessions,
                        COUNT(DISTINCT CASE WHEN session_status = 'success' THEN session_id END) as successful_sessions,
                        SUM(total_fetched) as total_fetched,
                        MAX(session_start) as last_fetch_time
                    FROM (
                        SELECT
                            session_id,
                            SUM(fetched_records) as total_fetched,
                            MIN(session_start_time) as session_start,
                            CASE
                                WHEN COUNT(CASE WHEN status = 'failed' THEN 1 END) > 0 THEN 'partial'
                                WHEN COUNT(CASE WHEN status = 'success' THEN 1 END) = COUNT(*) THEN 'success'
                                ELSE 'failed'
                            END as session_status
                        FROM fetch_logs
                        WHERE session_id IS NOT NULL
                        GROUP BY session_id
                    ) sessions
                """)

                summary = dict(cursor.fetchone())

            return jsonify({
                'success': True,
                'data': {
                    'logs': fetch_logs,
                    'summary': summary
                }
            })

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                # 数据库锁定时返回空数据，不影响页面加载
                return jsonify({
                    'success': True,
                    'data': {
                        'logs': [],
                        'summary': {
                            'total_sessions': 0,
                            'successful_sessions': 0,
                            'total_fetched': 0,
                            'last_fetch_time': None
                        }
                    },
                    'message': '数据抓取进行中，历史记录暂时不可用'
                })
            else:
                raise e

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取抓取历史失败: {str(e)}'
        })


@app.route('/api/analyze_method', methods=['GET'])
@require_login
def analyze_method():
    """方法分析API"""
    method_name = request.args.get('method_name', '').strip()
    project_name = request.args.get('project_name', '').strip()
    debug = request.args.get('debug', '').lower() == 'true'

    if not method_name and not project_name:
        return jsonify({
            'success': False,
            'message': '请输入要分析的方法名称或项目名称'
        })

    try:
        # 根据提供的参数调用不同的分析方法
        if method_name and project_name:
            # 同时使用方法名称和项目名称进行搜索
            result = price_manager.analyze_method_by_name_and_project(method_name, project_name)
        elif method_name:
            # 仅使用方法名称搜索
            result = price_manager.analyze_method_by_name(method_name)
        else:
            # 仅使用项目名称搜索
            result = price_manager.analyze_method_by_project_name(project_name)

        # 如果启用调试模式，添加调试信息
        if debug and not result['success']:
            # 尝试检查是否有类似的方法名或项目名
            with price_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM check_methods LIMIT 5")
                sample_methods = [row['name'] for row in cursor.fetchall()]
                cursor.execute("SELECT name FROM service_items LIMIT 5")
                sample_projects = [row['name'] for row in cursor.fetchall()]

                result['debug_info'] = {
                    'input_length': len(method_name or project_name),
                    'input_repr': repr(method_name or project_name),
                    'sample_methods': sample_methods,
                    'sample_projects': sample_projects,
                    'method_search': bool(method_name),
                    'project_search': bool(project_name)
                }

        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'分析方法时出错: {str(e)}'
        })


@app.route('/api/debug_method/<int:method_id>')
@require_login
def debug_method(method_id):
    """调试特定方法的价格信息"""
    try:
        with price_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 获取方法基本信息
            cursor.execute("""
                SELECT cm.*, si.name as service_item_name
                FROM check_methods cm
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE cm.id = ?
            """, (method_id,))

            method_info = cursor.fetchone()
            if not method_info:
                return jsonify({
                    'success': False,
                    'message': '方法不存在'
                })

            method_dict = dict(method_info)

            # 获取采样类型
            cursor.execute("""
                SELECT * FROM sample_types
                WHERE check_method_id = ?
                ORDER BY type
            """, (method_id,))

            sample_types = [dict(row) for row in cursor.fetchall()]

            # 获取价格历史记录
            cursor.execute("""
                SELECT * FROM price_history
                WHERE check_method_id = ?
                ORDER BY fetch_time DESC
                LIMIT 10
            """, (method_id,))

            price_history = [dict(row) for row in cursor.fetchall()]

            # 获取首次价格记录
            cursor.execute("""
                SELECT check_method_price as initial_analysis_price
                FROM price_history
                WHERE check_method_id = ? AND check_method_price IS NOT NULL
                ORDER BY fetch_time ASC
                LIMIT 1
            """, (method_id,))

            initial_price_row = cursor.fetchone()
            initial_analysis_price = initial_price_row['initial_analysis_price'] if initial_price_row else None

            return jsonify({
                'success': True,
                'data': {
                    'method_info': method_dict,
                    'sample_types': sample_types,
                    'price_history': price_history,
                    'initial_analysis_price': initial_analysis_price,
                    'current_analysis_price': method_dict.get('price'),
                    'current_lowest_price': method_dict.get('lowest_bid')
                }
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'调试方法信息时出错: {str(e)}'
        })


@app.route('/api/debug_data_integrity')
@require_login
def debug_data_integrity():
    """检查数据完整性"""
    try:
        with price_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 检查孤立的price_history记录
            cursor.execute("""
                SELECT COUNT(*) as orphaned_price_history
                FROM price_history ph
                LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                WHERE cm.id IS NULL
            """)
            orphaned_price_history = cursor.fetchone()['orphaned_price_history']

            # 检查孤立的check_methods记录
            cursor.execute("""
                SELECT COUNT(*) as orphaned_check_methods
                FROM check_methods cm
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE si.id IS NULL
            """)
            orphaned_check_methods = cursor.fetchone()['orphaned_check_methods']

            # 检查有null service_item_name的记录
            cursor.execute("""
                SELECT
                    ph.check_method_id,
                    si.name as service_item_name,
                    si.number as service_item_number,
                    cm.name as check_method_name
                FROM price_history ph
                LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
                LEFT JOIN service_items si ON cm.service_item_id = si.id
                WHERE ph.fetch_time >= datetime('now', '-7 days')
                AND (si.name IS NULL OR si.number IS NULL)
                LIMIT 10
            """)
            problematic_records = [dict(row) for row in cursor.fetchall()]

            return jsonify({
                'success': True,
                'data': {
                    'orphaned_price_history': orphaned_price_history,
                    'orphaned_check_methods': orphaned_check_methods,
                    'problematic_records': problematic_records
                }
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查数据完整性时出错: {str(e)}'
        })


@app.route('/api/clean_orphaned_data', methods=['POST'])
@require_login
def clean_orphaned_data():
    """清理孤立数据"""
    try:
        with price_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 删除孤立的price_history记录
            cursor.execute("""
                DELETE FROM price_history
                WHERE check_method_id NOT IN (
                    SELECT id FROM check_methods
                )
            """)
            deleted_price_history = cursor.rowcount

            # 删除孤立的sample_types记录
            cursor.execute("""
                DELETE FROM sample_types
                WHERE check_method_id NOT IN (
                    SELECT id FROM check_methods
                )
            """)
            deleted_sample_types = cursor.rowcount

            # 删除孤立的check_methods记录
            cursor.execute("""
                DELETE FROM check_methods
                WHERE service_item_id NOT IN (
                    SELECT id FROM service_items
                )
            """)
            deleted_check_methods = cursor.rowcount

            conn.commit()

            return jsonify({
                'success': True,
                'message': '数据清理完成',
                'data': {
                    'deleted_price_history': deleted_price_history,
                    'deleted_sample_types': deleted_sample_types,
                    'deleted_check_methods': deleted_check_methods
                }
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理数据时出错: {str(e)}'
        })


@app.route('/api/methods_by_status')
@require_login
def get_methods_by_status():
    """根据状态获取方法列表API"""
    status = request.args.get('status')
    keyword = request.args.get('keyword', '')
    project_number = request.args.get('project_number', '')
    category = request.args.get('category', '')
    method_name = request.args.get('method_name', '')
    method_name2 = request.args.get('method_name2', '')
    cma = request.args.get('cma', '')
    cnas = request.args.get('cnas', '')
    nhc = request.args.get('nhc', '')
    availability = request.args.get('availability', '')
    date_filter = request.args.get('date_filter', '')
    change_filter = request.args.get('change_filter', '')
    change_date = request.args.get('change_date', '')
    method_ids = request.args.get('method_ids', '')  # 新增方法ID列表参数
    sort_order = request.args.get('sort_order', 'default')
    limit = int(request.args.get('limit', 20))
    offset = int(request.args.get('offset', 0))

    # 转换status参数
    if status is not None:
        try:
            status = int(status)
        except ValueError:
            return jsonify({
                'success': False,
                'message': '无效的状态参数'
            })

    try:
        result = price_manager.get_methods_by_status(
            status=status,
            keyword=keyword,
            project_number=project_number,
            category=category,
            method_name=method_name,
            method_name2=method_name2,
            cma=cma,
            cnas=cnas,
            nhc=nhc,
            availability=availability,
            date_filter=date_filter,
            change_filter=change_filter,
            change_date=change_date,
            method_ids=method_ids,  # 新增方法ID列表参数
            sort_order=sort_order,
            limit=limit,
            offset=offset
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取方法列表时出错: {str(e)}',
            'data': {'methods': [], 'total': 0}
        })


@app.route('/api/update_method_status', methods=['POST'])
@require_login
def update_method_status():
    """更新方法状态API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    method_id = data.get('method_id')
    status = data.get('status')

    if method_id is None or status is None:
        return jsonify({
            'success': False,
            'message': '缺少必要参数: method_id 和 status'
        })

    try:
        method_id = int(method_id)
        status = int(status)

        if status not in [0, 1]:
            return jsonify({
                'success': False,
                'message': '状态值必须为 0(有效) 或 1(废止)'
            })

        result = price_manager.update_method_status(method_id, status)
        return jsonify(result)

    except ValueError:
        return jsonify({
            'success': False,
            'message': '无效的参数格式'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新方法状态时出错: {str(e)}'
        })


@app.route('/api/batch_update_method_status', methods=['POST'])
@require_login
def batch_update_method_status():
    """批量更新方法状态API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    method_ids = data.get('method_ids', [])
    status = data.get('status')

    if not method_ids or status is None:
        return jsonify({
            'success': False,
            'message': '缺少必要参数: method_ids 和 status'
        })

    try:
        # 验证method_ids是列表且包含整数
        if not isinstance(method_ids, list):
            return jsonify({
                'success': False,
                'message': 'method_ids 必须是数组'
            })

        # 转换为整数列表
        method_ids = [int(mid) for mid in method_ids]
        status = int(status)

        if status not in [0, 1]:
            return jsonify({
                'success': False,
                'message': '状态值必须为 0(有效) 或 1(废止)'
            })

        if len(method_ids) > 1000:  # 限制批量操作的数量
            return jsonify({
                'success': False,
                'message': '单次批量操作不能超过1000个方法'
            })

        result = price_manager.batch_update_method_status(method_ids, status)
        return jsonify(result)

    except ValueError:
        return jsonify({
            'success': False,
            'message': '无效的参数格式'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量更新方法状态时出错: {str(e)}'
        })




@app.route('/api/get_method_change_statistics')
@require_login
def get_method_change_statistics():
    """获取基于last_date字段的变动方法统计API（带缓存优化）"""
    try:
        time_range = request.args.get('time_range', 'month')
        months = request.args.get('months', type=int)

        # 验证时间范围参数
        if time_range not in ['year', 'quarter', 'month']:
            return jsonify({
                'success': False,
                'message': '无效的时间范围参数'
            })

        # 验证月数参数
        if months is not None and (months < 1 or months > 24):
            return jsonify({
                'success': False,
                'message': '月数参数必须在1-24之间'
            })

        # 使用缓存优化的统计方法
        from cache_manager import get_cached_method_change_statistics
        statistics = get_cached_method_change_statistics(price_manager, time_range, months)
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取变动方法统计时出错: {str(e)}'
        })


@app.route('/test/api/get_method_change_statistics')
def test_get_method_change_statistics():
    """测试用的方法变动统计API（无需登录）"""
    try:
        time_range = request.args.get('time_range', 'month')
        months = request.args.get('months', type=int)

        # 验证时间范围参数
        if time_range not in ['year', 'quarter', 'month']:
            return jsonify({
                'success': False,
                'message': '无效的时间范围参数'
            })

        # 验证月数参数
        if months is not None and (months < 1 or months > 24):
            return jsonify({
                'success': False,
                'message': '月数参数必须在1-24之间'
            })

        statistics = price_manager.get_method_change_statistics(time_range, months)
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取变动方法统计时出错: {str(e)}'
        })


@app.route('/api/get_methods_by_date')
@require_login
def get_methods_by_date():
    """获取指定日期有变动的方法详细信息API"""
    try:
        target_date = request.args.get('date')

        if not target_date:
            return jsonify({
                'success': False,
                'message': '缺少日期参数'
            })

        methods = price_manager.get_methods_by_date(target_date)
        return jsonify({
            'success': True,
            'data': methods,
            'count': len(methods)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取方法详细信息时出错: {str(e)}'
        })


@app.route('/test/api/get_methods_by_date')
def test_get_methods_by_date():
    """测试用的获取指定日期方法详细信息API（无需登录）"""
    try:
        target_date = request.args.get('date')

        if not target_date:
            return jsonify({
                'success': False,
                'message': '缺少日期参数'
            })

        methods = price_manager.get_methods_by_date(target_date)
        return jsonify({
            'success': True,
            'data': methods,
            'count': len(methods)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取方法详细信息时出错: {str(e)}'
        })


@app.route('/api/get_detailed_change_statistics')
@require_login
def get_detailed_change_statistics():
    """获取详细变动统计API（新增方法、资质变更、价格变更）（带缓存优化）"""
    try:
        time_range = request.args.get('time_range', 'month')
        months = request.args.get('months', type=int)

        # 验证时间范围参数
        if time_range not in ['year', 'quarter', 'month']:
            return jsonify({
                'success': False,
                'message': '无效的时间范围参数'
            })

        # 验证月数参数
        if months is not None and (months < 1 or months > 24):
            return jsonify({
                'success': False,
                'message': '月数参数必须在1-24之间'
            })

        # 使用缓存优化的统计方法
        from cache_manager import get_cached_detailed_change_statistics
        statistics = get_cached_detailed_change_statistics(price_manager, time_range, months)
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取详细变动统计时出错: {str(e)}'
        })


@app.route('/test/api/get_detailed_change_statistics')
def test_get_detailed_change_statistics():
    """测试用的详细变动统计API（无需登录）"""
    try:
        time_range = request.args.get('time_range', 'month')
        months = request.args.get('months', type=int)

        # 验证时间范围参数
        if time_range not in ['year', 'quarter', 'month']:
            return jsonify({
                'success': False,
                'message': '无效的时间范围参数'
            })

        # 验证月数参数
        if months is not None and (months < 1 or months > 24):
            return jsonify({
                'success': False,
                'message': '月数参数必须在1-24之间'
            })

        statistics = price_manager.get_detailed_change_statistics(time_range, months)
        return jsonify({
            'success': True,
            'data': statistics
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取详细变动统计时出错: {str(e)}'
        })


@app.route('/api/clear_statistics_cache', methods=['POST'])
@require_login
def clear_statistics_cache():
    """清理统计数据缓存API"""
    try:
        from cache_manager import clear_statistics_cache
        success = clear_statistics_cache()

        if success:
            return jsonify({
                'success': True,
                'message': '统计数据缓存清理成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '统计数据缓存清理失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理统计数据缓存时出错: {str(e)}'
        })


@app.route('/api/refresh_statistics_cache', methods=['POST'])
@require_login
def refresh_statistics_cache():
    """刷新统计数据缓存API"""
    try:
        from cache_manager import clear_statistics_cache, update_statistics_cache

        # 先清理旧缓存
        clear_success = clear_statistics_cache()
        if not clear_success:
            return jsonify({
                'success': False,
                'message': '清理旧缓存失败'
            })

        # 重新生成缓存
        update_success = update_statistics_cache(price_manager)
        if update_success:
            return jsonify({
                'success': True,
                'message': '统计数据缓存刷新成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '重新生成缓存失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新统计数据缓存时出错: {str(e)}'
        })


@app.route('/api/update_statistics_incrementally', methods=['POST'])
@require_login
def update_statistics_incrementally():
    """增量更新统计数据API"""
    try:
        from cache_manager import update_statistics_incrementally

        success = update_statistics_incrementally()

        if success:
            return jsonify({
                'success': True,
                'message': '增量更新统计数据成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '增量更新统计数据失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'增量更新统计数据时出错: {str(e)}'
        })


@app.route('/api/check_statistics_consistency')
@require_login
def check_statistics_consistency():
    """检查统计数据一致性API"""
    try:
        from cache_manager import check_statistics_consistency

        consistency_report = check_statistics_consistency()

        return jsonify({
            'success': True,
            'data': consistency_report
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查统计数据一致性时出错: {str(e)}'
        })


@app.route('/api/rebuild_statistics_summary', methods=['POST'])
@require_login
def rebuild_statistics_summary():
    """重建统计汇总表API"""
    try:
        from cache_manager import update_statistics_summary_table, clear_statistics_cache

        # 清理缓存
        clear_statistics_cache()

        # 重建统计汇总表
        success = update_statistics_summary_table(price_manager)

        if success:
            return jsonify({
                'success': True,
                'message': '统计汇总表重建成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '统计汇总表重建失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重建统计汇总表时出错: {str(e)}'
        })


@app.route('/api/methods_by_ids')
@require_login
def get_methods_by_ids():
    """根据方法ID列表获取方法详情"""
    try:
        method_ids_str = request.args.get('method_ids', '')
        if not method_ids_str:
            return jsonify({
                'success': False,
                'message': '缺少方法ID参数'
            })

        # 解析方法ID列表
        try:
            method_ids = [int(id.strip()) for id in method_ids_str.split(',') if id.strip()]
        except ValueError:
            return jsonify({
                'success': False,
                'message': '方法ID格式错误'
            })

        if not method_ids:
            return jsonify({
                'success': True,
                'methods': []
            })

        # 获取方法详情
        methods = price_manager.get_methods_by_ids(method_ids)

        return jsonify({
            'success': True,
            'methods': methods
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取方法详情时出错: {str(e)}'
        })


@app.route('/method_details')
@require_login
def method_details_page():
    """方法详情页面"""
    return render_template('method_details_window.html')

@app.route('/api/get_statistics')
@require_login
def get_statistics():
    """获取系统统计信息API"""
    try:
        stats = price_manager.get_statistics()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计信息时出错: {str(e)}'
        })


@app.route('/api/categories')
@require_login
def get_categories():
    """获取分类统计API，支持排序和分页"""
    sort_by = request.args.get('sort_by', 'item_count')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 6, type=int)
    
    try:
        data = price_manager.get_categories_statistics(sort_by, page, per_page)
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/api/categories-chart')
@require_login
def get_categories_chart_data():
    """获取分类图表数据API"""
    try:
        data = price_manager.get_categories_chart_data()
        return jsonify(data)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@app.route('/settings')
@require_login
def settings():
    """设置页面"""
    # 检查是否已验证设置密码
    if not session.get('settings_verified', False):
        return render_template('settings_password.html')

    # 获取当前会话信息
    session_key = session.get('session_key')
    all_sessions = web_login_manager.get_all_sessions()

    return render_template('settings.html',
                         current_session=session_key,
                         all_sessions=all_sessions)


@app.route('/api/verify_settings_password', methods=['POST'])
@require_login
def verify_settings_password():
    """验证设置密码API"""
    data = request.get_json()
    password = data.get('password', '')

    # 固定密码
    SETTINGS_PASSWORD = '5846445576'

    if password == SETTINGS_PASSWORD:
        session['settings_verified'] = True
        return jsonify({
            'success': True,
            'message': '密码验证成功'
        })
    else:
        return jsonify({
            'success': False,
            'message': '密码错误，请重新输入'
        })


@app.route('/favicon.ico')
def favicon():
    """网站图标"""
    return send_file('static/favicon.ico', mimetype='image/vnd.microsoft.icon')


@app.route('/test_fetch.html')
def test_fetch_page():
    """测试页面"""
    with open('test_fetch.html', 'r', encoding='utf-8') as f:
        return f.read()


@app.route('/api/cleanup_sessions', methods=['POST'])
@require_login
def cleanup_sessions():
    """清理过期会话API"""
    try:
        cleaned_count = web_login_manager.cleanup_expired_sessions()
        return jsonify({
            'success': True,
            'message': f'已清理 {cleaned_count} 个过期会话'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理会话时出错: {str(e)}'
        })


@app.route('/api/get_data_statistics')
@require_login
def get_data_statistics():
    """获取数据统计API"""
    try:
        import sqlite3
        import os
        
        db_path = 'kangda_prices.db'
        statistics = {}
        
        # 获取数据库文件大小
        if os.path.exists(db_path):
            db_size = os.path.getsize(db_path)
            if db_size < 1024:
                statistics['db_size'] = f'{db_size} B'
            elif db_size < 1024 * 1024:
                statistics['db_size'] = f'{db_size / 1024:.1f} KB'
            else:
                statistics['db_size'] = f'{db_size / 1024 / 1024:.1f} MB'
        else:
            statistics['db_size'] = '未知'
        
        # 获取各表记录数
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检测项目数
            cursor.execute("SELECT COUNT(*) FROM service_items")
            statistics['service_items'] = cursor.fetchone()[0]
            
            # 检测方法数
            cursor.execute("SELECT COUNT(*) FROM check_methods")
            statistics['check_methods'] = cursor.fetchone()[0]
            
            # 价格历史记录数
            cursor.execute("SELECT COUNT(*) FROM price_history")
            statistics['price_history'] = cursor.fetchone()[0]
            
            # 抓取记录数
            cursor.execute("SELECT COUNT(*) FROM fetch_logs")
            statistics['fetch_logs'] = cursor.fetchone()[0]
        
        return jsonify({
            'success': True,
            'statistics': statistics
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据时出错: {str(e)}'
        })


@app.route('/api/set_manual_baseline_prices', methods=['POST'])
@require_login
def set_manual_baseline_prices():
    """设置手动基准价格API"""
    try:
        data = request.get_json()
        method_ids = data.get('method_ids', [])

        if not method_ids:
            return jsonify({
                'success': False,
                'message': '请选择要设置基准价格的方法'
            })

        # 获取当前用户信息
        username = session.get('username', '未知用户')

        result = price_manager.set_manual_baseline_prices(method_ids, username)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'设置基准价格时出错: {str(e)}'
        })


@app.route('/api/get_manual_baseline_prices')
@require_login
def get_manual_baseline_prices():
    """获取手动基准价格API"""
    try:
        method_ids = request.args.get('method_ids')
        if method_ids:
            method_ids = [int(id.strip()) for id in method_ids.split(',') if id.strip()]
        else:
            method_ids = None

        result = price_manager.get_manual_baseline_prices(method_ids)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取基准价格时出错: {str(e)}'
        })


@app.route('/api/remove_manual_baseline_prices', methods=['POST'])
@require_login
def remove_manual_baseline_prices():
    """移除手动基准价格API"""
    try:
        data = request.get_json()
        method_ids = data.get('method_ids', [])

        if not method_ids:
            return jsonify({
                'success': False,
                'message': '请选择要移除基准价格的方法'
            })

        result = price_manager.remove_manual_baseline_prices(method_ids)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'移除基准价格时出错: {str(e)}'
        })


@app.route('/api/get_manual_baseline_details')
@require_login
def get_manual_baseline_details():
    """获取手动基准价格详细信息API"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        result = price_manager.get_manual_baseline_details(page, per_page)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取手动基准价格详细信息时出错: {str(e)}'
        })


@app.route('/api/clear_data', methods=['POST'])
@require_login
def clear_data():
    """清空数据API"""
    try:
        import sqlite3

        data = request.get_json()
        data_type = data.get('data_type')

        if not data_type:
            return jsonify({
                'success': False,
                'message': '缺少数据类型参数'
            })

        db_path = 'kangda_prices.db'

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            if data_type == 'price_history':
                # 清空价格历史
                cursor.execute("DELETE FROM price_history")
                affected_rows = cursor.rowcount

                # 清理last_date字段
                cursor.execute("UPDATE check_methods SET last_date = NULL WHERE last_date IS NOT NULL")

                message = f'已删除 {affected_rows} 条价格历史记录'

                # 清理相关缓存
                cache_deleted = clear_cache_data()
                if cache_deleted > 0:
                    message += f'，清理了 {cache_deleted} 条缓存记录'

            elif data_type == 'fetch_logs':
                # 清空抓取记录
                cursor.execute("DELETE FROM fetch_logs")
                affected_rows = cursor.rowcount
                message = f'已删除 {affected_rows} 条抓取记录'

            elif data_type == 'cache_only':
                # 仅清空缓存数据，不影响业务数据
                cache_deleted = clear_cache_data()
                message = f'已清理 {cache_deleted} 条缓存记录'

                # 不需要操作主数据库，直接返回结果
                return jsonify({
                    'success': True,
                    'message': message
                })

            elif data_type == 'all_data':
                # 清空所有数据
                tables = ['price_history', 'sample_types', 'check_methods', 'service_items', 'fetch_logs']
                total_deleted = 0

                for table in tables:
                    cursor.execute(f"DELETE FROM {table}")
                    total_deleted += cursor.rowcount

                # 重置自增ID
                cursor.execute("DELETE FROM sqlite_sequence")

                # 清理last_date字段
                cursor.execute("UPDATE check_methods SET last_date = NULL WHERE last_date IS NOT NULL")

                message = f'已删除所有数据，共 {total_deleted} 条记录'

                # 清理缓存数据
                cache_deleted = clear_cache_data()
                if cache_deleted > 0:
                    message += f'，清理了 {cache_deleted} 条缓存记录'

            else:
                return jsonify({
                    'success': False,
                    'message': '无效的数据类型'
                })

            conn.commit()

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空数据时出错: {str(e)}'
        })


# ==================== 标准方法管理路由 ====================

@app.route('/standard_methods')
@require_login
def standard_methods():
    """标准方法管理页面"""
    return render_template('standard_methods.html')


@app.route('/evaluation_methods')
@require_login
def evaluation_methods():
    """评价管理页面"""
    return render_template('evaluation_methods.html')

@app.route('/test_scope_frontend')
@require_login
def test_scope_frontend():
    """适用范围前端测试页面"""
    return send_from_directory('.', 'test_scope_frontend.html')

@app.route('/test_edit_fix')
@require_login
def test_edit_fix():
    """评价方法编辑功能测试页面"""
    return send_from_directory('.', 'test_edit_fix.html')


@app.route('/debug_frontend')
def debug_frontend():
    """调试前端页面（无需登录）"""
    return send_from_directory('.', 'debug_frontend.html')


@app.route('/api/standard_methods/<int:method_id>', methods=['GET'])
@require_login
def get_standard_method_detail(method_id):
    """获取单个标准方法详情API"""
    result = standard_methods_manager.get_standard_method(method_id)
    return jsonify(result)


@app.route('/api/standard_methods', methods=['GET'])
@require_login
def get_standard_methods():
    """获取标准方法列表API"""
    keyword = request.args.get('keyword', '')
    scope_id = request.args.get('scope_id', type=int)
    item_id = request.args.get('item_id', type=int)
    item_name = request.args.get('item_name', '')  # 新增：检测项目名称模糊匹配
    is_appendix = request.args.get('is_appendix')
    is_informative = request.args.get('is_informative')
    status = request.args.get('status', type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 转换布尔值参数
    if is_appendix is not None:
        is_appendix = is_appendix == '1'
    if is_informative is not None:
        is_informative = is_informative == '1'

    offset = (page - 1) * per_page

    result = standard_methods_manager.get_standard_methods(
        keyword=keyword,
        scope_id=scope_id,
        item_id=item_id,
        item_name=item_name,  # 传递检测项目名称参数
        is_appendix=is_appendix,
        is_informative=is_informative,
        status=status,
        limit=per_page,
        offset=offset
    )

    return jsonify(result)


@app.route('/api/standard_methods/<int:method_id>', methods=['GET'])
@require_login
def get_standard_method(method_id):
    """获取单个标准方法详情API"""
    try:
        with standard_methods_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 获取方法基本信息
            cursor.execute("SELECT * FROM standard_methods WHERE id = ?", (method_id,))
            method = cursor.fetchone()

            if not method:
                return jsonify({
                    'success': False,
                    'message': '标准方法不存在'
                })

            method_dict = dict(method)

            # 获取关联的适用范围ID
            cursor.execute("""
                SELECT applicable_scope_id FROM standard_method_scopes
                WHERE standard_method_id = ?
            """, (method_id,))
            scope_ids = [row[0] for row in cursor.fetchall()]
            method_dict['applicable_scope_ids'] = scope_ids

            # 获取关联的检测项目ID
            cursor.execute("""
                SELECT detection_item_id FROM standard_method_items
                WHERE standard_method_id = ?
            """, (method_id,))
            item_ids = [row[0] for row in cursor.fetchall()]
            method_dict['detection_item_ids'] = item_ids

            return jsonify({
                'success': True,
                'data': method_dict
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取标准方法详情失败: {str(e)}'
        })


@app.route('/api/standard_methods', methods=['POST'])
@require_login
def add_standard_method():
    """添加标准方法API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    result = standard_methods_manager.add_standard_method(data)
    return jsonify(result)


@app.route('/api/standard_methods/<int:method_id>', methods=['PUT'])
@require_login
def update_standard_method(method_id):
    """更新标准方法API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    result = standard_methods_manager.update_standard_method(method_id, data)
    return jsonify(result)


@app.route('/api/standard_methods/<int:method_id>', methods=['DELETE'])
@require_login
def delete_standard_method(method_id):
    """删除标准方法API"""
    result = standard_methods_manager.delete_standard_method(method_id)
    return jsonify(result)


@app.route('/api/standard_methods/scopes', methods=['GET'])
@require_login
def get_applicable_scopes():
    """获取适用范围列表API（动态获取）"""
    scopes = standard_methods_manager.get_applicable_scopes()
    return jsonify({
        'success': True,
        'data': scopes,
        'message': f'获取到{len(scopes)}个适用范围（来自标准方法关联）'
    })

@app.route('/api/standard_methods/scopes/stats', methods=['GET'])
@require_login
def get_scopes_with_stats():
    """获取适用范围及统计信息API"""
    result = standard_methods_manager.get_applicable_scopes_with_stats()
    return jsonify(result)


@app.route('/api/standard_methods/items', methods=['GET'])
@require_login
def get_detection_items():
    """获取检测项目列表API"""
    items = standard_methods_manager.get_detection_items()
    return jsonify({
        'success': True,
        'data': items
    })


@app.route('/api/detection_items/<int:item_id>/aliases', methods=['GET'])
@require_login
def get_detection_item_aliases(item_id):
    """获取检测项目别名API"""
    result = standard_methods_manager.get_detection_item_aliases(item_id)
    return jsonify(result)


@app.route('/api/detection_items/<int:item_id>/aliases', methods=['PUT'])
@require_login
def update_detection_item_aliases(item_id):
    """更新检测项目别名API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    aliases = data.get('aliases', [])
    show_in_list = data.get('show_in_list', False)

    # 验证别名格式
    if not isinstance(aliases, list):
        return jsonify({
            'success': False,
            'message': '别名必须是数组格式'
        })

    # 验证每个别名都是字符串
    for alias in aliases:
        if not isinstance(alias, str):
            return jsonify({
                'success': False,
                'message': '别名必须是字符串'
            })

    result = standard_methods_manager.update_detection_item_aliases(
        item_id=item_id,
        aliases=aliases,
        show_in_list=show_in_list
    )

    return jsonify(result)


@app.route('/api/standard_methods/import', methods=['POST'])
@require_login
def import_standard_methods():
    """Excel导入标准方法API"""
    if 'file' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有上传文件'
        })

    file = request.files['file']
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        })

    if not file.filename.lower().endswith(('.xlsx', '.xls')):
        return jsonify({
            'success': False,
            'message': '文件格式不支持，请上传Excel文件'
        })

    try:
        # 使用内存流，避免临时文件问题
        from io import BytesIO

        # 将上传的文件读取到内存流中
        file_stream = BytesIO()
        file.save(file_stream)
        file_stream.seek(0)  # 重置流位置到开始

        # 导入数据
        result = standard_methods_manager.import_from_excel(file_stream)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入失败: {str(e)}'
        })


@app.route('/api/standard_methods/export', methods=['GET'])
@require_login
def export_standard_methods():
    """Excel导出标准方法API"""
    try:
        result = standard_methods_manager.export_to_excel()
        
        if result['success']:
            # 创建文件响应
            return send_file(
                io.BytesIO(result['data']),
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=result['filename']
            )
        else:
            return jsonify(result)
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        })


@app.route('/api/standard_methods/<int:method_id>/alternatives', methods=['GET'])
@require_login
def get_method_alternatives(method_id):
    """获取方法替代方案API"""
    alternatives = standard_methods_manager.get_method_alternatives(method_id)
    return jsonify({
        'success': True,
        'data': alternatives
    })


@app.route('/api/evaluation_methods', methods=['GET'])
@require_login
def get_evaluation_methods():
    """获取评价方法列表API"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    offset = (page - 1) * per_page

    result = standard_methods_manager.get_evaluation_methods(
        limit=per_page,
        offset=offset
    )

    return jsonify(result)


@app.route('/api/evaluation_methods', methods=['POST'])
@require_login
def create_evaluation_method():
    """创建评价方法API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    # 添加创建者信息
    data['created_by'] = session.get('username', '')

    result = standard_methods_manager.create_evaluation_method(data)
    return jsonify(result)


@app.route('/api/evaluation_methods/<int:evaluation_id>', methods=['GET'])
@require_login
def get_evaluation_method(evaluation_id):
    """获取单个评价方法详情API（不包含推荐配置，推荐配置通过分页API获取）"""
    try:
        with standard_methods_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 获取评价方法基本信息
            cursor.execute("""
                SELECT em.*, aps.name as scope_name
                FROM evaluation_methods em
                LEFT JOIN applicable_scopes aps ON em.applicable_scope_id = aps.id
                WHERE em.id = ? AND em.is_active = 1
            """, (evaluation_id,))

            method_data = cursor.fetchone()
            if not method_data:
                return jsonify({
                    'success': False,
                    'message': '评价方法不存在'
                })

            method = dict(method_data)

            # 获取推荐配置总数（用于前端显示）
            cursor.execute("""
                SELECT COUNT(*) as total_recommendations
                FROM evaluation_recommendations er
                LEFT JOIN detection_items di ON er.detection_item_id = di.id
                LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                WHERE er.evaluation_method_id = ?
                  AND er.detection_item_id IS NOT NULL
                  AND er.recommended_standard_method_id IS NOT NULL
                  AND di.id IS NOT NULL
                  AND sm.id IS NOT NULL
            """, (evaluation_id,))

            total_count = cursor.fetchone()[0]
            method['total_recommendations'] = total_count

            return jsonify({
                'success': True,
                'data': method
            })

    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return jsonify({
            'success': False,
            'message': '数据库查询失败'
        })
    except Exception as e:
        print(f"获取评价方法详情失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取评价方法详情失败: {str(e)}'
        })


@app.route('/api/evaluation_methods/<int:evaluation_id>/recommendations', methods=['GET'])
@require_login
def get_evaluation_method_recommendations(evaluation_id):
    """获取评价方法推荐配置的分页API"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        cursor_id = request.args.get('cursor', type=int)  # 游标分页支持

        # 限制每页大小
        size = min(size, 50)
        offset = (page - 1) * size

        with standard_methods_manager.get_connection() as conn:
            db_cursor = conn.cursor()

            # 验证评价方法是否存在
            db_cursor.execute("SELECT id FROM evaluation_methods WHERE id = ? AND is_active = 1", (evaluation_id,))
            if not db_cursor.fetchone():
                return jsonify({
                    'success': False,
                    'message': '评价方法不存在'
                })

            # 构建查询条件
            where_clause = """
                WHERE er.evaluation_method_id = ?
                  AND er.detection_item_id IS NOT NULL
                  AND er.recommended_standard_method_id IS NOT NULL
                  AND di.id IS NOT NULL
                  AND sm.id IS NOT NULL
            """
            params = [evaluation_id]

            # 如果使用游标分页
            if cursor_id:
                where_clause += " AND er.id > ?"
                params.append(cursor_id)

            # 获取总数（仅在第一页或非游标分页时计算）
            total = 0
            if page == 1 and not cursor_id:
                db_cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM evaluation_recommendations er
                    LEFT JOIN detection_items di ON er.detection_item_id = di.id
                    LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                    {where_clause}
                """, params)
                total = db_cursor.fetchone()[0]

            # 获取推荐配置数据
            query = f"""
                SELECT er.*, di.name as detection_item_name, di.display_name as detection_item_display_name,
                       sm.full_standard_number, sm.standard_name
                FROM evaluation_recommendations er
                LEFT JOIN detection_items di ON er.detection_item_id = di.id
                LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                {where_clause}
                ORDER BY er.id
                LIMIT ? OFFSET ?
            """

            if cursor_id:
                # 游标分页不使用OFFSET
                query = query.replace("LIMIT ? OFFSET ?", "LIMIT ?")
                params.extend([size])
            else:
                params.extend([size, offset])

            db_cursor.execute(query, params)

            recommendations = []
            last_cursor = None
            for row in db_cursor.fetchall():
                try:
                    rec_dict = dict(row)
                    # 验证必要字段
                    if (rec_dict.get('detection_item_id') and
                        rec_dict.get('recommended_standard_method_id') and
                        rec_dict.get('detection_item_name') and
                        rec_dict.get('standard_name')):
                        recommendations.append(rec_dict)
                        last_cursor = rec_dict['id']  # 记录最后一个ID作为游标
                except Exception as e:
                    print(f"处理推荐配置记录时出错: {e}")
                    continue

            # 检查是否还有更多数据
            has_next = len(recommendations) == size
            if has_next and last_cursor:
                # 检查是否真的还有下一页
                db_cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM evaluation_recommendations er
                    LEFT JOIN detection_items di ON er.detection_item_id = di.id
                    LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                    WHERE er.evaluation_method_id = ?
                      AND er.detection_item_id IS NOT NULL
                      AND er.recommended_standard_method_id IS NOT NULL
                      AND di.id IS NOT NULL
                      AND sm.id IS NOT NULL
                      AND er.id > ?
                    LIMIT 1
                """, (evaluation_id, last_cursor))
                has_next = db_cursor.fetchone()[0] > 0

            return jsonify({
                'success': True,
                'data': {
                    'recommendations': recommendations,
                    'pagination': {
                        'page': page,
                        'size': size,
                        'total': total,
                        'has_next': has_next,
                        'next_cursor': last_cursor if has_next else None
                    }
                }
            })

    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return jsonify({
            'success': False,
            'message': '数据库查询失败'
        })
    except Exception as e:
        print(f"获取推荐配置失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取推荐配置失败: {str(e)}'
        })


@app.route('/api/standard_methods/search', methods=['GET'])
@require_login
def search_standard_methods():
    """搜索标准方法API（用于选择框懒加载）"""
    try:
        query = request.args.get('q', '').strip()
        limit = min(request.args.get('limit', 20, type=int), 50)

        # 使用优化的搜索方法
        result = standard_methods_manager.search_standard_methods_optimized(query, limit)
        return jsonify(result)

    except Exception as e:
        print(f"搜索标准方法失败: {e}")
        return jsonify({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        })


@app.route('/api/detection_items/search', methods=['GET'])
@require_login
def search_detection_items():
    """搜索检测项目API（用于选择框懒加载）"""
    try:
        query = request.args.get('q', '').strip()
        limit = min(request.args.get('limit', 20, type=int), 50)

        # 使用优化的搜索方法
        result = standard_methods_manager.search_detection_items_optimized(query, limit)
        return jsonify(result)

    except Exception as e:
        print(f"搜索检测项目失败: {e}")
        return jsonify({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        })


@app.route('/api/cache/stats', methods=['GET'])
@require_login
def get_cache_stats():
    """获取缓存统计信息API"""
    try:
        stats = standard_methods_manager.get_cache_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取缓存统计失败: {str(e)}'
        })


@app.route('/api/cache/clear', methods=['POST'])
@require_login
def clear_cache():
    """清除缓存API"""
    try:
        standard_methods_manager.clear_search_cache()
        return jsonify({
            'success': True,
            'message': '缓存已清除'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清除缓存失败: {str(e)}'
        })


@app.route('/api/evaluation_methods/<int:evaluation_id>', methods=['DELETE'])
@require_login
def delete_evaluation_method(evaluation_id):
    """删除评价方法API"""
    try:
        with standard_methods_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 检查评价方法是否存在
            cursor.execute("SELECT id, name FROM evaluation_methods WHERE id = ? AND is_active = 1", (evaluation_id,))
            method = cursor.fetchone()

            if not method:
                return jsonify({
                    'success': False,
                    'message': '评价方法不存在'
                })

            # 删除推荐配置
            cursor.execute("DELETE FROM evaluation_recommendations WHERE evaluation_method_id = ?", (evaluation_id,))

            # 软删除评价方法（设置is_active为0）
            cursor.execute("UPDATE evaluation_methods SET is_active = 0 WHERE id = ?", (evaluation_id,))

            conn.commit()

            return jsonify({
                'success': True,
                'message': '评价方法删除成功'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除评价方法失败: {str(e)}'
        })


@app.route('/api/evaluation_methods/<int:evaluation_id>', methods=['PUT'])
@require_login
def update_evaluation_method(evaluation_id):
    """更新评价方法API"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    try:
        with standard_methods_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 检查评价方法是否存在
            cursor.execute("SELECT id FROM evaluation_methods WHERE id = ? AND is_active = 1", (evaluation_id,))
            if not cursor.fetchone():
                return jsonify({
                    'success': False,
                    'message': '评价方法不存在'
                })

            # 更新评价方法基本信息
            cursor.execute("""
                UPDATE evaluation_methods
                SET name = ?, description = ?, applicable_scope_id = ?
                WHERE id = ?
            """, (
                data['name'],
                data.get('description', ''),
                data.get('applicable_scope_id'),
                evaluation_id
            ))

            # 删除现有的推荐配置
            cursor.execute("DELETE FROM evaluation_recommendations WHERE evaluation_method_id = ?", (evaluation_id,))

            # 添加新的推荐配置
            if 'recommendations' in data:
                for rec in data['recommendations']:
                    cursor.execute("""
                        INSERT INTO evaluation_recommendations
                        (evaluation_method_id, detection_item_id, recommended_standard_method_id,
                         priority, reason)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        evaluation_id,
                        rec['detection_item_id'],
                        rec['recommended_standard_method_id'],
                        rec.get('priority', 1),
                        rec.get('reason', '')
                    ))

            conn.commit()

            return jsonify({
                'success': True,
                'message': '评价方法更新成功'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新评价方法失败: {str(e)}'
        })


@app.route('/api/intelligent_recommendations', methods=['POST'])
@require_login
def get_intelligent_recommendations():
    """智能推荐API - 基于评价方法的推荐系统"""
    data = request.get_json()

    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据不能为空'
        })

    evaluation_method_id = data.get('evaluation_method_id')
    applicable_scope_id = data.get('applicable_scope_id')
    detection_items_text = data.get('detection_items_text', '')

    if not evaluation_method_id:
        return jsonify({
            'success': False,
            'message': '请选择评价方法'
        })

    if not applicable_scope_id:
        return jsonify({
            'success': False,
            'message': '请选择适用范围'
        })

    if not detection_items_text.strip():
        return jsonify({
            'success': False,
            'message': '请输入检测项目'
        })

    result = standard_methods_manager.get_evaluation_based_recommendations(
        evaluation_method_id, applicable_scope_id, detection_items_text
    )

    return jsonify(result)


@app.route('/api/evaluation_methods/<int:evaluation_id>/scopes', methods=['GET'])
@require_login
def get_evaluation_method_scopes(evaluation_id):
    """获取评价方法的所有适用范围API"""
    try:
        result = standard_methods_manager.get_evaluation_method_scopes(evaluation_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取评价方法适用范围失败: {str(e)}'
        })


@app.route('/api/detection_items/<item_name>/alternatives', methods=['GET'])
@require_login
def get_item_alternative_methods(item_name):
    """获取检测项目的所有可用标准方法API（用于悬浮窗显示）"""
    try:
        applicable_scope_id = request.args.get('scope_id', type=int)
        if not applicable_scope_id:
            return jsonify({
                'success': False,
                'message': '请提供适用范围ID'
            })

        result = standard_methods_manager.get_item_alternative_methods(item_name, applicable_scope_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取检测项目可用方法失败: {str(e)}'
        })


@app.route('/api/evaluation_methods/<int:evaluation_id>/import_recommendations', methods=['POST'])
@require_login
def import_evaluation_recommendations(evaluation_id):
    """导入评价方法推荐配置API"""
    if 'file' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有上传文件'
        })

    file = request.files['file']
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        })

    if not file.filename.lower().endswith(('.xlsx', '.xls')):
        return jsonify({
            'success': False,
            'message': '文件格式不支持，请上传Excel文件'
        })

    try:
        # 使用内存流，避免临时文件问题
        from io import BytesIO

        # 将上传的文件读取到内存流中
        file_stream = BytesIO()
        file.save(file_stream)
        file_stream.seek(0)  # 重置流位置到开始

        # 导入数据
        result = standard_methods_manager.import_evaluation_recommendations_from_excel(
            evaluation_method_id=evaluation_id,
            file_path_or_stream=file_stream
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入失败: {str(e)}'
        })


@app.route('/api/evaluation_recommendations_template', methods=['GET'])
@require_login
def get_evaluation_recommendations_template():
    """获取评价方法推荐配置Excel模板API"""
    try:
        template_result = standard_methods_manager.generate_evaluation_recommendations_template()

        if not template_result['success']:
            return jsonify(template_result)

        # 生成Excel文件
        import pandas as pd
        from io import BytesIO

        # 创建DataFrame
        df = pd.DataFrame(template_result['data'])

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 写入数据
            df.to_excel(writer, sheet_name='推荐配置模板', index=False)

            # 写入说明
            instructions_df = pd.DataFrame({
                '使用说明': template_result['instructions']
            })
            instructions_df.to_excel(writer, sheet_name='使用说明', index=False)

        output.seek(0)

        # 返回文件
        from flask import send_file
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='评价方法推荐配置模板.xlsx'
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'生成模板失败: {str(e)}'
        })


@app.route('/api/standard_methods/clear', methods=['POST'])
@require_login
def clear_standard_methods():
    """清空标准方法数据API"""
    data = request.get_json()

    if not data or not data.get('confirm'):
        return jsonify({
            'success': False,
            'message': '请确认清空操作'
        })

    try:
        import sqlite3
        from datetime import datetime

        # 创建备份
        import shutil
        backup_name = f"kangda_prices_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2('kangda_prices.db', backup_name)

        with sqlite3.connect('kangda_prices.db') as conn:
            cursor = conn.cursor()

            # 统计清空前的数据
            cursor.execute("SELECT COUNT(*) FROM standard_methods")
            method_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM applicable_scopes")
            scope_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM detection_items")
            item_count = cursor.fetchone()[0]

            # 按依赖关系顺序删除数据
            delete_operations = [
                'evaluation_recommendations',
                'evaluation_methods',
                'method_alternatives',
                'standard_method_items',
                'standard_method_scopes',
                'standard_methods',
                'detection_items',
                'applicable_scopes'
            ]

            total_deleted = 0
            for table in delete_operations:
                try:
                    cursor.execute(f"DELETE FROM {table}")
                    total_deleted += cursor.rowcount
                except sqlite3.OperationalError:
                    pass  # 表不存在，跳过

            # 重置自增ID
            reset_tables = ['standard_methods', 'applicable_scopes', 'detection_items', 'evaluation_methods']
            for table in reset_tables:
                try:
                    cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
                except:
                    pass

            conn.commit()

            # 压缩数据库
            cursor.execute("VACUUM")

            return jsonify({
                'success': True,
                'message': f'成功清空 {total_deleted} 条记录。备份文件：{backup_name}',
                'data': {
                    'deleted_count': total_deleted,
                    'backup_file': backup_name,
                    'before_stats': {
                        'methods': method_count,
                        'scopes': scope_count,
                        'items': item_count
                    }
                }
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空数据失败: {str(e)}'
        })


if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('static/reports', exist_ok=True)
    
    print("康达价格管理系统 Web界面启动中...")
    print("访问地址: http://localhost:1234")
    print("=" * 50)

    app.run(host='0.0.0.0', port=1234, debug=True)