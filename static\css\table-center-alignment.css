/* 
 * 表格单元格完全居中对齐样式
 * 用于分析方法界面和方法价格全览界面的表格
 */

/* 基础表格单元格居中样式 */
.table-center-alignment th,
.table-center-alignment td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 针对所有表格的通用居中样式 */
.table.column-divider th,
.table.column-divider td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 确保表头也完全居中 */
.table thead th {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 确保表体单元格完全居中 */
.table tbody td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 特殊处理复选框列，保持居中 */
.table th input[type="checkbox"],
.table td input[type="checkbox"] {
    margin: 0 auto;
    display: block;
}

/* 确保按钮和链接在单元格中居中 */
.table td .btn,
.table td .badge,
.table td .text-primary,
.table td .text-danger,
.table td .text-success,
.table td .text-warning {
    display: inline-block;
    text-align: center;
}

/* 处理可点击的项目名称和方法名称链接 */
.table td .project-name-clickable,
.table td .method-name-clickable {
    display: inline-block;
    text-align: center;
}

/* 特殊处理方法名称列 - 强制居中对齐 */
.table td:has(.method-name-clickable) {
    text-align: center !important;
}

/* 兼容性处理 - 如果浏览器不支持:has选择器 */
.table td span.method-name-clickable {
    display: block;
    text-align: center;
    width: 100%;
}

/* 确保价格显示居中 */
.table td .price-cell,
.table td .fw-bold {
    text-align: center !important;
}

/* 处理多行内容的居中对齐 */
.table td small {
    display: block;
    text-align: center;
}

/* 确保状态徽章居中 */
.table td .badge {
    margin: 0 auto;
}

/* 处理表格中的图标居中 */
.table td i,
.table th i {
    text-align: center;
}

/* 响应式表格的居中对齐 */
.table-responsive .table th,
.table-responsive .table td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 特殊处理价格趋势表格 */
#priceHistoryTableHeader th,
#priceHistoryTableBody td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 确保动态生成的表格内容也居中 */
.table-sm th,
.table-sm td {
    text-align: center !important;
    vertical-align: middle !important;
}

/* 处理表格中的删除线文本 */
.table td del {
    text-align: center;
    display: inline-block;
}

/* 确保表格中的所有文本内容都居中 */
.table * {
    text-align: center !important;
}

/* 覆盖可能存在的左对齐样式 */
.table .text-left,
.table .text-start {
    text-align: center !important;
}

/* 覆盖可能存在的右对齐样式 */
.table .text-right,
.table .text-end {
    text-align: center !important;
}
