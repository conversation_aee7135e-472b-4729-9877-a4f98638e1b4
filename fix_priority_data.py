#!/usr/bin/env python3
"""
修复推荐配置优先级数据脚本
将错误设置为3（低优先级）的Excel导入数据修正为1（高优先级）
"""

import sqlite3
import sys
from datetime import datetime

def backup_database(db_path: str) -> str:
    """备份数据库"""
    import shutil
    backup_path = f"{db_path}.priority_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path

def analyze_priority_data(db_path: str):
    """分析当前优先级数据"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 统计优先级分布
            cursor.execute("""
                SELECT priority, COUNT(*) as count
                FROM evaluation_recommendations 
                GROUP BY priority 
                ORDER BY priority
            """)
            
            priority_stats = cursor.fetchall()
            print("当前优先级分布:")
            for priority, count in priority_stats:
                priority_name = {1: "高", 2: "中", 3: "低"}.get(priority, "未知")
                print(f"  优先级 {priority} ({priority_name}): {count} 条记录")
            
            # 统计Excel导入的记录
            cursor.execute("""
                SELECT priority, COUNT(*) as count
                FROM evaluation_recommendations 
                WHERE reason LIKE 'Excel批量导入%'
                GROUP BY priority 
                ORDER BY priority
            """)
            
            excel_stats = cursor.fetchall()
            print("\nExcel导入记录的优先级分布:")
            for priority, count in excel_stats:
                priority_name = {1: "高", 2: "中", 3: "低"}.get(priority, "未知")
                print(f"  优先级 {priority} ({priority_name}): {count} 条记录")
            
            # 查看一些具体的Excel导入记录
            cursor.execute("""
                SELECT er.id, er.priority, di.name as item_name, sm.full_standard_number, er.reason
                FROM evaluation_recommendations er
                LEFT JOIN detection_items di ON er.detection_item_id = di.id
                LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
                WHERE er.reason LIKE 'Excel批量导入%'
                LIMIT 5
            """)
            
            sample_records = cursor.fetchall()
            print("\nExcel导入记录示例:")
            for record in sample_records:
                priority_name = {1: "高", 2: "中", 3: "低"}.get(record[1], "未知")
                print(f"  ID: {record[0]}, 优先级: {record[1]} ({priority_name}), 项目: {record[2]}, 方法: {record[3]}")
                
    except Exception as e:
        print(f"分析数据失败: {e}")

def fix_priority_data(db_path: str, dry_run: bool = True):
    """修复优先级数据"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 查找需要修复的记录（Excel导入且优先级为3的记录）
            cursor.execute("""
                SELECT id, priority, reason
                FROM evaluation_recommendations 
                WHERE reason LIKE 'Excel批量导入%' AND priority = 3
            """)
            
            records_to_fix = cursor.fetchall()
            
            if not records_to_fix:
                print("没有找到需要修复的记录")
                return
            
            print(f"找到 {len(records_to_fix)} 条需要修复的记录")
            
            if dry_run:
                print("这是预览模式，不会实际修改数据")
                print("需要修复的记录:")
                for record in records_to_fix[:10]:  # 只显示前10条
                    print(f"  ID: {record[0]}, 当前优先级: {record[1]}, 理由: {record[2]}")
                if len(records_to_fix) > 10:
                    print(f"  ... 还有 {len(records_to_fix) - 10} 条记录")
                return
            
            # 实际修复数据
            print("开始修复数据...")
            
            # 更新优先级从3改为1
            cursor.execute("""
                UPDATE evaluation_recommendations 
                SET priority = 1 
                WHERE reason LIKE 'Excel批量导入%' AND priority = 3
            """)
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"成功修复 {affected_rows} 条记录的优先级")
            print("修复完成！")
            
    except Exception as e:
        print(f"修复数据失败: {e}")

def verify_fix(db_path: str):
    """验证修复结果"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查是否还有Excel导入且优先级为3的记录
            cursor.execute("""
                SELECT COUNT(*) 
                FROM evaluation_recommendations 
                WHERE reason LIKE 'Excel批量导入%' AND priority = 3
            """)
            
            remaining_count = cursor.fetchone()[0]
            
            if remaining_count == 0:
                print("✅ 验证成功：所有Excel导入记录的优先级都已修正")
            else:
                print(f"❌ 验证失败：仍有 {remaining_count} 条Excel导入记录的优先级为3")
            
            # 显示修复后的统计
            print("\n修复后的优先级分布:")
            analyze_priority_data(db_path)
            
    except Exception as e:
        print(f"验证失败: {e}")

def main():
    """主函数"""
    db_path = 'kangda_prices.db'
    
    print("推荐配置优先级数据修复工具")
    print("="*50)
    print("问题说明：")
    print("- 前端界面定义：1=高，2=中，3=低")
    print("- 但Excel导入时错误地设置为3（低优先级）")
    print("- 实际应该设置为1（高优先级）")
    print("="*50)
    
    if not sqlite3.connect(db_path):
        print(f"错误: 无法连接数据库 {db_path}")
        sys.exit(1)
    
    # 分析当前数据
    print("1. 分析当前数据...")
    analyze_priority_data(db_path)
    
    # 预览修复
    print("\n2. 预览需要修复的数据...")
    fix_priority_data(db_path, dry_run=True)
    
    # 询问是否继续
    response = input(f"\n是否要修复这些数据？(y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        sys.exit(0)
    
    # 备份数据库
    print("\n3. 备份数据库...")
    backup_path = backup_database(db_path)
    
    # 执行修复
    print("\n4. 执行修复...")
    fix_priority_data(db_path, dry_run=False)
    
    # 验证修复结果
    print("\n5. 验证修复结果...")
    verify_fix(db_path)
    
    print(f"\n修复完成！数据库备份保存在: {backup_path}")

if __name__ == "__main__":
    main()
