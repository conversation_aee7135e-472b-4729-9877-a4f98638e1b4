"""
康达价格管理系统 - 缓存管理模块
提供轻量级的数据库缓存功能，用于优化价格变动分析性能
"""

import json
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Optional, Any


class CacheManager:
    """轻量级缓存管理器"""
    
    def __init__(self, db_path: str = 'price_management.db'):
        self.db_path = db_path
        self._lock = threading.Lock()
        self._ensure_cache_table()
    
    def _ensure_cache_table(self):
        """确保缓存表存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS price_change_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        cache_key TEXT UNIQUE NOT NULL,
                        data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL
                    )
                """)
                
                # 创建索引提升查询性能
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_cache_key_expires 
                    ON price_change_cache(cache_key, expires_at)
                """)
                
                conn.commit()
        except Exception as e:
            print(f"创建缓存表失败: {e}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT data FROM price_change_cache 
                    WHERE cache_key = ? AND expires_at > datetime('now')
                """, (key,))
                
                result = cursor.fetchone()
                if result:
                    return json.loads(result[0])
                return None
                
        except Exception as e:
            print(f"读取缓存失败 [{key}]: {e}")
            return None
    
    def set(self, key: str, data: Any, expire_hours: int = 24) -> bool:
        """设置缓存数据"""
        try:
            with self._lock:
                expires_at = datetime.now() + timedelta(hours=expire_hours)
                json_data = json.dumps(data, ensure_ascii=False)
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO price_change_cache 
                        (cache_key, data, expires_at) VALUES (?, ?, ?)
                    """, (key, json_data, expires_at))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            print(f"设置缓存失败 [{key}]: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除指定缓存"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM price_change_cache WHERE cache_key = ?", (key,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除缓存失败 [{key}]: {e}")
            return False
    
    def clear_expired(self) -> int:
        """清理过期缓存"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM price_change_cache WHERE expires_at <= datetime('now')")
                deleted_count = cursor.rowcount
                conn.commit()
                return deleted_count
        except Exception as e:
            print(f"清理过期缓存失败: {e}")
            return 0
    
    def clear_all(self) -> bool:
        """清空所有缓存"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM price_change_cache")
                conn.commit()
                return True
        except Exception as e:
            print(f"清空缓存失败: {e}")
            return False
    
    def get_cache_info(self) -> dict:
        """获取缓存统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总缓存数
                cursor.execute("SELECT COUNT(*) FROM price_change_cache")
                total_count = cursor.fetchone()[0]
                
                # 有效缓存数
                cursor.execute("SELECT COUNT(*) FROM price_change_cache WHERE expires_at > datetime('now')")
                valid_count = cursor.fetchone()[0]
                
                # 过期缓存数
                expired_count = total_count - valid_count
                
                return {
                    'total_count': total_count,
                    'valid_count': valid_count,
                    'expired_count': expired_count
                }
        except Exception as e:
            print(f"获取缓存信息失败: {e}")
            return {'total_count': 0, 'valid_count': 0, 'expired_count': 0}


# 全局缓存管理器实例
cache_manager = CacheManager()


def update_price_change_cache(price_manager, days: int = 7):
    """更新价格变动分析缓存"""
    try:
        print(f"开始更新价格变动分析缓存（{days}天范围）...")
        
        # 计算价格变动数据
        changes = price_manager.get_comprehensive_price_changes(days)
        
        # 缓存数据
        cache_key = f"comprehensive_price_changes_{days}days"
        success = cache_manager.set(cache_key, changes, expire_hours=24)
        
        if success:
            recent_count = len(changes.get('recent_changes', []))
            baseline_count = len(changes.get('baseline_changes', []))
            print(f"价格变动分析缓存更新成功: 近期变动{recent_count}个, 基准变动{baseline_count}个")
        else:
            print("价格变动分析缓存更新失败")
            
        return success
        
    except Exception as e:
        print(f"更新价格变动分析缓存时出错: {e}")
        return False


def get_cached_price_changes(price_manager, days: int = 7):
    """获取缓存的价格变动数据，失败时回退到实时计算"""
    cache_key = f"comprehensive_price_changes_{days}days"

    # 尝试从缓存获取
    cached_data = cache_manager.get(cache_key)
    if cached_data:
        print(f"使用缓存的价格变动分析数据（{days}天范围）")
        return cached_data

    # 缓存未命中，回退到实时计算
    print(f"缓存未命中，回退到实时计算价格变动分析（{days}天范围）")
    return price_manager.get_comprehensive_price_changes(days)


def update_statistics_cache(price_manager):
    """更新统计数据缓存"""
    try:
        print("开始更新统计数据缓存...")

        # 常用的时间范围和月数组合
        cache_configs = [
            ('month', 6), ('month', 12), ('month', 24),
            ('quarter', 8), ('quarter', 12),
            ('year', 24)
        ]

        success_count = 0
        total_count = len(cache_configs)

        for time_range, months in cache_configs:
            try:
                # 更新方法变动统计缓存
                method_stats = price_manager.get_method_change_statistics_v2(time_range, months)
                method_cache_key = f"method_change_statistics_{time_range}_{months}"
                if cache_manager.set(method_cache_key, method_stats, expire_hours=24):
                    print(f"✅ 方法变动统计缓存更新成功: {method_cache_key}")
                    success_count += 1

                # 更新详细变动统计缓存
                detailed_stats = price_manager.get_detailed_change_statistics(time_range, months)
                detailed_cache_key = f"detailed_change_statistics_{time_range}_{months}"
                if cache_manager.set(detailed_cache_key, detailed_stats, expire_hours=24):
                    print(f"✅ 详细变动统计缓存更新成功: {detailed_cache_key}")
                    success_count += 1

            except Exception as e:
                print(f"❌ 更新统计缓存失败 [{time_range}_{months}]: {e}")

        print(f"统计数据缓存更新完成: {success_count}/{total_count * 2} 个缓存项更新成功")
        return success_count > 0

    except Exception as e:
        print(f"更新统计数据缓存时出错: {e}")
        return False


def get_cached_method_change_statistics(price_manager, time_range: str = 'month', months: int = None):
    """获取缓存的方法变动统计数据，失败时回退到实时计算"""
    # 使用默认月数如果未指定
    if months is None:
        default_months = {'year': 24, 'quarter': 12, 'month': 6}.get(time_range, 6)
        months = default_months

    cache_key = f"method_change_statistics_{time_range}_{months}"

    # 尝试从缓存获取
    cached_data = cache_manager.get(cache_key)
    if cached_data:
        print(f"使用缓存的方法变动统计数据: {cache_key}")
        return cached_data

    # 缓存未命中，回退到实时计算
    print(f"缓存未命中，回退到实时计算方法变动统计: {cache_key}")
    result = price_manager.get_method_change_statistics_v2(time_range, months)

    # 将结果缓存起来
    cache_manager.set(cache_key, result, expire_hours=24)
    return result


def get_cached_detailed_change_statistics(price_manager, time_range: str = 'month', months: int = None):
    """获取缓存的详细变动统计数据，失败时回退到实时计算"""
    # 使用默认月数如果未指定
    if months is None:
        default_months = {'year': 24, 'quarter': 12, 'month': 6}.get(time_range, 6)
        months = default_months

    cache_key = f"detailed_change_statistics_{time_range}_{months}"

    # 尝试从缓存获取
    cached_data = cache_manager.get(cache_key)
    if cached_data:
        print(f"使用缓存的详细变动统计数据: {cache_key}")
        return cached_data

    # 缓存未命中，回退到实时计算
    print(f"缓存未命中，回退到实时计算详细变动统计: {cache_key}")
    result = price_manager.get_detailed_change_statistics(time_range, months)

    # 将结果缓存起来
    cache_manager.set(cache_key, result, expire_hours=24)
    return result


def clear_statistics_cache():
    """清理所有统计相关的缓存"""
    try:
        with sqlite3.connect(cache_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                DELETE FROM price_change_cache
                WHERE cache_key LIKE 'method_change_statistics_%'
                   OR cache_key LIKE 'detailed_change_statistics_%'
            """)
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"清理统计缓存完成，删除了 {deleted_count} 个缓存项")
            return True
    except Exception as e:
        print(f"清理统计缓存失败: {e}")
        return False


def update_statistics_summary_table(price_manager):
    """更新统计汇总表数据"""
    try:
        print("开始更新统计汇总表数据...")

        # 导入统计汇总表管理器
        from create_statistics_summary_table import StatisticsSummaryManager

        summary_manager = StatisticsSummaryManager(price_manager.db_path)

        # 检查数据新鲜度
        if summary_manager.check_summary_data_freshness():
            print("统计汇总表数据仍然新鲜，跳过更新")
            return True

        # 重新填充统计汇总表数据
        success = summary_manager.populate_statistics_summary()

        if success:
            print("✅ 统计汇总表数据更新成功")
            # 同时清理相关缓存
            clear_statistics_cache()
        else:
            print("❌ 统计汇总表数据更新失败")

        return success

    except Exception as e:
        print(f"更新统计汇总表数据时出错: {e}")
        return False


def update_statistics_incrementally():
    """增量更新统计数据"""
    try:
        print("开始增量更新统计数据...")

        # 导入增量更新管理器
        from incremental_update_manager import IncrementalUpdateManager

        update_manager = IncrementalUpdateManager()

        # 执行增量更新
        success = update_manager.update_statistics_incrementally()

        if success:
            print("✅ 增量更新统计数据成功")
            # 清理相关缓存
            clear_statistics_cache()
        else:
            print("❌ 增量更新统计数据失败")

        return success

    except Exception as e:
        print(f"增量更新统计数据时出错: {e}")
        return False


def check_statistics_consistency():
    """检查统计数据一致性"""
    try:
        print("开始检查统计数据一致性...")

        # 导入增量更新管理器
        from incremental_update_manager import IncrementalUpdateManager

        update_manager = IncrementalUpdateManager()

        # 执行一致性检查
        consistency_report = update_manager.check_data_consistency()

        return consistency_report

    except Exception as e:
        print(f"检查统计数据一致性时出错: {e}")
        return {
            'consistent': False,
            'issues': [f"检查过程出错: {e}"],
            'checked_at': datetime.now()
        }
