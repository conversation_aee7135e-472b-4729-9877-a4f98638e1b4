-- 数据库查询优化脚本
-- 为评价管理系统添加必要的索引以提升查询性能

-- 1. 评价方法相关索引
CREATE INDEX IF NOT EXISTS idx_evaluation_methods_active ON evaluation_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_evaluation_methods_scope ON evaluation_methods(applicable_scope_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_methods_created_at ON evaluation_methods(created_at);

-- 2. 评价推荐配置相关索引
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_method_id ON evaluation_recommendations(evaluation_method_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_item_id ON evaluation_recommendations(detection_item_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_standard_method_id ON evaluation_recommendations(recommended_standard_method_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_priority ON evaluation_recommendations(priority);

-- 3. 检测项目相关索引
CREATE INDEX IF NOT EXISTS idx_detection_items_name ON detection_items(name);
CREATE INDEX IF NOT EXISTS idx_detection_items_display_name ON detection_items(display_name);

-- 4. 标准方法相关索引
CREATE INDEX IF NOT EXISTS idx_standard_methods_status ON standard_methods(status);
CREATE INDEX IF NOT EXISTS idx_standard_methods_number ON standard_methods(full_standard_number);
CREATE INDEX IF NOT EXISTS idx_standard_methods_name ON standard_methods(standard_name);

-- 5. 复合索引优化
-- 评价推荐配置的复合索引（用于分页查询）
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_composite 
ON evaluation_recommendations(evaluation_method_id, detection_item_id, recommended_standard_method_id);

-- 标准方法的复合索引（用于搜索）
CREATE INDEX IF NOT EXISTS idx_standard_methods_search 
ON standard_methods(status, full_standard_number, standard_name);

-- 检测项目的复合索引（用于搜索）
CREATE INDEX IF NOT EXISTS idx_detection_items_search 
ON detection_items(name, display_name);

-- 6. 全文搜索索引（如果SQLite支持FTS）
-- 注意：这些索引需要根据实际SQLite版本和配置调整

-- 7. 查询统计信息更新
-- SQLite会自动维护统计信息，但可以手动触发分析
ANALYZE;

-- 8. 验证索引创建
-- 查看所有索引
SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND tbl_name IN (
    'evaluation_methods', 
    'evaluation_recommendations', 
    'detection_items', 
    'standard_methods'
) ORDER BY tbl_name, name;
