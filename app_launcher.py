#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
康达价格管理系统 - 启动器
用于打包成exe的主启动文件
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

# 设置运行时环境
from runtime_config import setup_runtime_environment, check_database_files

# 初始化运行时环境
current_dir = setup_runtime_environment()
sys.path.insert(0, current_dir)

# 导入Web应用
from web_app import app, price_manager, web_login_manager


def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("           康达价格管理系统 v1.0")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {current_dir}")
    print("=" * 60)


def check_database():
    """检查数据库连接"""
    # 检查数据库文件是否存在
    missing_files = check_database_files()
    if missing_files:
        print(f"✗ 缺少数据库文件: {', '.join(missing_files)}")
        print("  提示: 请确保数据库文件与程序在同一目录")
        return False

    try:
        stats = price_manager.get_statistics()
        print(f"✓ 数据库连接正常")
        print(f"  - 检测项目: {stats.get('total_items', 0)} 个")
        print(f"  - 检测方法: {stats.get('total_methods', 0)} 个")
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False


def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:1234')
        print("✓ 已自动打开浏览器")
    except Exception as e:
        print(f"✗ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:1234")


def main():
    """主函数"""
    try:
        # 打印启动信息
        print_banner()
        
        # 创建必要的目录
        os.makedirs('static/reports', exist_ok=True)
        os.makedirs('static/exports', exist_ok=True)
        os.makedirs('debug', exist_ok=True)
        os.makedirs('flask_session', exist_ok=True)
        print("✓ 目录结构检查完成")
        
        # 检查数据库
        if not check_database():
            print("\n按任意键退出...")
            input()
            return
        
        print("\n服务器启动中...")
        print("访问地址: http://localhost:1234")
        print("按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 在后台线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=1234,
            debug=False,  # 生产环境关闭debug
            use_reloader=False,  # 避免重复启动
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n服务器已停止")
        print("感谢使用康达价格管理系统！")
    except Exception as e:
        print(f"\n启动失败: {e}")
        print("\n按任意键退出...")
        input()


if __name__ == '__main__':
    main()
