"""
康达价格管理系统 - API响应验证模块
提供统一的API响应格式验证和错误处理机制
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Tuple, Optional


class ApiResponseValidator:
    """API响应验证器"""
    
    def __init__(self, logger_name='api_validator'):
        """初始化验证器
        
        Args:
            logger_name: 日志记录器名称
        """
        self.logger = logging.getLogger(logger_name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def validate_price_api_response(self, response_data: Any, context: str = "") -> Tuple[bool, str, Optional[Dict]]:
        """验证价格管理API响应格式
        
        Args:
            response_data: API响应数据
            context: 上下文信息，用于日志记录
            
        Returns:
            tuple: (is_valid, error_message, validated_data)
                - is_valid: 是否验证通过
                - error_message: 错误消息（用户友好）
                - validated_data: 验证后的数据（如果验证通过）
        """
        try:
            # 记录验证开始
            self.logger.info(f"开始验证API响应 - {context}")
            
            # 1. 检查响应数据是否为空
            if response_data is None:
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"响应数据为空 - {context}")
                return False, error_msg, None
            
            # 2. 检查是否为字典类型
            if not isinstance(response_data, dict):
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"响应数据不是字典类型: {type(response_data)} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 3. 检查必要字段是否存在
            if 'rc' not in response_data:
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"响应缺少'rc'字段 - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 4. 检查返回码
            rc = response_data.get('rc')
            if rc != 0:
                # 根据返回码判断错误类型
                if rc == -1:
                    error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                elif rc == 1:
                    error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                else:
                    error_msg = f"数据获取失败，可能是因为您没有访问权限或API服务异常（错误代码：{rc}）"
                
                self.logger.error(f"API返回错误码: {rc} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 5. 检查ret字段
            if 'ret' not in response_data:
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"响应缺少'ret'字段 - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            ret_data = response_data.get('ret')
            if not isinstance(ret_data, dict):
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'ret'字段不是字典类型: {type(ret_data)} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 6. 检查会话超时
            if ret_data.get('ifTimeOut') == '1':
                error_msg = "会话已超时，请重新登录后再试"
                self.logger.warning(f"会话超时 - {context}")
                return False, error_msg, None
            
            # 7. 检查数据字段
            if 'total' not in ret_data:
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'ret'字段缺少'total'字段 - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            if 'rows' not in ret_data:
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'ret'字段缺少'rows'字段 - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 8. 验证数据类型
            total = ret_data.get('total')
            if not isinstance(total, (int, str)):
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'total'字段类型错误: {type(total)} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            rows = ret_data.get('rows')
            if not isinstance(rows, list):
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'rows'字段不是列表类型: {type(rows)} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 9. 转换total为整数
            try:
                total_int = int(total)
            except (ValueError, TypeError):
                error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
                self.logger.error(f"'total'字段无法转换为整数: {total} - {context}")
                self._log_response_details(response_data, context)
                return False, error_msg, None
            
            # 验证通过，返回标准化数据
            validated_data = {
                'total': total_int,
                'rows': rows,
                'original_response': response_data
            }
            
            self.logger.info(f"API响应验证通过 - {context}, total: {total_int}, rows: {len(rows)}")
            return True, "", validated_data
            
        except Exception as e:
            error_msg = "数据获取失败，可能是因为您没有访问权限或API服务异常"
            self.logger.error(f"验证过程中发生异常: {str(e)} - {context}")
            self._log_response_details(response_data, context)
            return False, error_msg, None
    
    def _log_response_details(self, response_data: Any, context: str):
        """记录响应详细信息用于调试
        
        Args:
            response_data: 响应数据
            context: 上下文信息
        """
        try:
            # 记录响应数据类型
            self.logger.debug(f"响应数据类型: {type(response_data)} - {context}")
            
            # 如果是字符串，记录前500个字符
            if isinstance(response_data, str):
                preview = response_data[:500] + "..." if len(response_data) > 500 else response_data
                self.logger.debug(f"响应内容预览: {preview} - {context}")
            
            # 如果是字典，记录结构信息
            elif isinstance(response_data, dict):
                keys = list(response_data.keys())
                self.logger.debug(f"响应字典键: {keys} - {context}")
                
                # 记录完整响应（用于调试）
                try:
                    response_str = json.dumps(response_data, ensure_ascii=False, indent=2)
                    if len(response_str) > 2000:
                        response_str = response_str[:2000] + "..."
                    self.logger.debug(f"完整响应内容: {response_str} - {context}")
                except Exception:
                    self.logger.debug(f"无法序列化响应数据 - {context}")
            
            # 其他类型
            else:
                self.logger.debug(f"响应数据: {str(response_data)[:500]} - {context}")
                
        except Exception as e:
            self.logger.error(f"记录响应详情时发生错误: {str(e)} - {context}")
    
    def get_user_friendly_error_message(self, error_type: str, original_error: str = "") -> str:
        """获取用户友好的错误消息
        
        Args:
            error_type: 错误类型
            original_error: 原始错误消息
            
        Returns:
            str: 用户友好的错误消息
        """
        error_messages = {
            'permission_denied': "数据获取失败，可能是因为您没有访问权限或API服务异常。请联系系统管理员检查您的账户权限。",
            'session_timeout': "会话已超时，请重新登录后再试。",
            'network_error': "网络连接异常，请检查网络连接后重试。",
            'server_error': "服务器异常，请稍后重试或联系系统管理员。",
            'data_format_error': "数据获取失败，可能是因为您没有访问权限或API服务异常。",
            'unknown_error': "发生未知错误，请联系系统管理员。"
        }
        
        return error_messages.get(error_type, error_messages['unknown_error'])
