"""
数据库查询缓存管理器
用于优化评价管理系统的数据库查询性能
"""

import sqlite3
import json
import time
import hashlib
from typing import Dict, Any, Optional, List, Tuple
from functools import wraps
import threading

class DatabaseCacheManager:
    """数据库查询缓存管理器"""
    
    def __init__(self, cache_ttl: int = 300):  # 默认缓存5分钟
        self.cache_ttl = cache_ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_lock = threading.RLock()
        
    def _generate_cache_key(self, query: str, params: tuple = ()) -> str:
        """生成缓存键"""
        cache_data = {
            'query': query.strip(),
            'params': params
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        return time.time() - cache_entry['timestamp'] < self.cache_ttl
    
    def get_cached_result(self, query: str, params: tuple = ()) -> Optional[List[Dict[str, Any]]]:
        """获取缓存的查询结果"""
        cache_key = self._generate_cache_key(query, params)
        
        with self.cache_lock:
            if cache_key in self.cache:
                cache_entry = self.cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    return cache_entry['result']
                else:
                    # 缓存过期，删除
                    del self.cache[cache_key]
        
        return None
    
    def set_cached_result(self, query: str, params: tuple, result: List[Dict[str, Any]]) -> None:
        """设置查询结果缓存"""
        cache_key = self._generate_cache_key(query, params)
        
        with self.cache_lock:
            self.cache[cache_key] = {
                'result': result,
                'timestamp': time.time()
            }
    
    def clear_cache(self, pattern: str = None) -> None:
        """清除缓存"""
        with self.cache_lock:
            if pattern is None:
                self.cache.clear()
            else:
                # 清除匹配模式的缓存
                keys_to_remove = []
                for key in self.cache.keys():
                    if pattern in key:
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    del self.cache[key]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            valid_entries = 0
            expired_entries = 0
            
            for cache_entry in self.cache.values():
                if self._is_cache_valid(cache_entry):
                    valid_entries += 1
                else:
                    expired_entries += 1
            
            return {
                'total_entries': len(self.cache),
                'valid_entries': valid_entries,
                'expired_entries': expired_entries,
                'cache_ttl': self.cache_ttl
            }

# 全局缓存管理器实例
cache_manager = DatabaseCacheManager()

def cached_query(ttl: int = 300):
    """查询缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 尝试从缓存获取结果
            # 这里需要根据具体的查询函数来生成缓存键
            # 暂时跳过缓存，直接执行查询
            return func(*args, **kwargs)
        return wrapper
    return decorator

class OptimizedQueryMixin:
    """优化查询混入类"""
    
    def execute_cached_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """执行带缓存的查询"""
        # 检查缓存
        cached_result = cache_manager.get_cached_result(query, params)
        if cached_result is not None:
            return cached_result
        
        # 执行查询
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            # 转换结果为字典列表
            columns = [description[0] for description in cursor.description]
            result = []
            for row in cursor.fetchall():
                result.append(dict(zip(columns, row)))
        
        # 缓存结果
        cache_manager.set_cached_result(query, params, result)
        
        return result
    
    def execute_optimized_search(self, table: str, search_fields: List[str], 
                                query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """执行优化的搜索查询"""
        if not query.strip():
            # 返回最常用的项目
            if table == 'detection_items':
                sql = """
                    SELECT di.*, COUNT(er.id) as usage_count
                    FROM detection_items di
                    LEFT JOIN evaluation_recommendations er ON di.id = er.detection_item_id
                    GROUP BY di.id
                    ORDER BY usage_count DESC, di.display_name
                    LIMIT ?
                """
                return self.execute_cached_query(sql, (limit,))
            elif table == 'standard_methods':
                sql = """
                    SELECT sm.*, COUNT(er.id) as usage_count
                    FROM standard_methods sm
                    LEFT JOIN evaluation_recommendations er ON sm.id = er.recommended_standard_method_id
                    WHERE sm.status = 0
                    GROUP BY sm.id
                    ORDER BY usage_count DESC, sm.full_standard_number
                    LIMIT ?
                """
                return self.execute_cached_query(sql, (limit,))
        
        # 构建搜索查询
        where_conditions = []
        params = []
        
        for field in search_fields:
            where_conditions.append(f"{field} LIKE ?")
            params.append(f"%{query}%")
        
        where_clause = " OR ".join(where_conditions)
        
        # 构建排序条件（精确匹配优先）
        order_conditions = []
        for i, field in enumerate(search_fields):
            order_conditions.append(f"CASE WHEN {field} LIKE ? THEN {i+1} ELSE {len(search_fields)+1} END")
            params.append(f"{query}%")
        
        order_clause = ", ".join(order_conditions)
        
        sql = f"""
            SELECT * FROM {table}
            WHERE {where_clause}
            ORDER BY {order_clause}, {search_fields[0]}
            LIMIT ?
        """
        params.append(limit)
        
        return self.execute_cached_query(sql, tuple(params))

def apply_database_optimizations(db_path: str) -> None:
    """应用数据库优化"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 读取并执行优化SQL
            with open('database_optimization.sql', 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            # 分割并执行每个SQL语句
            statements = sql_script.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except sqlite3.Error as e:
                        print(f"执行SQL语句失败: {statement[:50]}... 错误: {e}")
            
            conn.commit()
            print("数据库优化完成")
            
    except Exception as e:
        print(f"应用数据库优化失败: {e}")

if __name__ == "__main__":
    # 测试缓存管理器
    cache_manager = DatabaseCacheManager()
    
    # 模拟查询
    test_query = "SELECT * FROM test WHERE id = ?"
    test_params = (1,)
    test_result = [{'id': 1, 'name': 'test'}]
    
    # 设置缓存
    cache_manager.set_cached_result(test_query, test_params, test_result)
    
    # 获取缓存
    cached = cache_manager.get_cached_result(test_query, test_params)
    print(f"缓存结果: {cached}")
    
    # 获取统计信息
    stats = cache_manager.get_cache_stats()
    print(f"缓存统计: {stats}")
