# 智能推荐系统重构指南

## 重构背景

根据用户需求，智能推荐系统的交互逻辑需要调整，以更好地反映评价方法与适用范围的关系。

### 原有逻辑（错误）
1. 用户选择适用范围（检测类型）
2. 用户选择检测项目列表
3. 系统推荐标准方法

### 新逻辑（正确）
1. **用户选择评价方法**
2. **系统自动显示该评价方法的所有适用范围**
3. **用户选择适用范围**
4. **用户输入/粘贴检测项目**（支持顿号分隔）
5. **系统根据评价方法推荐配置进行检索推荐**（考虑别名）

## 核心概念

### 评价方法与适用范围的关系
- 当一个评价方法有多个适用范围时，每个适用范围都会新建一个评价方法记录
- 这些评价方法的名称相同，但适用范围不同
- 每个评价方法记录都有独立的推荐配置

### 别名管理
- 系统支持检测项目的别名匹配
- 例如：镉、Cd、cadmium 都能匹配到同一个检测项目
- 支持自定义检测项目的模糊匹配

## 技术实现

### 1. 后端API更新

#### 新增API接口

**获取评价方法适用范围**
```
GET /api/evaluation_methods/{id}/scopes
```
返回指定评价方法名称的所有适用范围。

**智能推荐API（重构）**
```
POST /api/intelligent_recommendations
```
请求参数：
```json
{
    "evaluation_method_id": 1,
    "applicable_scope_id": 2,
    "detection_items_text": "镉、铅、汞、铬、砷"
}
```

#### 核心方法实现

**`get_evaluation_method_scopes()`**
- 根据评价方法ID查找同名方法的所有适用范围
- 返回评价方法名称和适用范围列表

**`get_evaluation_based_recommendations()`**
- 基于评价方法推荐配置进行智能推荐
- 支持顿号和逗号分隔的检测项目文本
- 支持别名匹配和模糊匹配

**`_find_evaluation_recommendations()`**
- 查找评价方法中匹配的推荐配置
- 支持系统检测项目和自定义检测项目
- 支持别名匹配和模糊匹配

### 2. 前端界面重构

#### 新的界面布局
```html
<form id="recommendationForm" class="row g-3">
    <!-- 评价方法选择 -->
    <div class="col-md-3">
        <label for="evaluationMethodSelect">评价方法 *</label>
        <select id="evaluationMethodSelect" required>
            <option value="">请选择评价方法</option>
        </select>
    </div>
    
    <!-- 适用范围选择（动态加载） -->
    <div class="col-md-3">
        <label for="applicableScopeSelect">适用范围 *</label>
        <select id="applicableScopeSelect" required disabled>
            <option value="">请先选择评价方法</option>
        </select>
    </div>
    
    <!-- 检测项目输入 -->
    <div class="col-md-4">
        <label for="detectionItemsInput">检测项目 *</label>
        <textarea id="detectionItemsInput" rows="2" 
                  placeholder="请输入检测项目，多个项目用顿号（、）或逗号（,）分隔" required></textarea>
        <div class="form-text">支持别名匹配，如：Cd、Pb、Hg等</div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="col-md-2">
        <button type="submit" class="btn btn-success">获取推荐</button>
        <button type="button" class="btn btn-outline-secondary" onclick="clearRecommendationForm()">清空</button>
    </div>
</form>
```

#### 核心JavaScript函数

**`updateEvaluationMethodSelect()`**
- 按名称分组评价方法
- 避免重复显示同名方法

**`loadMethodScopes()`**
- 根据选择的评价方法加载适用范围
- 动态更新适用范围选择框

**`getRecommendations()`**
- 使用新的API进行推荐请求
- 支持文本输入的检测项目

**`displayRecommendations()`**
- 按检测项目分组显示推荐结果
- 显示匹配率和匹配详情
- 使用手风琴布局提升用户体验

### 3. 数据流程

#### 用户操作流程
1. **选择评价方法** → 触发 `loadMethodScopes()`
2. **选择适用范围** → 启用检测项目输入
3. **输入检测项目** → 支持顿号/逗号分隔
4. **点击获取推荐** → 调用新的推荐API
5. **查看推荐结果** → 按项目分组显示

#### 数据处理流程
1. **解析检测项目文本** → 分割为项目列表
2. **别名匹配** → 查找系统检测项目
3. **自定义项目匹配** → 查找评价方法中的自定义项目
4. **模糊匹配** → 对未匹配项目进行模糊搜索
5. **结果排序** → 按优先级和匹配度排序

## 推荐结果展示

### 匹配率计算
- **匹配率 = 匹配项目数 / 输入项目数**
- 颜色标识：≥80%绿色，≥50%黄色，<50%红色

### 结果分组显示
- 按检测项目分组
- 每个项目显示所有匹配的推荐方法
- 显示优先级、匹配类型、推荐理由

### 优先级标识
- **高优先级（1）**：绿色徽章
- **中优先级（2）**：黄色徽章
- **低优先级（3）**：灰色徽章

### 匹配类型说明
- **系统项目**：在系统检测项目中找到精确匹配
- **自定义项目**：在评价方法的自定义项目中找到匹配
- **模糊匹配**：通过模糊搜索找到相似项目

## 优势特点

### 1. 更符合业务逻辑
- 基于评价方法的推荐配置进行推荐
- 体现评价方法与适用范围的正确关系
- 支持同名评价方法的多个适用范围

### 2. 更好的用户体验
- 渐进式选择，逻辑清晰
- 支持文本输入，便于批量输入
- 智能别名匹配，减少输入错误

### 3. 更强的匹配能力
- 支持系统项目和自定义项目
- 支持精确匹配和模糊匹配
- 支持别名管理系统

### 4. 更丰富的结果展示
- 详细的匹配分析
- 清晰的优先级标识
- 分组展示，便于查看

## 兼容性说明

### 向后兼容
- 保留原有的推荐API（`get_intelligent_recommendations`）
- 新旧系统可以并存
- 数据库结构无需修改

### 渐进升级
- 可以逐步迁移到新的推荐逻辑
- 支持A/B测试
- 用户可以选择使用新旧系统

## 部署指南

### 1. 后端部署
- 更新 `web_app.py` 中的API接口
- 更新 `standard_methods_manager.py` 中的推荐逻辑
- 确保数据库连接正常

### 2. 前端部署
- 更新 `templates/evaluation_methods.html`
- 测试新的交互流程
- 验证推荐结果显示

### 3. 测试验证
- 测试评价方法选择
- 测试适用范围加载
- 测试检测项目输入和推荐
- 验证别名匹配功能

## 后续优化建议

### 1. 性能优化
- 实现推荐结果缓存
- 优化数据库查询
- 添加搜索索引

### 2. 功能增强
- 支持推荐结果导出
- 添加推荐历史记录
- 实现推荐结果收藏

### 3. 用户体验
- 添加输入提示和自动完成
- 实现拖拽排序
- 添加快捷操作按钮

## 技术文档

### 相关文件
- `web_app.py` - API接口实现
- `standard_methods_manager.py` - 推荐逻辑实现
- `templates/evaluation_methods.html` - 前端界面
- `INTELLIGENT_RECOMMENDATION_SYSTEM_GUIDE.md` - 本文档

### API文档
详细的API接口文档请参考代码注释和接口实现。

### 数据库表
- `evaluation_methods` - 评价方法表
- `evaluation_recommendations` - 推荐配置表
- `detection_items` - 检测项目表
- `standard_methods` - 标准方法表
- `applicable_scopes` - 适用范围表
