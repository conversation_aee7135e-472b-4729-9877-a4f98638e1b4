-- 康达价格管理系统数据库结构设计
-- SQLite数据库结构

-- 服务分类表
CREATE TABLE service_categories (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 二级分类表
CREATE TABLE second_categories (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    service_category_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id)
);

-- 检测项目表
CREATE TABLE service_items (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    number TEXT UNIQUE NOT NULL,
    service_category_id INTEGER,
    second_category_id INTEGER,
    service_category_name TEXT,
    second_category_name TEXT,
    child_item TEXT,
    status INTEGER DEFAULT 0,
    auto_rule TEXT,
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id),
    FOREIGN KEY (second_category_id) REFERENCES second_categories(id)
);

-- 检测方法表
CREATE TABLE check_methods (
    id INTEGER PRIMARY KEY,
    method_no TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    service_item_id INTEGER,
    price DECIMAL(10,2),
    according_by_ratio BOOLEAN DEFAULT FALSE,
    lowest_bid DECIMAL(10,2),
    cma INTEGER DEFAULT 0,
    cnas INTEGER DEFAULT 0,
    gov_agree INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,
    remark TEXT,
    method_remark TEXT,
    analysis_cost DECIMAL(10,2),
    order_index INTEGER,
    last_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_item_id) REFERENCES service_items(id)
);

-- 样品类型表
CREATE TABLE sample_types (
    id INTEGER PRIMARY KEY,
    check_method_id INTEGER,
    type TEXT NOT NULL,
    price DECIMAL(10,2),
    sample_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (check_method_id) REFERENCES check_methods(id)
);

-- 价格历史记录表（核心表）
CREATE TABLE price_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_item_id INTEGER,
    check_method_id INTEGER,
    sample_type_id INTEGER,
    check_method_price DECIMAL(10,2),
    sample_type_price DECIMAL(10,2),
    analysis_cost DECIMAL(10,2),
    lowest_bid DECIMAL(10, 2),
    sample_cost DECIMAL(10,2),
    cma_status INTEGER,
    cnas_status INTEGER,
    gov_agree_status INTEGER,
    fetch_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_item_id) REFERENCES service_items(id),
    FOREIGN KEY (check_method_id) REFERENCES check_methods(id),
    FOREIGN KEY (sample_type_id) REFERENCES sample_types(id)
);

-- 数据抓取记录表
CREATE TABLE fetch_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT,
    page_number INTEGER NOT NULL,
    total_records INTEGER,
    fetched_records INTEGER,
    status TEXT DEFAULT 'success',
    error_message TEXT,
    fetch_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_start_time TIMESTAMP,
    session_end_time TIMESTAMP
);

-- 数据抓取状态表（用于并发控制）
CREATE TABLE fetch_status (
    id INTEGER PRIMARY KEY CHECK (id = 1), -- 确保只有一条记录
    is_running INTEGER DEFAULT 0, -- 0: 空闲, 1: 进行中
    task_id TEXT,
    user_session_key TEXT,
    username TEXT,
    start_time TIMESTAMP,
    timeout_time TIMESTAMP,
    progress INTEGER DEFAULT 0,
    message TEXT DEFAULT '空闲',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引优化查询性能
CREATE INDEX idx_service_items_number ON service_items(number);
CREATE INDEX idx_check_methods_method_no ON check_methods(method_no);
CREATE INDEX idx_price_history_service_item ON price_history(service_item_id);
CREATE INDEX idx_price_history_check_method ON price_history(check_method_id);
CREATE INDEX idx_price_history_fetch_time ON price_history(fetch_time);
CREATE INDEX idx_fetch_logs_fetch_time ON fetch_logs(fetch_time);

-- 创建视图方便查询
CREATE VIEW price_trend_view AS
SELECT 
    si.name as service_item_name,
    si.number as service_item_number,
    cm.name as check_method_name,
    cm.method_no,
    st.type as sample_type,
    ph.check_method_price,
    ph.sample_type_price,
    ph.analysis_cost,
    ph.sample_cost,
    ph.cma_status,
    ph.cnas_status,
    ph.fetch_time,
    si.service_category_name,
    si.second_category_name
FROM price_history ph
LEFT JOIN service_items si ON ph.service_item_id = si.id
LEFT JOIN check_methods cm ON ph.check_method_id = cm.id
LEFT JOIN sample_types st ON ph.sample_type_id = st.id
ORDER BY ph.fetch_time DESC;