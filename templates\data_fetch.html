{% extends "base.html" %}

{% block title %}数据抓取 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-download me-2"></i>数据抓取
    </h1>
</div>

<!-- 抓取状态卡片 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>抓取任务状态
                </h5>
            </div>
            <div class="card-body">
                <div id="fetch-status-content">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
                        <h5>正在检查抓取状态...</h5>
                        <p>请稍候</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 抓取历史 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>抓取历史
                </h5>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadFetchHistory()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="fetch-history-content">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p class="mt-2">加载抓取历史...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度模态框 -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>数据抓取进行中
                </h5>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span id="progress-text">准备开始...</span>
                        <span id="progress-percent">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="progress-bar" role="progressbar" 
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
                
                <div id="progress-details" class="text-muted">
                    <small>请勿关闭此窗口，抓取过程可能需要几分钟时间...</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancel-btn" onclick="cancelFetch()" disabled>
                    取消抓取
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTaskId = null;
let progressInterval = null;

$(document).ready(function() {
    // 页面加载时检查当前抓取状态
    checkCurrentFetchStatus();
    // 获取抓取历史
    loadFetchHistory();
});

// 检查当前抓取状态
function checkCurrentFetchStatus() {
    apiRequest('/api/current_fetch_status')
        .then(data => {
            if (data.success) {
                displayFetchStatus(data.status);
            } else {
                displayFetchStatusError('无法获取抓取状态');
            }
        })
        .catch(error => {
            console.error('检查抓取状态失败:', error);
            displayFetchStatusError('检查抓取状态失败');
        });
}

// 显示抓取状态
function displayFetchStatus(status) {
    const container = document.getElementById('fetch-status-content');

    if (status.is_running) {
        // 有抓取任务正在进行
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-cog fa-spin fa-3x mb-3 text-primary"></i>
                <h5 class="text-primary">数据抓取进行中</h5>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">执行用户</h6>
                                <p class="card-text">${status.username || '未知用户'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">开始时间</h6>
                                <p class="card-text">${status.start_time ? new Date(status.start_time).toLocaleString('zh-CN') : '未知'}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress mb-2">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: ${status.progress || 0}%">
                            ${status.progress || 0}%
                        </div>
                    </div>
                    <p class="text-muted">${status.message || '正在处理...'}</p>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    当前有其他用户正在进行数据抓取，请等待完成后再试
                </div>
            </div>
        `;

        // 如果有任务在进行，检查是否是当前用户的任务
        if (status.task_id && status.task_id.includes(new Date().toISOString().slice(0, 10).replace(/-/g, ''))) {
            // 可能是当前用户的任务，启动实时进度监控
            currentTaskId = status.task_id;
            if (!progressInterval) {
                startInlineProgressMonitoring();
            }
        } else {
            // 其他用户的任务，定期刷新状态
            setTimeout(checkCurrentFetchStatus, 5000);
        }
    } else {
        // 空闲状态，可以开始抓取
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-cloud-download-alt fa-3x mb-3 text-success"></i>
                <h5 class="text-success">系统空闲</h5>
                <p class="text-muted">点击下方按钮开始抓取康达网站的最新价格数据</p>
                <button type="button" class="btn btn-primary btn-lg" onclick="startFetch()">
                    <i class="fas fa-play me-2"></i>开始抓取数据
                </button>
            </div>
        `;
    }
}

// 显示状态错误
function displayFetchStatusError(message) {
    const container = document.getElementById('fetch-status-content');

    // 检查是否为权限相关错误
    const isPermissionError = message.includes('没有访问权限') ||
                             message.includes('API服务异常') ||
                             message.includes('会话已超时');

    let suggestions = '';
    if (isPermissionError) {
        suggestions = `
            <div class="mt-3 p-3 bg-light rounded text-start">
                <h6 class="text-muted mb-2"><i class="fas fa-lightbulb me-1"></i>建议解决方案：</h6>
                <ul class="mb-0 text-muted small">
                    <li>检查网络连接是否正常</li>
                    <li>尝试重新登录系统</li>
                    <li>联系系统管理员检查服务状态</li>
                </ul>
            </div>
        `;
    }

    container.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
            <h5 class="text-warning">状态检查失败</h5>
            <p class="text-muted">${message}</p>
            ${suggestions}
            <div class="mt-3">
                <button type="button" class="btn btn-outline-primary me-2" onclick="checkCurrentFetchStatus()">
                    <i class="fas fa-sync-alt me-1"></i>重新检查
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                    <i class="fas fa-refresh me-1"></i>刷新页面
                </button>
            </div>
        </div>
    `;
}

// 开始抓取数据
function startFetch() {
    // 先检查当前状态
    checkCurrentFetchStatus();

    // 发送开始抓取请求
    apiRequest('/api/start_fetch', {
        method: 'POST',
        body: JSON.stringify({})
    })
    .then(data => {
        if (data.success) {
            currentTaskId = data.task_id;
            // 移除模态框，改为使用页面内嵌进度条
            startInlineProgressMonitoring();
            // 立即刷新状态显示为进行中状态
            setTimeout(checkCurrentFetchStatus, 500);
        } else {
            // 显示详细的错误信息
            showErrorAlert('启动抓取任务失败', data.message);
            // 刷新状态显示
            checkCurrentFetchStatus();
        }
    })
    .catch(error => {
        console.error('启动抓取任务失败:', error);
        showErrorAlert('启动抓取任务失败', '网络错误，请检查连接后重试');
        checkCurrentFetchStatus();
    });
}

// 显示错误提示
function showErrorAlert(title, message) {
    // 检查是否为权限相关错误
    const isPermissionError = message.includes('没有访问权限') ||
                             message.includes('API服务异常') ||
                             message.includes('会话已超时');

    let suggestions = '';
    if (isPermissionError) {
        suggestions = `
            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="text-muted mb-2"><i class="fas fa-lightbulb me-1"></i>建议解决方案：</h6>
                <ul class="mb-0 text-muted small">
                    <li>检查您的账户是否有数据抓取权限</li>
                    <li>尝试重新登录系统</li>
                    <li>联系系统管理员检查API服务状态</li>
                    <li>如果问题持续存在，请联系技术支持</li>
                </ul>
            </div>
        `;
    }

    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>${title}</h6>
            <p class="mb-0">${message}</p>
            ${suggestions}
            <div class="mt-3">
                <button type="button" class="btn btn-sm btn-outline-danger me-2" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>刷新页面
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.href='/login'">
                    <i class="fas fa-sign-in-alt me-1"></i>重新登录
                </button>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在页面顶部显示错误信息
    const container = document.querySelector('.main-content');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 自动滚动到错误提示
    container.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// 显示进度模态框
function showProgressModal() {
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();
}

// 开始监控进度
function startProgressMonitoring() {
    if (!currentTaskId) return;
    
    progressInterval = setInterval(() => {
        checkFetchProgress();
    }, 2000); // 每2秒检查一次
}

// 开始内嵌进度监控（新的进度监控方式）
function startInlineProgressMonitoring() {
    if (!currentTaskId) return;

    progressInterval = setInterval(() => {
        checkInlineFetchProgress();
    }, 2000); // 每2秒检查一次
}

// 检查内嵌进度（新的进度检查方式）
function checkInlineFetchProgress() {
    if (!currentTaskId) return;

    apiRequest(`/api/fetch_status/${currentTaskId}`)
        .then(data => {
            if (data.success) {
                const task = data.task;
                updateInlineProgress(task);

                // 如果任务完成或失败，停止监控
                if (task.status === 'completed' || task.status === 'failed') {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        showInlineFetchResult(task);
                        loadFetchHistory(); // 刷新历史记录
                        checkCurrentFetchStatus(); // 刷新状态显示
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.error('检查进度失败:', error);
            // 如果检查进度失败，也刷新状态显示
            checkCurrentFetchStatus();
        });
}

// 更新内嵌进度显示
function updateInlineProgress(task) {
    // 直接更新页面内嵌的进度显示
    const container = document.getElementById('fetch-status-content');
    if (!container) return;

    const progress = task.progress || 0;
    const message = task.message || '正在处理...';

    container.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-cog fa-spin fa-3x mb-3 text-primary"></i>
            <h5 class="text-primary">数据抓取进行中</h5>
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">任务ID</h6>
                            <p class="card-text"><code>${currentTaskId}</code></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">开始时间</h6>
                            <p class="card-text">${task.start_time ? new Date(task.start_time).toLocaleString('zh-CN') : '刚刚开始'}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="progress mb-2" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: ${progress}%">
                        ${progress}%
                    </div>
                </div>
                <p class="text-muted">${message}</p>
            </div>
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                数据抓取正在进行中，请耐心等待完成
            </div>
        </div>
    `;
}

// 显示内嵌抓取结果
function showInlineFetchResult(task) {
    const container = document.getElementById('fetch-status-content');
    if (!container) return;

    if (task.status === 'completed') {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                <h5 class="text-success">数据抓取完成！</h5>
                <div class="alert alert-success mt-3">
                    <h6 class="alert-heading">抓取成功</h6>
                    <p class="mb-0">${task.message}</p>
                    ${task.total_count ? `<hr><small>共处理 ${task.total_count} 条数据</small>` : ''}
                </div>
                <button type="button" class="btn btn-primary" onclick="checkCurrentFetchStatus()">
                    <i class="fas fa-sync-alt me-2"></i>刷新状态
                </button>
            </div>
        `;
    } else {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                <h5 class="text-warning">数据抓取失败</h5>
                <div class="alert alert-warning mt-3">
                    <h6 class="alert-heading">抓取失败</h6>
                    <p class="mb-0">${task.message}</p>
                </div>
                <button type="button" class="btn btn-primary" onclick="checkCurrentFetchStatus()">
                    <i class="fas fa-sync-alt me-2"></i>重新检查状态
                </button>
            </div>
        `;
    }
}

// 检查抓取进度（保留原有函数以兼容）
function checkFetchProgress() {
    if (!currentTaskId) return;
    
    apiRequest(`/api/fetch_status/${currentTaskId}`)
        .then(data => {
            if (data.success) {
                const task = data.task;
                updateProgress(task);
                
                // 如果任务完成或失败，停止监控
                if (task.status === 'completed' || task.status === 'failed') {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        // 不再使用模态框，改为内嵌显示
                        showInlineFetchResult(task);
                        loadFetchHistory(); // 刷新历史记录
                        checkCurrentFetchStatus(); // 刷新状态显示
                    }, 2000);
                }
            }
        });
}

// 更新进度显示
function updateProgress(task) {
    const progressText = document.getElementById('progress-text');
    const progressPercent = document.getElementById('progress-percent');
    const progressBar = document.getElementById('progress-bar');
    const progressDetails = document.getElementById('progress-details');
    
    progressText.textContent = task.message || '处理中...';
    
    let percent = task.progress || 0;
    
    // 确保进度百分比在合理范围内
    if (task.status === 'running' && (!task.progress || task.progress <= 0)) {
        // 如果没有具体进度或进度为0，根据时间计算一个模拟进度
        try {
            const startTime = new Date(task.start_time);
            const now = new Date();
            const elapsedSeconds = Math.max(0, (now - startTime) / 1000);
            // 每10秒增长10%，最多到90%
            percent = Math.min(90, elapsedSeconds / 10 * 10);
        } catch (e) {
            // 如果时间解析失败，使用固定进度
            percent = 10;
        }
    }
    
    // 确保进度在0-100范围内
    percent = Math.max(0, Math.min(100, percent));
    
    progressPercent.textContent = Math.round(percent) + '%';
    progressBar.style.width = percent + '%';
    progressBar.setAttribute('aria-valuenow', percent);
    
    // 更新进度条颜色
    progressBar.className = 'progress-bar';
    if (task.status === 'completed') {
        progressBar.classList.add('bg-success');
        progressPercent.textContent = '100%';
        progressBar.style.width = '100%';
    } else if (task.status === 'failed') {
        progressBar.classList.add('bg-danger');
    } else {
        progressBar.classList.add('bg-primary');
    }
    
    // 更新详细信息
    if (task.total_count) {
        progressDetails.innerHTML = `
            <small class="text-muted">
                总数据量: ${task.total_count} 个项目<br>
                开始时间: ${formatDateTime(task.start_time)}
            </small>
        `;
    }
}

// 隐藏进度模态框
function hideProgressModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
    if (modal) {
        modal.hide();
    }
}

// 显示抓取结果
function showFetchResult(task) {
    const statusContent = document.getElementById('fetch-status-content');
    
    if (task.status === 'completed') {
        statusContent.innerHTML = `
            <div class="text-center text-success py-4">
                <i class="fas fa-check-circle fa-3x mb-3"></i>
                <h5 class="text-success">数据抓取完成！</h5>
                <p class="text-muted">${task.message}</p>
                <button type="button" class="btn btn-success me-2" onclick="location.href='/dashboard'">
                    <i class="fas fa-tachometer-alt me-1"></i>查看控制台
                </button>
                <button type="button" class="btn btn-primary" onclick="startFetch()">
                    <i class="fas fa-redo me-1"></i>重新抓取
                </button>
            </div>
        `;
    } else if (task.status === 'failed') {
        // 检查是否为权限相关错误
        const isPermissionError = task.message.includes('没有访问权限') ||
                                 task.message.includes('API服务异常') ||
                                 task.message.includes('会话已超时');

        let suggestions = '';
        if (isPermissionError) {
            suggestions = `
                <div class="mt-3 p-3 bg-light rounded text-start">
                    <h6 class="text-muted mb-2"><i class="fas fa-lightbulb me-1"></i>建议解决方案：</h6>
                    <ul class="mb-0 text-muted small">
                        <li>检查您的账户是否有数据抓取权限</li>
                        <li>尝试重新登录系统</li>
                        <li>联系系统管理员检查API服务状态</li>
                        <li>如果问题持续存在，请联系技术支持</li>
                    </ul>
                </div>
            `;
        }

        statusContent.innerHTML = `
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                <h5 class="text-danger">抓取失败</h5>
                <p class="text-muted">${task.message}</p>
                ${suggestions}
                <div class="mt-3">
                    <button type="button" class="btn btn-danger me-2" onclick="startFetch()">
                        <i class="fas fa-redo me-1"></i>重试抓取
                    </button>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新页面
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="location.href='/login'">
                        <i class="fas fa-sign-in-alt me-1"></i>重新登录
                    </button>
                </div>
            </div>
        `;
    }
}

// 取消抓取（目前仅隐藏模态框）
function cancelFetch() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    hideProgressModal();
    currentTaskId = null;
}

// 加载抓取历史
function loadFetchHistory() {
    showLoading('fetch-history-content', '加载抓取历史...');

    // 调用获取抓取历史的API
    apiRequest('/api/fetch_history?limit=20')
        .then(data => {
            if (data.success) {
                displayFetchHistory(data.data.logs, data.data.summary);
            } else {
                document.getElementById('fetch-history-content').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        获取抓取历史失败: ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('fetch-history-content').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    获取抓取历史时出错: ${error.message}
                </div>
            `;
        });
}

// 显示抓取历史
function displayFetchHistory(history, summary) {
    const container = document.getElementById('fetch-history-content');

    if (!history || history.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-history fa-2x mb-3"></i>
                <p>暂无抓取历史记录</p>
            </div>
        `;
        return;
    }

    // 显示汇总信息
    let html = '';
    if (summary) {
        html += `
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center py-2">
                            <h5 class="card-title mb-1">${summary.total_sessions || 0}</h5>
                            <p class="card-text small mb-0">总抓取次数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center py-2">
                            <h5 class="card-title mb-1">${summary.successful_sessions || 0}</h5>
                            <p class="card-text small mb-0">成功次数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center py-2">
                            <h5 class="card-title mb-1">${summary.total_fetched || 0}</h5>
                            <p class="card-text small mb-0">总抓取记录数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center py-2">
                            <h5 class="card-title mb-1">${summary.last_fetch_time ? formatDateTime(summary.last_fetch_time) : '-'}</h5>
                            <p class="card-text small mb-0">最后抓取时间</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    html += '<div class="table-responsive"><table class="table table-hover table-sm" style="font-size: 0.95rem;">';
    html += `
        <thead class="table-light">
            <tr>
                <th width="20%">抓取时间</th>
                <th width="8%">页数</th>
                <th width="10%">状态</th>
                <th width="15%">数据量</th>
                <th width="47%">结果信息</th>
            </tr>
        </thead>
        <tbody>
    `;

    history.forEach(record => {
        let statusBadge;
        if (record.status === 'success') {
            statusBadge = '<span class="badge bg-success">成功</span>';
        } else if (record.status === 'partial') {
            statusBadge = '<span class="badge bg-warning">部分成功</span>';
        } else {
            statusBadge = '<span class="badge bg-danger">失败</span>';
        }

        const dataInfo = `${record.total_fetched || 0}/${record.total_records || 0}`;
        const fetchTime = record.fetch_time ? formatDateTime(record.fetch_time) : '-';
        const resultMessage = record.error_message || record.message || '抓取完成';
        const pageInfo = record.total_pages ? `${record.total_pages}页` : (record.page || '-');

        html += `
            <tr>
                <td>${fetchTime}</td>
                <td class="text-center">${pageInfo}</td>
                <td>${statusBadge}</td>
                <td class="text-center">${dataInfo}</td>
                <td>${resultMessage}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}
</script>
{% endblock %}