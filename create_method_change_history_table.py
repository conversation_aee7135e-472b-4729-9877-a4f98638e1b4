#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方法变动历史表创建脚本
用于解决方法变动统计被覆盖的问题
"""

import sqlite3
import os
from datetime import datetime


class MethodChangeHistoryMigration:
    """方法变动历史表迁移类"""
    
    def __init__(self, db_path='kangda_prices.db'):
        self.db_path = db_path
    
    def create_method_change_history_table(self):
        """创建方法变动历史表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建方法变动历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS method_change_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        method_id INTEGER NOT NULL,
                        change_date DATE NOT NULL,
                        detected_time DATETIME NOT NULL,
                        change_type TEXT NOT NULL,
                        field_name TEXT,
                        old_value TEXT,
                        new_value TEXT,
                        fetch_session_id TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (method_id) REFERENCES check_methods (id)
                    )
                ''')
                
                # 创建索引以优化查询性能
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_method_id ON method_change_history(method_id)",
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_change_date ON method_change_history(change_date)",
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_detected_time ON method_change_history(detected_time)",
                    "CREATE INDEX IF NOT EXISTS idx_method_change_history_change_type ON method_change_history(change_type)"
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                conn.commit()
                print("✅ method_change_history 表创建成功")
                return True
                
        except Exception as e:
            print(f"❌ 创建 method_change_history 表失败: {e}")
            return False
    
    def check_table_exists(self):
        """检查表是否已存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='method_change_history'
                """)
                return cursor.fetchone() is not None
        except Exception as e:
            print(f"❌ 检查表存在性失败: {e}")
            return False
    
    def migrate_existing_data(self):
        """迁移现有数据：基于check_methods表的last_date创建初始记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询所有有last_date的方法
                cursor.execute("""
                    SELECT id, last_date 
                    FROM check_methods 
                    WHERE last_date IS NOT NULL
                """)
                
                methods_with_changes = cursor.fetchall()
                
                if not methods_with_changes:
                    print("ℹ️  没有找到需要迁移的历史变动数据")
                    return True
                
                # 为每个方法创建初始变动记录
                migration_time = datetime.now()
                for method_id, last_date in methods_with_changes:
                    cursor.execute("""
                        INSERT INTO method_change_history 
                        (method_id, change_date, detected_time, change_type, field_name, 
                         old_value, new_value, fetch_session_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        method_id,
                        last_date,  # 使用原始的变动日期
                        migration_time,  # 迁移时间作为发现时间
                        'historical_migration',
                        'last_date',
                        None,  # 历史数据无法获取旧值
                        last_date,
                        'migration_' + datetime.now().strftime('%Y%m%d_%H%M%S')
                    ))
                
                conn.commit()
                print(f"✅ 成功迁移 {len(methods_with_changes)} 条历史变动记录")
                return True
                
        except Exception as e:
            print(f"❌ 迁移现有数据失败: {e}")
            return False
    
    def run_migration(self):
        """执行完整的迁移过程"""
        print("=" * 60)
        print("康达价格管理系统 - 方法变动历史表迁移")
        print("解决方法变动统计被覆盖的问题")
        print("=" * 60)
        
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        try:
            # 备份数据库
            backup_path = f"{self.db_path}.backup_method_change_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ 数据库已备份到: {backup_path}")
            
            # 检查表是否已存在
            if self.check_table_exists():
                print("ℹ️  method_change_history 表已存在，跳过创建")
            else:
                # 创建新表
                if not self.create_method_change_history_table():
                    return False
            
            # 迁移现有数据
            if not self.migrate_existing_data():
                return False
            
            print("✅ 方法变动历史表迁移完成！")
            print("\n📋 迁移说明:")
            print("1. 创建了 method_change_history 表用于记录所有方法变动")
            print("2. 迁移了现有的 last_date 数据作为历史记录")
            print("3. 后续的数据抓取将自动记录变动到此表")
            print("4. 统计功能将基于此表提供准确的变动统计")
            
            return True
            
        except Exception as e:
            print(f"❌ 迁移过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    migration = MethodChangeHistoryMigration()
    success = migration.run_migration()
    
    if success:
        print("\n🎉 迁移成功完成！")
    else:
        print("\n💥 迁移失败，请检查错误信息")
    
    return success


if __name__ == "__main__":
    main()
