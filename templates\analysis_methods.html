{% extends "base.html" %}

{% block title %}分析方法 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center pt-2 pb-2 mb-3 border-bottom">
    <h1 class="h4">
        <i class="fas fa-microscope me-2"></i>分析方法
    </h1>
</div>

<!-- Tab导航 -->
<div class="row mb-3">
    <div class="col-12">
        <ul class="nav nav-tabs" id="methodTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="active-methods-tab" data-bs-toggle="tab" 
                        data-bs-target="#active-methods" type="button" role="tab">
                    <i class="fas fa-check-circle me-1"></i>有效方法
                    <span id="active-count" class="badge bg-success ms-1">0</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="disabled-methods-tab" data-bs-toggle="tab" 
                        data-bs-target="#disabled-methods" type="button" role="tab">
                    <i class="fas fa-times-circle me-1"></i>废止方法
                    <span id="disabled-count" class="badge bg-warning ms-1">0</span>
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- Tab内容 -->
<div class="tab-content" id="methodTabContent">
    <!-- 有效方法标签页 -->
    <div class="tab-pane fade show active" id="active-methods" role="tabpanel">
        <!-- 搜索表单 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-2">
                        <form id="active-search-form">
                            <!-- 第一行：主要搜索条件 -->
                            <div class="row g-2 mb-2 align-items-center">
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-hashtag"></i></span>
                                        <input type="text" class="form-control" id="active-project-number"
                                               placeholder="项目编号">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-project-diagram"></i></span>
                                        <input type="text" class="form-control" id="active-project-name"
                                               placeholder="项目名称/子项目">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-tags"></i></span>
                                        <input type="text" class="form-control" id="active-category"
                                               placeholder="检测分类">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-flask"></i></span>
                                        <input type="text" class="form-control" id="active-method-name"
                                               placeholder="方法名称1">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-plus"></i></span>
                                        <input type="text" class="form-control" id="active-method-name2"
                                               placeholder="方法名称2">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary btn-sm w-100" onclick="searchActiveMethods(1)">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 第二行：筛选条件和操作 -->
                            <div class="row g-2 align-items-center">
                                <div class="col-md-2">
                                    <select class="form-select form-select-sm" id="active-availability-filter">
                                        <option value="">可用性</option>
                                        <option value="available">可用</option>
                                        <option value="unavailable">不可用</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex gap-2 align-items-center">
                                        <span class="text-muted small">资质:</span>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="active-cma-filter">
                                            <label class="form-check-label small" for="active-cma-filter">CMA</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="active-cnas-filter">
                                            <label class="form-check-label small" for="active-cnas-filter">CNAS</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="active-nhc-filter">
                                            <label class="form-check-label small" for="active-nhc-filter">NHC</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        支持多条件组合搜索，方法名称支持模糊匹配
                                    </small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearActiveSearch()">
                                        <i class="fas fa-eraser me-1"></i>清空
                                    </button>
                                </div>
                                <!-- 隐藏的日期筛选器，保留功能 -->
                                <div class="col-md-2" style="display: none;">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="active-date-filter"
                                               title="筛选指定日期变动的方法">
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- 批量操作工具栏 -->
                        <div class="row mt-2" id="active-batch-toolbar" style="display: none;">
                            <div class="col-12">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <span class="text-muted me-3">
                                            已选择 <span id="active-selected-count" class="fw-bold text-primary">0</span> 个方法
                                        </span>
                                        <button type="button" class="btn btn-warning btn-sm me-2" onclick="batchDisableMethods()" id="active-batch-disable-btn">
                                            <i class="fas fa-times me-1"></i>批量废止
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearActiveSelection()">
                                            <i class="fas fa-times"></i>清空选择
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 有效方法列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <div id="active-methods-results">
                            <div class="text-center p-4 text-muted">
                                <i class="fas fa-search fa-2x mb-2"></i>
                                <p>请使用上方搜索功能查找有效方法</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div id="active-pagination-container" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
            <div class="d-flex align-items-center">
                <span class="text-muted me-2">每页显示:</span>
                <select class="form-select form-select-sm" style="width: auto;" id="active-page-size" onchange="changeActivePageSize()">
                    <option value="10">10</option>
                    <option value="20" selected>20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-muted ms-2">条</span>
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="active-pagination">
                </ul>
            </nav>
        </div>
    </div>

    <!-- 废止方法标签页 -->
    <div class="tab-pane fade" id="disabled-methods" role="tabpanel">
        <!-- 搜索表单 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-2">
                        <form id="disabled-search-form">
                            <!-- 第一行：主要搜索条件 -->
                            <div class="row g-2 mb-2 align-items-center">
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-hashtag"></i></span>
                                        <input type="text" class="form-control" id="disabled-project-number"
                                               placeholder="项目编号">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-project-diagram"></i></span>
                                        <input type="text" class="form-control" id="disabled-project-name"
                                               placeholder="项目名称/子项目">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-tags"></i></span>
                                        <input type="text" class="form-control" id="disabled-category"
                                               placeholder="检测分类">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-flask"></i></span>
                                        <input type="text" class="form-control" id="disabled-method-name"
                                               placeholder="方法名称1">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text bg-light"><i class="fas fa-plus"></i></span>
                                        <input type="text" class="form-control" id="disabled-method-name2"
                                               placeholder="方法名称2">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary btn-sm w-100" onclick="searchDisabledMethods(1)">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 第二行：筛选条件和操作 -->
                            <div class="row g-2 align-items-center">
                                <div class="col-md-2">
                                    <select class="form-select form-select-sm" id="disabled-availability-filter">
                                        <option value="">可用性</option>
                                        <option value="available">可用</option>
                                        <option value="unavailable">不可用</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex gap-2 align-items-center">
                                        <span class="text-muted small">资质:</span>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="disabled-cma-filter">
                                            <label class="form-check-label small" for="disabled-cma-filter">CMA</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="disabled-cnas-filter">
                                            <label class="form-check-label small" for="disabled-cnas-filter">CNAS</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="disabled-nhc-filter">
                                            <label class="form-check-label small" for="disabled-nhc-filter">NHC</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        支持多条件组合搜索，方法名称支持模糊匹配
                                    </small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDisabledSearch()">
                                        <i class="fas fa-eraser me-1"></i>清空
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- 批量操作工具栏 -->
                        <div class="row mt-2" id="disabled-batch-toolbar" style="display: none;">
                            <div class="col-12">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <span class="text-muted me-3">
                                            已选择 <span id="disabled-selected-count" class="fw-bold text-primary">0</span> 个方法
                                        </span>
                                        <button type="button" class="btn btn-success btn-sm me-2" onclick="batchEnableMethods()" id="disabled-batch-enable-btn">
                                            <i class="fas fa-check me-1"></i>批量恢复
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDisabledSelection()">
                                            <i class="fas fa-times"></i>清空选择
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 废止方法列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <div id="disabled-methods-results">
                            <div class="text-center p-4 text-muted">
                                <i class="fas fa-search fa-2x mb-2"></i>
                                <p>请使用上方搜索功能查找废止方法</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div id="disabled-pagination-container" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
            <div class="d-flex align-items-center">
                <span class="text-muted me-2">每页显示:</span>
                <select class="form-select form-select-sm" style="width: auto;" id="disabled-page-size" onchange="changeDisabledPageSize()">
                    <option value="10">10</option>
                    <option value="20" selected>20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-muted ms-2">条</span>
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="disabled-pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 确认对话框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="confirmModalTitle">
                    <i class="fas fa-exclamation-triangle me-2"></i>确认操作
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <p id="confirmModalMessage" class="mb-0"></p>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmModalConfirm">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 子项目弹窗 -->
<div class="modal fade" id="childItemsModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="childItemsModalTitle">
                    <i class="fas fa-list me-2"></i>子项目列表
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="childItemsContent">
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 价格趋势弹窗 -->
<div class="modal fade" id="priceTrendModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="priceTrendModalTitle">
                    <i class="fas fa-chart-line me-2"></i>价格趋势图
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="priceTrendContent">
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载价格趋势数据中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let activeCurrentPage = 1;
let activeTotalPages = 1;
let activeCurrentProjectNumber = '';
let activeCurrentProjectName = '';
let activeCurrentCategory = '';
let activeCurrentMethodName = '';
let activeCurrentLimit = 20;

let disabledCurrentPage = 1;
let disabledTotalPages = 1;
let disabledCurrentProjectNumber = '';
let disabledCurrentProjectName = '';
let disabledCurrentCategory = '';
let disabledCurrentMethodName = '';
let disabledCurrentLimit = 20;

// 批量选择相关变量
let activeSelectedMethods = new Set();
let disabledSelectedMethods = new Set();



// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查URL参数，如果有日期筛选参数则自动应用
    const urlParams = new URLSearchParams(window.location.search);
    const dateFilter = urlParams.get('date_filter');
    const changeFilter = urlParams.get('change_filter');
    const changeDate = urlParams.get('change_date');
    const methodIds = urlParams.get('method_ids');

    if (dateFilter) {
        // 设置日期筛选字段
        document.getElementById('active-date-filter').value = dateFilter;

        // 显示提示信息
        const alertHtml = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                已自动筛选 <strong>${dateFilter}</strong> 的方法变动记录
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在搜索表单前插入提示
        const searchForm = document.querySelector('#active-search-form').closest('.card');
        searchForm.insertAdjacentHTML('beforebegin', alertHtml);
    }

    if (changeFilter && changeDate) {
        // 设置变动类型筛选（隐藏字段）
        let filterDescription = '';
        switch(changeFilter) {
            case 'new_methods':
                filterDescription = '新增方法';
                break;
            case 'qualification_changes':
                filterDescription = '资质变更';
                break;
            case 'price_changes':
                filterDescription = '价格变更';
                break;
            default:
                filterDescription = '方法变动';
        }

        // 显示精确筛选提示信息
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-filter me-2"></i>
                已精确筛选 <strong>${changeDate}</strong> 的<strong>${filterDescription}</strong>记录
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在搜索表单前插入提示
        const searchForm = document.querySelector('#active-search-form').closest('.card');
        searchForm.insertAdjacentHTML('beforebegin', alertHtml);

        // 设置隐藏的筛选参数
        window.activeChangeFilter = changeFilter;
        window.activeChangeDate = changeDate;
    }

    // 处理方法ID列表筛选
    if (methodIds) {
        // 设置全局变量，用于API调用
        window.activeMethodIds = methodIds;

        // 隐藏其他筛选控件（可选）
        const searchForm = document.querySelector('#active-search-form');
        if (searchForm) {
            // 可以选择隐藏搜索表单或添加只读提示
            const filterInfo = document.createElement('div');
            filterInfo.className = 'alert alert-primary mb-3';
            filterInfo.innerHTML = `
                <i class="fas fa-list me-2"></i>
                <strong>已筛选指定方法列表</strong> - 共 ${methodIds.split(',').length} 个方法
                <a href="/analysis_methods" class="btn btn-outline-primary btn-sm ms-2">
                    <i class="fas fa-times me-1"></i>清除筛选
                </a>
            `;
            searchForm.closest('.card').insertAdjacentElement('beforebegin', filterInfo);
        }

        // 显示变动类型和日期信息（如果有）
        if (changeFilter && changeDate) {
            let filterDescription = '';
            switch(changeFilter) {
                case 'new_methods':
                    filterDescription = '新增方法';
                    break;
                case 'qualification_changes':
                    filterDescription = '资质变更';
                    break;
                case 'price_changes':
                    filterDescription = '价格变更';
                    break;
                default:
                    filterDescription = '方法变动';
            }

            const changeInfo = document.createElement('div');
            changeInfo.className = 'alert alert-success mb-3';
            changeInfo.innerHTML = `
                <i class="fas fa-calendar me-2"></i>
                显示 <strong>${changeDate}</strong> 的 <strong>${filterDescription}</strong>
            `;

            const existingAlert = document.querySelector('.alert-primary');
            if (existingAlert) {
                existingAlert.insertAdjacentElement('afterend', changeInfo);
            }
        }
    }

    // 初始化搜索和计数
    searchActiveMethods(1);
    // 直接调用废止方法搜索来获取计数，但不显示结果
    loadDisabledMethodsCount();

    // 添加回车键事件监听
    ['active-project-number', 'active-project-name', 'active-category', 'active-method-name'].forEach(id => {
        document.getElementById(id).addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchActiveMethods(1);
            }
        });
    });

    ['disabled-project-number', 'disabled-project-name', 'disabled-category', 'disabled-method-name'].forEach(id => {
        document.getElementById(id).addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchDisabledMethods(1);
            }
        });
    });
    
    
    // Tab切换事件
    document.getElementById('disabled-methods-tab').addEventListener('shown.bs.tab', function() {
        if (disabledCurrentPage === 1 && !disabledCurrentProjectNumber && !disabledCurrentProjectName && !disabledCurrentCategory && !disabledCurrentMethodName) {
            searchDisabledMethods(1);
        }
    });

    // 添加事件委托处理项目名称点击和方法名称点击
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('project-name-clickable')) {
            const projectName = e.target.getAttribute('data-project-name');
            const childItems = e.target.getAttribute('data-child-items');
            showChildItems(projectName, childItems);
        } else if (e.target.classList.contains('method-name-clickable')) {
            const methodId = e.target.getAttribute('data-method-id');
            const methodName = e.target.getAttribute('data-method-name');
            const projectName = e.target.getAttribute('data-project-name');
            showPriceTrend(methodId, methodName, projectName);
        }
    });

});

// 加载废止方法计数（不显示结果，只更新计数）
function loadDisabledMethodsCount() {
    console.log('[DEBUG] 开始加载废止方法计数...');

    const params = new URLSearchParams({
        status: '1',  // 废止方法
        limit: '1',   // 只需要总数
        offset: '0'
    });

    apiRequest(`/api/methods_by_status?${params.toString()}`)
        .then(data => {
            console.log('[DEBUG] 废止方法计数API响应:', data);
            if (data.success) {
                // 更新废止方法计数
                document.getElementById('disabled-count').textContent = data.data.total;
                console.log('[DEBUG] 更新废止方法计数为:', data.data.total);
            } else {
                console.error('[DEBUG] 废止方法计数API返回失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取废止方法计数失败:', error);
        });
}

// 更新所有方法计数（在状态变更后调用）
function updateAllMethodCounts() {
    console.log('[DEBUG] 更新所有方法计数...');

    // 更新有效方法计数
    const activeParams = new URLSearchParams({
        status: '0',  // 有效方法
        limit: '1',   // 只需要总数
        offset: '0'
    });

    apiRequest(`/api/methods_by_status?${activeParams.toString()}`)
        .then(data => {
            if (data.success) {
                document.getElementById('active-count').textContent = data.data.total;
                console.log('[DEBUG] 更新有效方法计数为:', data.data.total);
            }
        })
        .catch(error => {
            console.error('获取有效方法计数失败:', error);
        });

    // 更新废止方法计数
    loadDisabledMethodsCount();
}



// 工具函数
function showLoading(containerId, message = '加载中...') {
    document.getElementById(containerId).innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">${message}</span>
            </div>
            <p class="mt-2 text-muted">${message}</p>
        </div>
    `;
}

function showError(containerId, message) {
    document.getElementById(containerId).innerHTML = `
        <div class="alert alert-danger m-3" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
        </div>
    `;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatMethodId(methodNo) {
    if (!methodNo) return '-';
    const str = methodNo.toString();
    return str.length >= 2 ? str.slice(-2) : str;
}

function formatCategory(category1, category2) {
    const cat1 = category1 || '';
    const cat2 = category2 || '';
    if (cat1 && cat2) {
        return `${cat1}-${cat2}`;
    } else if (cat1) {
        return cat1;
    } else if (cat2) {
        return cat2;
    }
    return '-';
}

function formatPrice(analysisPrice, lowestPrice) {
    let analysis = analysisPrice || 0;
    let lowest = lowestPrice || analysis;

    // 如果分析价格为"未设置"或空，设为0
    if (analysis === '未设置' || !analysis) analysis = 0;
    if (lowest === '未设置' || !lowest) lowest = analysis;

    return `${analysis}/${lowest}`;
}

// 新增：带价格变动箭头的价格格式化函数
function formatPriceWithChanges(method) {
    let analysis = method.analysis_price || 0;
    let lowest = method.lowest_price || analysis;

    // 如果分析价格为"未设置"或空，设为0
    if (analysis === '未设置' || !analysis) analysis = 0;
    if (lowest === '未设置' || !lowest) lowest = analysis;

    // 获取价格变动箭头
    let analysisArrow = '';
    let lowestArrow = '';

    if (method.analysis_price_change && method.analysis_price_change.change !== undefined) {
        analysisArrow = getPriceChangeArrow(method.analysis_price_change.change);
    }

    if (method.lowest_price_change && method.lowest_price_change.change !== undefined) {
        lowestArrow = getPriceChangeArrow(method.lowest_price_change.change);
    }

    return `${analysis}${analysisArrow}/${lowest}${lowestArrow}`;
}

// 新增：获取价格变动箭头指示器
function getPriceChangeArrow(change) {
    if (change > 0) {
        return '<span class="text-danger ms-1">↑</span>';
    } else if (change < 0) {
        return '<span class="text-success ms-1">↓</span>';
    }
    return '';
}

function formatProjectName(projectName, hasChildItem) {
    if (!projectName) return '-';

    if (hasChildItem && hasChildItem !== '无') {
        // 使用data属性存储子项目数据，避免在onclick中传递大量字符串
        return `<span class="text-primary fw-bold project-name-clickable" style="cursor: pointer;"
                      data-project-name="${escapeHtml(projectName)}"
                      data-child-items="${escapeHtml(hasChildItem)}">${escapeHtml(projectName)}</span>`;
    } else {
        return `<span class="fw-bold">${escapeHtml(projectName)}</span>`;
    }
}

function formatChildItemCount(hasChildItem) {
    if (!hasChildItem || hasChildItem === '无' || hasChildItem.trim() === '') {
        return '<span class="text-muted">1</span>';
    }

    // 使用中文顿号分割子项目，计算数量
    const childItems = hasChildItem.split('、').filter(item => item.trim() !== '');
    const count = childItems.length;

    return `<span style="color: black;">${count}</span>`;
}

function formatStatus(itemStatus, methodStatus) {
    const itemLocked = itemStatus == 1;
    const methodLocked = methodStatus == 1;

    let itemIcon = itemLocked ? '<i class="fas fa-lock text-danger" title="项目锁定"></i>' : '<i class="fas fa-unlock text-success" title="项目正常"></i>';
    let methodIcon = methodLocked ? '<i class="fas fa-lock text-danger" title="方法锁定"></i>' : '<i class="fas fa-unlock text-success" title="方法正常"></i>';

    return `<span style="font-size: 0.75rem;">项目${itemIcon} 方法${methodIcon}</span>`;
}

function formatAvailability(itemStatus, methodStatus) {
    const itemLocked = itemStatus == 1;
    const methodLocked = methodStatus == 1;
    const isAvailable = !itemLocked && !methodLocked;

    return isAvailable
        ? '<span class="badge bg-success-subtle text-success border border-success" style="font-size: 0.7rem;">可用</span>'
        : '<span class="badge bg-danger-subtle text-danger border border-danger" style="font-size: 0.7rem;">不可用</span>';
}

function formatCertification(cma, cnas, nhc) {
    const hasCMA = cma == 1;
    const hasCNAS = cnas == 1;
    const hasNHC = nhc == 1;

    const certifications = [];
    if (hasCMA) certifications.push('CMA');
    if (hasCNAS) certifications.push('CNAS');
    if (hasNHC) certifications.push('NHC');

    if (certifications.length === 0) {
        return '<span class="text-muted" style="font-size:0.7rem;">无</span>';
    } else if (certifications.length === 1) {
        const cert = certifications[0];
        const colorClass = cert === 'CMA' ? 'bg-success' :
                          cert === 'CNAS' ? 'bg-info' : 'bg-warning';
        return `<span class="badge ${colorClass}" style="font-size:0.7rem;">${cert}</span>`;
    } else {
        return `<span class="badge bg-primary" style="font-size:0.7rem;">${certifications.join('/')}</span>`;
    }
}

// API请求函数
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 搜索有效方法
function searchActiveMethods(page = 1) {
    activeCurrentPage = page;
    activeCurrentProjectNumber = document.getElementById('active-project-number').value.trim();
    activeCurrentProjectName = document.getElementById('active-project-name').value.trim();
    activeCurrentCategory = document.getElementById('active-category').value.trim();
    activeCurrentMethodName = document.getElementById('active-method-name').value.trim();
    const activeCurrentMethodName2 = document.getElementById('active-method-name2').value.trim();
    const activeCmaFilter = document.getElementById('active-cma-filter').checked;
    const activeCnasFilter = document.getElementById('active-cnas-filter').checked;
    const activeNhcFilter = document.getElementById('active-nhc-filter').checked;
    const activeAvailabilityFilter = document.getElementById('active-availability-filter').value;
    const activeDateFilter = document.getElementById('active-date-filter').value;


    showLoading('active-methods-results', '搜索中...');
    document.getElementById('active-pagination-container').style.display = 'none';

    const params = new URLSearchParams();

    // 如果有方法ID列表，优先使用方法ID筛选，忽略其他筛选条件
    if (window.activeMethodIds) {
        params.append('method_ids', window.activeMethodIds);
    } else {
        // 使用常规筛选条件
        params.append('status', '0'); // 有效方法
        if (activeCurrentProjectNumber) params.append('project_number', activeCurrentProjectNumber);
        if (activeCurrentProjectName) params.append('keyword', activeCurrentProjectName);
        if (activeCurrentCategory) params.append('category', activeCurrentCategory);
        if (activeCurrentMethodName) params.append('method_name', activeCurrentMethodName);
        if (activeCurrentMethodName2) params.append('method_name2', activeCurrentMethodName2);
        if (activeCmaFilter) params.append('cma', '1');
        if (activeCnasFilter) params.append('cnas', '1');
        if (activeNhcFilter) params.append('nhc', '1');
        if (activeAvailabilityFilter) params.append('availability', activeAvailabilityFilter);
        if (activeDateFilter) params.append('date_filter', activeDateFilter);

        // 添加变动类型筛选参数
        if (window.activeChangeFilter && window.activeChangeDate) {
            params.append('change_filter', window.activeChangeFilter);
            params.append('change_date', window.activeChangeDate);
        }
    }


    params.append('limit', activeCurrentLimit);
    params.append('offset', (activeCurrentPage - 1) * activeCurrentLimit);

    apiRequest(`/api/methods_by_status?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayActiveMethods(data.data.methods);

                // 更新标签页数量统计
                document.getElementById('active-count').textContent = data.data.total;

                if (data.data.total > activeCurrentLimit) {
                    activeTotalPages = Math.ceil(data.data.total / activeCurrentLimit);
                    updateActivePagination(data.data.total);
                }
            } else {
                showError('active-methods-results', '搜索失败: ' + data.message);
            }
        })
        .catch(error => {
            showError('active-methods-results', '请求出错: ' + error.message);
        });
}

// 搜索废止方法
function searchDisabledMethods(page = 1) {
    disabledCurrentPage = page;
    disabledCurrentProjectNumber = document.getElementById('disabled-project-number').value.trim();
    disabledCurrentProjectName = document.getElementById('disabled-project-name').value.trim();
    disabledCurrentCategory = document.getElementById('disabled-category').value.trim();
    disabledCurrentMethodName = document.getElementById('disabled-method-name').value.trim();
    const disabledCurrentMethodName2 = document.getElementById('disabled-method-name2').value.trim();
    const disabledCmaFilter = document.getElementById('disabled-cma-filter').checked;
    const disabledCnasFilter = document.getElementById('disabled-cnas-filter').checked;
    const disabledNhcFilter = document.getElementById('disabled-nhc-filter').checked;
    const disabledAvailabilityFilter = document.getElementById('disabled-availability-filter').value;


    showLoading('disabled-methods-results', '搜索中...');
    document.getElementById('disabled-pagination-container').style.display = 'none';

    const params = new URLSearchParams();
    params.append('status', '1'); // 废止方法
    if (disabledCurrentProjectNumber) params.append('project_number', disabledCurrentProjectNumber);
    if (disabledCurrentProjectName) params.append('keyword', disabledCurrentProjectName);
    if (disabledCurrentCategory) params.append('category', disabledCurrentCategory);
    if (disabledCurrentMethodName) params.append('method_name', disabledCurrentMethodName);
    if (disabledCurrentMethodName2) params.append('method_name2', disabledCurrentMethodName2);
    if (disabledCmaFilter) params.append('cma', '1');
    if (disabledCnasFilter) params.append('cnas', '1');
    if (disabledNhcFilter) params.append('nhc', '1');
    if (disabledAvailabilityFilter) params.append('availability', disabledAvailabilityFilter);

    params.append('limit', disabledCurrentLimit);
    params.append('offset', (disabledCurrentPage - 1) * disabledCurrentLimit);

    apiRequest(`/api/methods_by_status?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayDisabledMethods(data.data.methods);

                // 更新标签页数量统计
                document.getElementById('disabled-count').textContent = data.data.total;

                if (data.data.total > disabledCurrentLimit) {
                    disabledTotalPages = Math.ceil(data.data.total / disabledCurrentLimit);
                    updateDisabledPagination(data.data.total);
                }
            } else {
                showError('disabled-methods-results', '搜索失败: ' + data.message);
            }
        })
        .catch(error => {
            showError('disabled-methods-results', '请求出错: ' + error.message);
        });
}

// 清空有效方法搜索
function clearActiveSearch() {
    document.getElementById('active-project-number').value = '';
    document.getElementById('active-project-name').value = '';
    document.getElementById('active-category').value = '';
    document.getElementById('active-method-name').value = '';
    document.getElementById('active-method-name2').value = '';
    document.getElementById('active-cma-filter').checked = false;
    document.getElementById('active-cnas-filter').checked = false;
    document.getElementById('active-nhc-filter').checked = false;
    document.getElementById('active-availability-filter').value = '';


    activeCurrentPage = 1;
    activeCurrentProjectNumber = '';
    activeCurrentProjectName = '';
    activeCurrentCategory = '';
    activeCurrentMethodName = '';

    searchActiveMethods(1);
}

// 清空废止方法搜索
function clearDisabledSearch() {
    document.getElementById('disabled-project-number').value = '';
    document.getElementById('disabled-project-name').value = '';
    document.getElementById('disabled-category').value = '';
    document.getElementById('disabled-method-name').value = '';
    document.getElementById('disabled-method-name2').value = '';
    document.getElementById('disabled-cma-filter').checked = false;
    document.getElementById('disabled-cnas-filter').checked = false;
    document.getElementById('disabled-nhc-filter').checked = false;
    document.getElementById('disabled-availability-filter').value = '';


    disabledCurrentPage = 1;
    disabledCurrentProjectNumber = '';
    disabledCurrentProjectName = '';
    disabledCurrentCategory = '';
    disabledCurrentMethodName = '';

    searchDisabledMethods(1);
}

// 显示有效方法列表
function displayActiveMethods(methods) {
    const container = document.getElementById('active-methods-results');

    if (!methods || methods.length === 0) {
        container.innerHTML = `
            <div class="text-center p-4 text-muted">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>未找到符合条件的有效方法</p>
            </div>
        `;
        return;
    }

    renderActiveMethodsTable(methods);
}

// 渲染有效方法表格
function renderActiveMethodsTable(methods) {

    let html = `
        <div class="table-responsive">
            <table class="table table-sm table-hover mb-0 column-divider" style="font-size: 0.95rem;">
                <thead class="table-light">
                    <tr>
                        <th width="3%" class="text-center">
                            <input type="checkbox" class="form-check-input" id="active-select-all"
                                   onchange="toggleActiveSelectAll(this.checked)">
                        </th>
                        <th width="6%" class="text-center">分类</th>
                        <th width="6%" class="text-center">项目编号</th>
                        <th width="4%" class="text-center">方法ID</th>
                        <th width="6%" class="text-center">项目名称</th>
                        <th width="5%" class="text-center">子项目数</th>
                        <th width="28%" class="text-center">方法名称</th>
                        <th width="10%" class="text-center">价格(分析/最低)</th>
                        <th width="10%" class="text-center">采样价格</th>
                        <th width="8%" class="text-center">资质</th>
                        <th width="8%" class="text-center">状态</th>
                        <th width="4%" class="text-center">可用</th>
                    </tr>
                </thead>
                <tbody>
    `;

    methods.forEach(method => {
        // 处理采样价格（包括变动状态）
        let samplePricesHtml = '';
        const currentSamples = method.sample_types || [];
        const deletedSamples = method.deleted_sample_types || [];
        const sampleChanges = method.sample_price_changes || [];

        if (currentSamples.length > 0 || deletedSamples.length > 0 || sampleChanges.length > 0) {
            const samplePrices = [];

            // 创建采样类型变动状态映射
            const sampleChangeMap = {};
            sampleChanges.forEach(change => {
                sampleChangeMap[change.type] = change;
            });

            // 当前有效的采样价格
            currentSamples.forEach(sample => {
                const change = sampleChangeMap[sample.type];
                let priceText = `${sample.type}: ${sample.price || '未设置'}`;

                if (change && change.price_change) {
                    const arrow = getPriceChangeArrow(change.price_change.change);
                    priceText += arrow;
                }

                samplePrices.push(priceText);
            });

            // 已删除的采样价格
            deletedSamples.forEach(sample => {
                samplePrices.push(`<span class="text-danger"><del>${sample.type}: ${sample.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
            });

            // 处理变动中标记为删除的采样类型
            sampleChanges.forEach(change => {
                if (change.status === 'deleted' && !deletedSamples.find(s => s.type === change.type)) {
                    samplePrices.push(`<span class="text-danger"><del>${change.type}: ${change.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
                } else if (change.status === 'new') {
                    // 新增的采样类型（如果不在当前列表中）
                    if (!currentSamples.find(s => s.type === change.type)) {
                        samplePrices.push(`<span class="text-success">${change.type}: ${change.current_price || 0} <span class="text-muted small">新增</span></span>`);
                    }
                }
            });

            samplePricesHtml = samplePrices.join('<br>');
        } else {
            samplePricesHtml = '<span class="text-muted">无采样价格</span>';
        }

        const isSelected = activeSelectedMethods.has(method.method_id);

        html += `
            <tr>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input active-method-checkbox"
                           value="${method.method_id}" ${isSelected ? 'checked' : ''}
                           onchange="toggleActiveMethodSelection(${method.method_id}, this.checked)">
                </td>
                <td class="text-center">${formatCategory(method.service_category_name, method.second_category_name)}</td>
                <td class="text-center"><code class="small">${method.service_item_number || '-'}</code></td>
                <td class="text-center"><code class="small">${formatMethodId(method.method_no)}</code></td>
                <td class="text-center">${formatProjectName(method.service_item_name, method.child_item)}</td>
                <td class="text-center">${formatChildItemCount(method.child_item)}</td>
                <td>
                    <span class="method-name-clickable text-primary"
                          style="cursor: pointer; text-decoration: underline;"
                          data-method-id="${method.method_id}"
                          data-method-name="${escapeHtml(method.method_name)}"
                          data-project-name="${escapeHtml(method.service_item_name)}"
                          title="点击查看价格趋势图">
                        ${escapeHtml(method.method_name)}
                    </span>
                </td>
                <td class="text-center">${formatPriceWithChanges(method)}</td>
                <td class="text-center">${samplePricesHtml}</td>
                <td class="text-center">${formatCertification(method.cma, method.cnas, method.gov_agree)}</td>
                <td class="text-center">${formatStatus(method.item_status, method.method_status)}</td>
                <td class="text-center">${formatAvailability(method.item_status, method.method_status)}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    const container = document.getElementById('active-methods-results');
    container.innerHTML = html;
}

// 显示废止方法列表
function displayDisabledMethods(methods) {
    const container = document.getElementById('disabled-methods-results');

    if (!methods || methods.length === 0) {
        container.innerHTML = `
            <div class="text-center p-4 text-muted">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>未找到符合条件的废止方法</p>
            </div>
        `;
        return;
    }

    renderDisabledMethodsTable(methods);
}

// 渲染废止方法表格
function renderDisabledMethodsTable(methods) {

    let html = `
        <div class="table-responsive">
            <table class="table table-sm table-hover mb-0 column-divider" style="font-size: 0.95rem;">
                <thead class="table-light">
                    <tr>
                        <th width="3%" class="text-center">
                            <input type="checkbox" class="form-check-input" id="disabled-select-all"
                                   onchange="toggleDisabledSelectAll(this.checked)">
                        </th>
                        <th width="6%" class="text-center">分类</th>
                        <th width="6%" class="text-center">项目编号</th>
                        <th width="4%" class="text-center">方法ID</th>
                        <th width="6%" class="text-center">项目名称</th>
                        <th width="5%" class="text-center">子项目数</th>
                        <th width="28%" class="text-center">方法名称</th>
                        <th width="10%" class="text-center">价格(分析/最低)</th>
                        <th width="10%" class="text-center">采样价格</th>
                        <th width="8%" class="text-center">资质</th>
                        <th width="8%" class="text-center">状态</th>
                        <th width="4%" class="text-center">可用</th>
                    </tr>
                </thead>
                <tbody>
    `;

    methods.forEach(method => {
        // 处理采样价格（包括变动状态）
        let samplePricesHtml = '';
        const currentSamples = method.sample_types || [];
        const deletedSamples = method.deleted_sample_types || [];
        const sampleChanges = method.sample_price_changes || [];

        if (currentSamples.length > 0 || deletedSamples.length > 0 || sampleChanges.length > 0) {
            const samplePrices = [];

            // 创建采样类型变动状态映射
            const sampleChangeMap = {};
            sampleChanges.forEach(change => {
                sampleChangeMap[change.type] = change;
            });

            // 当前有效的采样价格
            currentSamples.forEach(sample => {
                const change = sampleChangeMap[sample.type];
                let priceText = `${sample.type}: ${sample.price || '未设置'}`;

                if (change && change.price_change) {
                    const arrow = getPriceChangeArrow(change.price_change.change);
                    priceText += arrow;
                }

                samplePrices.push(priceText);
            });

            // 已删除的采样价格
            deletedSamples.forEach(sample => {
                samplePrices.push(`<span class="text-danger"><del>${sample.type}: ${sample.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
            });

            // 处理变动中标记为删除的采样类型
            sampleChanges.forEach(change => {
                if (change.status === 'deleted' && !deletedSamples.find(s => s.type === change.type)) {
                    samplePrices.push(`<span class="text-danger"><del>${change.type}: ${change.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
                } else if (change.status === 'new') {
                    // 新增的采样类型（如果不在当前列表中）
                    if (!currentSamples.find(s => s.type === change.type)) {
                        samplePrices.push(`<span class="text-success">${change.type}: ${change.current_price || 0} <span class="text-muted small">新增</span></span>`);
                    }
                }
            });

            samplePricesHtml = samplePrices.join('<br>');
        } else {
            samplePricesHtml = '<span class="text-muted">无采样价格</span>';
        }

        const isSelected = disabledSelectedMethods.has(method.method_id);

        html += `
            <tr class="table-warning">
                <td class="text-center">
                    <input type="checkbox" class="form-check-input disabled-method-checkbox"
                           value="${method.method_id}" ${isSelected ? 'checked' : ''}
                           onchange="toggleDisabledMethodSelection(${method.method_id}, this.checked)">
                </td>
                <td class="text-center">${formatCategory(method.service_category_name, method.second_category_name)}</td>
                <td class="text-center"><code class="small">${method.service_item_number || '-'}</code></td>
                <td class="text-center"><code class="small">${formatMethodId(method.method_no)}</code></td>
                <td class="text-center">${formatProjectName(method.service_item_name, method.child_item)}</td>
                <td class="text-center">${formatChildItemCount(method.child_item)}</td>
                <td>
                    <span class="method-name-clickable text-primary"
                          style="cursor: pointer; text-decoration: underline;"
                          data-method-id="${method.method_id}"
                          data-method-name="${escapeHtml(method.method_name)}"
                          data-project-name="${escapeHtml(method.service_item_name)}"
                          title="点击查看价格趋势图">
                        ${escapeHtml(method.method_name)}
                    </span>
                </td>
                <td class="text-center">${formatPriceWithChanges(method)}</td>
                <td class="text-center">${samplePricesHtml}</td>
                <td class="text-center">${formatCertification(method.cma, method.cnas, method.gov_agree)}</td>
                <td class="text-center">${formatStatus(method.item_status, method.method_status)}</td>
                <td class="text-center">${formatAvailability(method.item_status, method.method_status)}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    const container = document.getElementById('disabled-methods-results');
    container.innerHTML = html;
}

// 废止方法
function disableMethod(methodId, methodName) {
    document.getElementById('confirmModalTitle').innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>确认废止方法';
    document.getElementById('confirmModalMessage').textContent = `确定要废止方法 "${methodName}" 吗？废止后该方法将移动到废止方法列表中。`;

    document.getElementById('confirmModalConfirm').onclick = function() {
        updateMethodStatus(methodId, 1, function() {
            // 刷新当前页面
            searchActiveMethods(activeCurrentPage);
            // 更新计数
            updateAllMethodCounts();
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        });
    };

    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

// 恢复方法
function enableMethod(methodId, methodName) {
    document.getElementById('confirmModalTitle').innerHTML = '<i class="fas fa-check-circle me-2"></i>确认恢复方法';
    document.getElementById('confirmModalMessage').textContent = `确定要恢复方法 "${methodName}" 吗？恢复后该方法将移动到有效方法列表中。`;

    document.getElementById('confirmModalConfirm').onclick = function() {
        updateMethodStatus(methodId, 0, function() {
            // 刷新当前页面
            searchDisabledMethods(disabledCurrentPage);
            // 更新计数
            updateAllMethodCounts();
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        });
    };

    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

// 更新方法状态
function updateMethodStatus(methodId, status, callback) {
    apiRequest('/api/update_method_status', {
        method: 'POST',
        body: JSON.stringify({
            method_id: methodId,
            status: status
        })
    })
    .then(data => {
        if (data.success) {
            // 显示成功消息
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check me-2"></i>${data.message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);

            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // 3秒后移除toast
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);

            if (callback) callback();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('操作失败: ' + error.message);
    });
}

// 更新有效方法分页
function updateActivePagination(total) {
    const container = document.getElementById('active-pagination-container');
    const pagination = document.getElementById('active-pagination');

    if (total <= activeCurrentLimit) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'flex';

    let html = '';

    // 上一页
    if (activeCurrentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="searchActiveMethods(${activeCurrentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, activeCurrentPage - 2);
    const endPage = Math.min(activeTotalPages, activeCurrentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === activeCurrentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="javascript:void(0)" onclick="searchActiveMethods(${i})">${i}</a></li>`;
    }

    // 下一页
    if (activeCurrentPage < activeTotalPages) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="searchActiveMethods(${activeCurrentPage + 1})">下一页</a></li>`;
    }

    pagination.innerHTML = html;
}

// 更新废止方法分页
function updateDisabledPagination(total) {
    const container = document.getElementById('disabled-pagination-container');
    const pagination = document.getElementById('disabled-pagination');

    if (total <= disabledCurrentLimit) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'flex';

    let html = '';

    // 上一页
    if (disabledCurrentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="searchDisabledMethods(${disabledCurrentPage - 1})">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, disabledCurrentPage - 2);
    const endPage = Math.min(disabledTotalPages, disabledCurrentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === disabledCurrentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="javascript:void(0)" onclick="searchDisabledMethods(${i})">${i}</a></li>`;
    }

    // 下一页
    if (disabledCurrentPage < disabledTotalPages) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="searchDisabledMethods(${disabledCurrentPage + 1})">下一页</a></li>`;
    }

    pagination.innerHTML = html;
}


// 批量选择相关函数
function toggleActiveMethodSelection(methodId, isSelected) {
    if (isSelected) {
        activeSelectedMethods.add(methodId);
    } else {
        activeSelectedMethods.delete(methodId);
    }
    updateActiveSelectionUI();
}

function toggleDisabledMethodSelection(methodId, isSelected) {
    if (isSelected) {
        disabledSelectedMethods.add(methodId);
    } else {
        disabledSelectedMethods.delete(methodId);
    }
    updateDisabledSelectionUI();
}

function toggleActiveSelectAll(isSelected) {
    const checkboxes = document.querySelectorAll('.active-method-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = isSelected;
        const methodId = parseInt(checkbox.value);
        if (isSelected) {
            activeSelectedMethods.add(methodId);
        } else {
            activeSelectedMethods.delete(methodId);
        }
    });
    updateActiveSelectionUI();
}

function toggleDisabledSelectAll(isSelected) {
    const checkboxes = document.querySelectorAll('.disabled-method-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = isSelected;
        const methodId = parseInt(checkbox.value);
        if (isSelected) {
            disabledSelectedMethods.add(methodId);
        } else {
            disabledSelectedMethods.delete(methodId);
        }
    });
    updateDisabledSelectionUI();
}

function updateActiveSelectionUI() {
    const count = activeSelectedMethods.size;
    document.getElementById('active-selected-count').textContent = count;

    const toolbar = document.getElementById('active-batch-toolbar');
    const batchBtn = document.getElementById('active-batch-disable-btn');

    if (count > 0) {
        toolbar.style.display = 'block';
        batchBtn.disabled = false;
    } else {
        toolbar.style.display = 'none';
        batchBtn.disabled = true;
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('active-select-all');
    const allCheckboxes = document.querySelectorAll('.active-method-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('.active-method-checkbox:checked').length;
        selectAllCheckbox.checked = checkedCount === allCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;
    }
}

function updateDisabledSelectionUI() {
    const count = disabledSelectedMethods.size;
    document.getElementById('disabled-selected-count').textContent = count;

    const toolbar = document.getElementById('disabled-batch-toolbar');
    const batchBtn = document.getElementById('disabled-batch-enable-btn');

    if (count > 0) {
        toolbar.style.display = 'block';
        batchBtn.disabled = false;
    } else {
        toolbar.style.display = 'none';
        batchBtn.disabled = true;
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('disabled-select-all');
    const allCheckboxes = document.querySelectorAll('.disabled-method-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('.disabled-method-checkbox:checked').length;
        selectAllCheckbox.checked = checkedCount === allCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;
    }
}

function clearActiveSelection() {
    activeSelectedMethods.clear();
    document.querySelectorAll('.active-method-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('active-select-all').checked = false;
    updateActiveSelectionUI();
}

function clearDisabledSelection() {
    disabledSelectedMethods.clear();
    document.querySelectorAll('.disabled-method-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('disabled-select-all').checked = false;
    updateDisabledSelectionUI();
}

// 批量操作函数
function batchDisableMethods() {
    const selectedIds = Array.from(activeSelectedMethods);
    if (selectedIds.length === 0) {
        alert('请先选择要废止的方法');
        return;
    }

    document.getElementById('confirmModalTitle').innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>确认批量废止方法';
    document.getElementById('confirmModalMessage').textContent = `确定要废止选中的 ${selectedIds.length} 个方法吗？废止后这些方法将移动到废止方法列表中。`;

    document.getElementById('confirmModalConfirm').onclick = function() {
        batchUpdateMethodStatus(selectedIds, 1, function() {
            // 清空选择
            clearActiveSelection();
            // 刷新当前页面
            searchActiveMethods(activeCurrentPage);
            // 更新计数
            updateAllMethodCounts();
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        });
    };

    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

function batchEnableMethods() {
    const selectedIds = Array.from(disabledSelectedMethods);
    if (selectedIds.length === 0) {
        alert('请先选择要恢复的方法');
        return;
    }

    document.getElementById('confirmModalTitle').innerHTML = '<i class="fas fa-check-circle me-2"></i>确认批量恢复方法';
    document.getElementById('confirmModalMessage').textContent = `确定要恢复选中的 ${selectedIds.length} 个方法吗？恢复后这些方法将移动到有效方法列表中。`;

    document.getElementById('confirmModalConfirm').onclick = function() {
        batchUpdateMethodStatus(selectedIds, 0, function() {
            // 清空选择
            clearDisabledSelection();
            // 刷新当前页面
            searchDisabledMethods(disabledCurrentPage);
            // 更新计数
            updateAllMethodCounts();
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        });
    };

    new bootstrap.Modal(document.getElementById('confirmModal')).show();
}

function batchUpdateMethodStatus(methodIds, status, callback) {
    // 显示进度提示
    const progressToast = document.createElement('div');
    progressToast.className = 'toast align-items-center text-white bg-info border-0 position-fixed top-0 end-0 m-3';
    progressToast.style.zIndex = '9999';
    progressToast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-spinner fa-spin me-2"></i>正在处理 ${methodIds.length} 个方法...
            </div>
        </div>
    `;
    document.body.appendChild(progressToast);

    const bsProgressToast = new bootstrap.Toast(progressToast, { autohide: false });
    bsProgressToast.show();

    apiRequest('/api/batch_update_method_status', {
        method: 'POST',
        body: JSON.stringify({
            method_ids: methodIds,
            status: status
        })
    })
    .then(data => {
        // 隐藏进度提示
        bsProgressToast.hide();
        setTimeout(() => {
            if (progressToast.parentNode) {
                progressToast.parentNode.removeChild(progressToast);
            }
        }, 500);

        if (data.success) {
            // 显示成功消息
            const successToast = document.createElement('div');
            successToast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            successToast.style.zIndex = '9999';
            successToast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check me-2"></i>${data.message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(successToast);

            const bsSuccessToast = new bootstrap.Toast(successToast);
            bsSuccessToast.show();

            // 5秒后移除toast
            setTimeout(() => {
                if (successToast.parentNode) {
                    successToast.parentNode.removeChild(successToast);
                }
            }, 5000);

            if (callback) callback();
        } else {
            alert('批量操作失败: ' + data.message);
        }
    })
    .catch(error => {
        // 隐藏进度提示
        bsProgressToast.hide();
        setTimeout(() => {
            if (progressToast.parentNode) {
                progressToast.parentNode.removeChild(progressToast);
            }
        }, 500);

        alert('批量操作失败: ' + error.message);
    });
}



// 子项目功能
function showChildItems(projectName, childItemData) {
    document.getElementById('childItemsModalTitle').innerHTML = `<i class="fas fa-list me-2"></i>${projectName} - 子项目列表`;

    // 显示加载状态
    document.getElementById('childItemsContent').innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载子项目数据中...</p>
        </div>
    `;

    // 显示弹窗
    new bootstrap.Modal(document.getElementById('childItemsModal')).show();

    // 直接显示原始子项目数据，不进行分割处理
    try {
        if (!childItemData || childItemData === '无' || childItemData === '') {
            document.getElementById('childItemsContent').innerHTML = `
                <div class="text-center p-4 text-muted">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>该项目暂无子项目信息</p>
                </div>
            `;
            return;
        }

        // 计算子项目数量（只按顿号分割）
        const childItems = childItemData.split('、').filter(item => item.trim());
        const itemCount = childItems.length;

        let html = `
            <div class="alert alert-info mb-3">
                <div class="row">
                    <div class="col-md-8">
                        <strong><i class="fas fa-list me-2"></i>子项目列表</strong>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge bg-primary">共 ${itemCount} 个子项目</span>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <div class="input-group input-group-sm">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="childItemSearch" placeholder="搜索子项目...">
                </div>
            </div>
            <div class="card">
                <div class="card-body p-2" style="max-height: 400px; overflow-y: auto;">
                    <div class="row" id="childItemsList">
        `;

        childItems.forEach((item, index) => {
            const trimmedItem = item.trim();
            if (trimmedItem) {
                html += `
                    <div class="col-md-6 mb-1 child-item-entry">
                        <small class="text-muted">${index + 1}.</small>
                        <span class="child-item-text">${escapeHtml(trimmedItem)}</span>
                    </div>
                `;
            }
        });

        html += `
                    </div>
                </div>
            </div>
        `;

        document.getElementById('childItemsContent').innerHTML = html;

        // 添加搜索功能
        const searchInput = document.getElementById('childItemSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                const childItemEntries = document.querySelectorAll('.child-item-entry');

                childItemEntries.forEach(entry => {
                    const text = entry.querySelector('.child-item-text').textContent.toLowerCase();
                    if (searchTerm === '' || text.includes(searchTerm)) {
                        entry.style.display = '';
                    } else {
                        entry.style.display = 'none';
                    }
                });
            });
        }

    } catch (error) {
        console.error('解析子项目数据失败:', error);
        document.getElementById('childItemsContent').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                解析子项目数据失败
                <hr>
                <small><strong>原始数据:</strong> ${escapeHtml(String(childItemData || ''))}</small>
                <br>
                <small><strong>错误信息:</strong> ${error.message}</small>
            </div>
        `;
    }
}

// 每页数量变更功能
function changeActivePageSize() {
    const newSize = parseInt(document.getElementById('active-page-size').value);
    activeCurrentLimit = newSize;
    activeCurrentPage = 1; // 重置到第一页
    searchActiveMethods(1);
}

function changeDisabledPageSize() {
    const newSize = parseInt(document.getElementById('disabled-page-size').value);
    disabledCurrentLimit = newSize;
    disabledCurrentPage = 1; // 重置到第一页
    searchDisabledMethods(1);
}



// 显示价格趋势图
function showPriceTrend(methodId, methodName, projectName) {
    // 设置弹窗标题
    document.getElementById('priceTrendModalTitle').innerHTML =
        `<i class="fas fa-chart-line me-2"></i>${escapeHtml(methodName)} - 价格趋势图`;

    // 显示加载状态
    document.getElementById('priceTrendContent').innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载价格趋势数据中...</p>
        </div>
    `;

    // 显示弹窗
    new bootstrap.Modal(document.getElementById('priceTrendModal')).show();

    // 获取价格趋势数据
    apiRequest(`/api/method_price_trend?method_id=${methodId}`)
        .then(data => {
            if (data.success) {
                displayPriceTrend(data.data, methodName, projectName);
            } else {
                document.getElementById('priceTrendContent').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || '获取价格趋势数据失败'}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('priceTrendContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    获取价格趋势数据时出错: ${error.message}
                </div>
            `;
        });
}

// 显示价格趋势图表
function displayPriceTrend(trendData, methodName, projectName) {
    const container = document.getElementById('priceTrendContent');

    // 保存当前趋势数据到全局变量，供表格函数使用
    window.currentTrendData = trendData;

    if (!trendData.price_history || trendData.price_history.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                该方法暂无价格历史记录
            </div>
        `;
        return;
    }

    // 获取所有采样类型
    let allSampleTypes = [];
    if (trendData.all_sample_types) {
        allSampleTypes = trendData.all_sample_types;
    } else {
        const sampleTypesSet = new Set();
        trendData.price_history.forEach(record => {
            if (record.sample_prices) {
                Object.keys(record.sample_prices).forEach(type => sampleTypesSet.add(type));
            }
        });
        allSampleTypes = Array.from(sampleTypesSet);
    }

    // 创建图表容器
    container.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>价格趋势图
                            <small class="text-muted ms-2">${escapeHtml(projectName)} - ${escapeHtml(methodName)}</small>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div style="height: 400px;">
                            <canvas id="priceTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-table me-2"></i>价格历史记录
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped column-divider" id="priceHistoryTable" style="font-size: 0.95rem;">
                                <thead class="table-primary">
                                    <tr>
                                        <th class="text-center fw-bold">抓取时间</th>
                                        <th class="text-center fw-bold">分析价格</th>
                                        <th class="text-center fw-bold">最低价格</th>
                                        ${allSampleTypes.map(type => `<th class="text-center fw-bold">采样价格<br><small>(${escapeHtml(type)})</small></th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody id="priceHistoryTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 准备图表数据
    const labels = trendData.price_history.map(record => new Date(record.fetch_time).toLocaleString('zh-CN'));
    const datasets = [];
    const colors = ['#007bff', '#dc3545', '#28a745', '#ffc107', '#17a2b8', '#6f42c1'];

    // 1. 分析价格
    datasets.push({
        label: '分析价格',
        data: trendData.price_history.map(record => record.check_method_price),
        borderColor: colors[0],
        backgroundColor: colors[0] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true // 连接空值点
    });

    // 2. 最低价格（基于当前方法状态决定逻辑）
    // 检查当前方法的最低价格状态
    const currentMethodInfo = trendData.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    datasets.push({
        label: '最低价格',
        data: trendData.price_history.map(record => {
            if (shouldUseAnalysisPrice) {
                // 当前方法的最低价格已被删除，整个历史都使用分析价格
                if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                    return record.check_method_price;
                } else {
                    return null;
                }
            } else {
                // 当前方法有最低价格，使用历史记录中的实际最低价格，null时回退到分析价格
                if (record.lowest_bid === null || record.lowest_bid === undefined || record.lowest_bid === 0) {
                    if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                        return record.check_method_price;
                    } else {
                        return null;
                    }
                }
                return record.lowest_bid;
            }
        }),
        borderColor: colors[1],
        backgroundColor: colors[1] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true // 连接空值点
    });

    // 3. 采样价格
    let colorIndex = 2;
    allSampleTypes.forEach(type => {
        datasets.push({
            label: `采样价格 - ${type}`,
            data: trendData.price_history.map(record => (record.sample_prices ? record.sample_prices[type] : null)),
            borderColor: colors[colorIndex % colors.length],
            backgroundColor: colors[colorIndex % colors.length] + '33',
            fill: false,
            tension: 0.1,
            spanGaps: true
        });
        colorIndex++;
    });

    // 绘制图表
    const ctx = document.getElementById('priceTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '抓取时间'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '价格 (元)'
                    },
                    beginAtZero: false
                }
            },
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // 填充价格历史表格
    fillPriceHistoryTable(trendData.price_history, allSampleTypes);
}

// 填充价格历史表格
function fillPriceHistoryTable(priceHistory, sampleTypes) {
    const tbody = document.getElementById('priceHistoryTableBody');

    // 获取当前方法的最低价格状态（从全局变量或数据中获取）
    const currentMethodInfo = window.currentTrendData?.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    let bodyHtml = '';
    [...priceHistory].reverse().forEach(record => {
        let rowHtml = `<tr><td class="text-center">${new Date(record.fetch_time).toLocaleString('zh-CN')}</td>`;

        const analysisPrice = record.check_method_price !== null && record.check_method_price !== undefined ? parseFloat(record.check_method_price).toFixed(2) + '元' : '-';
        rowHtml += `<td class="text-center fw-bold text-primary">${analysisPrice}</td>`;

        // 基于当前方法状态决定最低价格显示逻辑
        let lowestPriceDisplay;
        if (shouldUseAnalysisPrice) {
            // 当前方法的最低价格已被删除，整个历史都使用分析价格
            if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                lowestPriceDisplay = parseFloat(record.check_method_price).toFixed(2) + '元';
            } else {
                lowestPriceDisplay = '-';
            }
        } else {
            // 使用实际最低价格
            if (record.lowest_bid_price !== null && record.lowest_bid_price !== undefined && record.lowest_bid_price !== 0) {
                lowestPriceDisplay = parseFloat(record.lowest_bid_price).toFixed(2) + '元';
            } else {
                lowestPriceDisplay = '-';
            }
        }

        rowHtml += `<td class="text-center text-danger">${lowestPriceDisplay}</td>`;

        sampleTypes.forEach(type => {
            const samplePrice = (record.sample_prices && record.sample_prices[type] !== null && record.sample_prices[type] !== undefined) ? parseFloat(record.sample_prices[type]).toFixed(2) + '元' : '-';
            if (record.sample_prices && record.sample_prices[type] === 0) {
                rowHtml += `<td class="text-center text-danger"><del>0元</del></td>`;
            } else {
                rowHtml += `<td class="text-center">${samplePrice}</td>`;
            }
        });

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    tbody.innerHTML = bodyHtml;
}
</script>
{% endblock %}
