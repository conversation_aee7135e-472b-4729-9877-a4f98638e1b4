<!DOCTYPE html>
<html>
  <head>
    <title>Sign in ・ Cloudflare Access</title>
    <meta charset="utf-8" />
    <meta name="robots" content="noindex" />
    <meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width" />
    <article id="data"
      data-auto-redirect-to-identity="false"
      data-auto-redirect-url=""
      data-message="">
    </article>
    <style>*{-webkit-box-sizing:inherit;box-sizing:inherit}body,html{min-height:100vh}html{background:#f7f7f8;text-align:center;text-rendering:optimizeLegibility;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";line-height:1.5;word-wrap:break-word;-webkit-box-sizing:border-box;box-sizing:border-box;background:#eeeeee;color:#333333}.Content,body{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}body{padding:32px 8px;margin:0}.Content{-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin:32px;-webkit-box-flex:1;-ms-flex-positive:1;flex-grow:1}@media screen and (max-width:768px){.Content{padding:0;margin:32px 0}}.AuthBox{max-width:100%;background:#fff;border:1px solid #eaebeb;-webkit-box-shadow:0 4px 8px rgba(146,151,155,.15);box-shadow:0 4px 8px rgba(146,151,155,.15);padding-top:2em;width:36em}.base_AccessLogo{margin-bottom:1em}.base_TeamsLogo{padding-top:1em;padding-bottom:2em}@media only screen and (max-width:420px) and (pointer:coarse){.base_AccessLogo{margin-top:1em}.base_TeamsLogo{padding:1em 0}}.Content{margin-top:8px}.AuthBox{border-radius:5px;margin-top:0;margin-bottom:0}.AuthBox .AuthBox-error-box-footer{border-radius:5px 0 .5em .5em;background:rgba(0,0,0,.03);border-top:1px solid rgba(0,0,0,.15)}.AuthBox .AuthBoxRow--name{font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block}.AuthBox-RequestCode{border-bottom-left-radius:5px;border-bottom-right-radius:5px;padding-bottom:2.5em}.AuthBox-RequestCode__idpsExist{background:#f7f7f8}.AuthBox-error-box-footer{border:0;border-radius:5px;margin-bottom:2rem}.ErrorBlock,.ErrorBlock-icon{display:-webkit-box;display:-ms-flexbox;display:flex}.ErrorBlock-icon{width:2.25em;height:2.25em;margin-right:1.25em}.ErrorBlock-icon>svg{display:block;height:100%;width:100%}.ErrorBlock-body{-webkit-box-flex:1;-ms-flex:1;flex:1}.ErrorBlock-header-text{font-size:1.4em;margin-bottom:.5em}.JSONBlock{display:block;font-size:.9em;margin:0;padding:1.5em;font-family:Monaco,"Bitstream Vera Sans Mono","Lucida Console",Terminal,monospace;overflow-x:scroll;-webkit-overflow-scrolling:touch;color:rgba(0,0,0,.6)}.AuthBox-text{color:#4a4a4a;font-size:1.25em;font-weight:600;padding-top:1em;margin-bottom:24px}.AuthServiceLogin .AuthServiceLogin-row{padding-bottom:1em}.AuthFormLogin-row{text-align:left;margin:auto;max-width:22em;padding:1em 8px 0}.Footer-text,.Header-text{padding:3em 1em 1em;font-size:12px;line-height:18px}.Footer-text{padding:1em}.Footer-text span{white-space:pre-line}label{display:block;font-weight:600;margin-bottom:.5em}.Button{position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;display:inline-block;cursor:pointer;text-decoration:none;font:inherit;color:#fff;background:#333;padding:.5em 0;border:0;margin:0;border-radius:5px}.Button.Button-uses-org-theme-accent-color{background-color:#2c7cb0;padding:0 8px;width:100%}.Button:hover{-webkit-box-shadow:inset 0 0 0 999em rgba(255,255,255,.2);box-shadow:inset 0 0 0 999em rgba(255,255,255,.2)}.Button.Button-is-juicy{border-radius:5px;padding-top:.75em;padding-bottom:.75em;max-width:14em;font-weight:300}.Button.Button-is-block{display:block;margin:auto}.Button.Button-is-auth{background-color:#f7f7f8;border:1px solid #d5d7d8;color:inherit;text-align:left;min-height:55px;margin:0 3.5em;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-ms-flex-wrap:inherit;flex-wrap:inherit;padding-left:.5em}.Button.Button-is-auth .Button-auth-service-icon{width:2.8em;height:auto;padding:.5em;color:#1d1f20;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.Button.Button-is-auth .Button-auth-service-icon>svg{fill:currentColor;display:block;min-width:25px;width:25px;height:25px}.Button.Button-uses-org-theme-accent-color{margin:0;max-width:unset}.AuthBox-messages{max-width:36em}.Button.Button-is-auth .Button-auth-service-icon,.Message,.RayID{display:-webkit-box;display:-ms-flexbox;display:flex}.Message{-webkit-box-align:center;-ms-flex-align:center;align-items:center;font-size:.9em;overflow:hidden;margin-bottom:1em;border-radius:5px;padding:.5em 1em;text-align:left}.Message svg{height:1em;width:1em;margin-right:.5em}.Message.Message-is-warning{background:#fcf0f2;color:#711423;border:1px solid #f3bac3}.Message.Message-is-success{background:#a6eac9;color:#1c422b;border:1px solid #b0ddc2}.RayID{padding:0;text-align:center;background:#dfbd9f}.RayID.RayID-Title,.RayID.RayID-Value{-webkit-box-flex:0;-ms-flex:0 0 auto;flex:0 0 auto;padding:1em;display:inline-block}.RayID.RayID-Value{background:#e7c9af;-webkit-box-flex:1;-ms-flex:1 1 auto;flex:1 1 auto;font-family:Monaco,"Bitstream Vera Sans Mono","Lucida Console",Terminal,monospace}.StandardInput{position:relative;-webkit-user-select:auto;-moz-user-select:auto;-ms-user-select:auto;user-select:auto;-webkit-appearance:none;-moz-appearance:none;appearance:none;display:inline-block;text-align:left;font:inherit;color:"#333333";padding:.5em 1em;border:1px solid #cacaca;margin:0;border-radius:5px}.StandardInput:focus{outline:0;border-color:#777}.StandardInput.StandardInput-is-block{display:block;font-size:1em;width:100%}.StandardInput.StandardInput-is-code{font-family:Monaco,"Bitstream Vera Sans Mono","Lucida Console",Terminal,monospace}.StandardInput.StandardInput-is-entry-code{font-size:20px}.StandardInput:-moz-placeholder,.StandardInput::-webkit-input-placeholder{font-family:inherit}.OrgAvatarLink{display:block;text-decoration:none;color:inherit}.OrgAvatarLink-logo img{margin-bottom:.5em}.App-name,.OrgAvatarLink-title{text-align:center;font-weight:200}.OrgAvatarLink-title{color:#4e5255;font-size:.8em}.App-name{font-size:1.5em}@media screen and (max-width:650px){.Button.Button-is-auth{margin:0 8px}.AuthFormLogin .AuthFormLogin-row{width:auto}.Content{margin:8px 0}}</style>

    <!-- URL polyfill for IE11 -->
    <script>!function(e){"use strict";function t(t){return!!t&&("Symbol"in e&&"iterator"in e.Symbol&&"function"==typeof t[Symbol.iterator]||!!Array.isArray(t))}function n(e){return"from"in Array?Array.from(e):Array.prototype.slice.call(e)}!function(){var r,a=e.URL;try{if(a){if("searchParams"in(r=new e.URL("http://example.com")))return;"href"in r||(r=void 0)}}catch(e){}function i(e){var t="",n=!0;return e.forEach(function(e){var r=encodeURIComponent(e.name),a=encodeURIComponent(e.value);n||(t+="&"),t+=r+"="+a,n=!1}),t.replace(/%20/g,"+")}function o(e,t){var n=e.split("&");t&&-1===n[0].indexOf("=")&&(n[0]="="+n[0]);var r=[];n.forEach(function(e){if(0!==e.length){var t=e.indexOf("=");if(-1!==t)var n=e.substring(0,t),a=e.substring(t+1);else n=e,a="";n=n.replace(/\+/g," "),a=a.replace(/\+/g," "),r.push({name:n,value:a})}});var a=[];return r.forEach(function(e){a.push({name:decodeURIComponent(e.name),value:decodeURIComponent(e.value)})}),a}function u(e){var r=this;this._list=[],null==e||(e instanceof u?this._list=o(String(e)):"object"==typeof e&&t(e)?n(e).forEach(function(e){if(!t(e))throw TypeError();var a=n(e);if(2!==a.length)throw TypeError();r._list.push({name:String(a[0]),value:String(a[1])})}):"object"==typeof e&&e?Object.keys(e).forEach(function(t){r._list.push({name:String(t),value:String(e[t])})}):("?"===(e=String(e)).substring(0,1)&&(e=e.substring(1)),this._list=o(e))),this._url_object=null,this._setList=function(e){a||(r._list=e)};var a=!1;this._update_steps=function(){a||(a=!0,r._url_object&&("about:"===r._url_object.protocol&&-1!==r._url_object.pathname.indexOf("?")&&(r._url_object.pathname=r._url_object.pathname.split("?")[0]),r._url_object.search=i(r._list),a=!1))}}function l(e,t){var n=0;this.next=function(){if(n>=e.length)return{done:!0,value:void 0};var r=e[n++];return{done:!1,value:"key"===t?r.name:"value"===t?r.value:[r.name,r.value]}}}function c(t,n){if(!(this instanceof e.URL))throw new TypeError("Failed to construct 'URL': Please use the 'new' operator.");n&&(t=function(){if(r)return new a(t,n).href;var e;try{var i;if("[object OperaMini]"===Object.prototype.toString.call(window.operamini)?((e=document.createElement("iframe")).style.display="none",document.documentElement.appendChild(e),i=e.contentWindow.document):document.implementation&&document.implementation.createHTMLDocument?i=document.implementation.createHTMLDocument(""):document.implementation&&document.implementation.createDocument?((i=document.implementation.createDocument("http://www.w3.org/1999/xhtml","html",null)).documentElement.appendChild(i.createElement("head")),i.documentElement.appendChild(i.createElement("body"))):window.ActiveXObject&&((i=new window.ActiveXObject("htmlfile")).write("<head></head><body></body>"),i.close()),!i)throw Error("base not supported");var o=i.createElement("base");o.href=n,i.getElementsByTagName("head")[0].appendChild(o);var u=i.createElement("a");return u.href=t,u.href}finally{e&&e.parentNode.removeChild(e)}}());var i=function(e){if(r)return new a(e);var t=document.createElement("a");return t.href=e,t}(t||""),l=function(){if(!("defineProperties"in Object))return!1;try{var e={};return Object.defineProperties(e,{prop:{get:function(){return!0}}}),e.prop}catch(e){return!1}}()?this:document.createElement("a"),c=new u(i.search?i.search.substring(1):null);function s(){var e=i.href.replace(/#$|\?$|\?(?=#)/g,"");i.href!==e&&(i.href=e)}function f(){c._setList(i.search?o(i.search.substring(1)):[]),c._update_steps()}return c._url_object=l,Object.defineProperties(l,{href:{get:function(){return i.href},set:function(e){i.href=e,s(),f()},enumerable:!0,configurable:!0},origin:{get:function(){return"origin"in i?i.origin:this.protocol+"//"+this.host},enumerable:!0,configurable:!0},protocol:{get:function(){return i.protocol},set:function(e){i.protocol=e},enumerable:!0,configurable:!0},username:{get:function(){return i.username},set:function(e){i.username=e},enumerable:!0,configurable:!0},password:{get:function(){return i.password},set:function(e){i.password=e},enumerable:!0,configurable:!0},host:{get:function(){var e={"http:":/:80$/,"https:":/:443$/,"ftp:":/:21$/}[i.protocol];return e?i.host.replace(e,""):i.host},set:function(e){i.host=e},enumerable:!0,configurable:!0},hostname:{get:function(){return i.hostname},set:function(e){i.hostname=e},enumerable:!0,configurable:!0},port:{get:function(){return i.port},set:function(e){i.port=e},enumerable:!0,configurable:!0},pathname:{get:function(){return"/"!==i.pathname.charAt(0)?"/"+i.pathname:i.pathname},set:function(e){i.pathname=e},enumerable:!0,configurable:!0},search:{get:function(){return i.search},set:function(e){i.search!==e&&(i.search=e,s(),f())},enumerable:!0,configurable:!0},searchParams:{get:function(){return c},enumerable:!0,configurable:!0},hash:{get:function(){return i.hash},set:function(e){i.hash=e,s()},enumerable:!0,configurable:!0},toString:{value:function(){return i.toString()},enumerable:!1,configurable:!0},valueOf:{value:function(){return i.valueOf()},enumerable:!1,configurable:!0}}),l}if(Object.defineProperties(u.prototype,{append:{value:function(e,t){this._list.push({name:e,value:t}),this._update_steps()},writable:!0,enumerable:!0,configurable:!0},delete:{value:function(e){for(var t=0;t<this._list.length;)this._list[t].name===e?this._list.splice(t,1):++t;this._update_steps()},writable:!0,enumerable:!0,configurable:!0},get:{value:function(e){for(var t=0;t<this._list.length;++t)if(this._list[t].name===e)return this._list[t].value;return null},writable:!0,enumerable:!0,configurable:!0},getAll:{value:function(e){for(var t=[],n=0;n<this._list.length;++n)this._list[n].name===e&&t.push(this._list[n].value);return t},writable:!0,enumerable:!0,configurable:!0},has:{value:function(e){for(var t=0;t<this._list.length;++t)if(this._list[t].name===e)return!0;return!1},writable:!0,enumerable:!0,configurable:!0},set:{value:function(e,t){for(var n=!1,r=0;r<this._list.length;)this._list[r].name===e?n?this._list.splice(r,1):(this._list[r].value=t,n=!0,++r):++r;n||this._list.push({name:e,value:t}),this._update_steps()},writable:!0,enumerable:!0,configurable:!0},entries:{value:function(){return new l(this._list,"key+value")},writable:!0,enumerable:!0,configurable:!0},keys:{value:function(){return new l(this._list,"key")},writable:!0,enumerable:!0,configurable:!0},values:{value:function(){return new l(this._list,"value")},writable:!0,enumerable:!0,configurable:!0},forEach:{value:function(e){var t=arguments.length>1?arguments[1]:void 0;this._list.forEach(function(n,r){e.call(t,n.value,n.name)})},writable:!0,enumerable:!0,configurable:!0},toString:{value:function(){return i(this._list)},writable:!0,enumerable:!1,configurable:!0}}),"Symbol"in e&&"iterator"in e.Symbol&&(Object.defineProperty(u.prototype,e.Symbol.iterator,{value:u.prototype.entries,writable:!0,enumerable:!0,configurable:!0}),Object.defineProperty(l.prototype,e.Symbol.iterator,{value:function(){return this},writable:!0,enumerable:!0,configurable:!0})),a)for(var s in a)a.hasOwnProperty(s)&&"function"==typeof a[s]&&(c[s]=a[s]);e.URL=c,e.URLSearchParams=u}(),function(){if("1"!==new e.URLSearchParams([["a",1]]).get("a")||"1"!==new e.URLSearchParams({a:1}).get("a")){var r=e.URLSearchParams;e.URLSearchParams=function(e){if(e&&"object"==typeof e&&t(e)){var a=new r;return n(e).forEach(function(e){if(!t(e))throw TypeError();var r=n(e);if(2!==r.length)throw TypeError();a.append(r[0],r[1])}),a}return e&&"object"==typeof e?(a=new r,Object.keys(e).forEach(function(t){a.set(t,e[t])}),a):new r(e)}}}()}(self);</script>
    <script>"use strict";var get=function(t){return Array.prototype.slice.call(document.querySelectorAll(t))},addFragmentToURLState=function(t){if(!t)return t;var e=new URL(t);if(e&&""!==fragment&&fragment.length<100){var a=window.btoa(JSON.stringify({fragment:"#"===fragment[0]?fragment:"#".concat(fragment)})),r=e.searchParams.get("state"),n=e.searchParams.get("RelayState");r?e.searchParams.set("state","".concat(r,".").concat(a)):n&&e.searchParams.set("RelayState","".concat(n,".").concat(a))}return e.toString()},fragment=window.location.hash||"",article=document.querySelector("#data"),dset=article.dataset,autoRedirectToIdentity="true"===dset.autoRedirectToIdentity,autoRedirectURL=addFragmentToURLState(dset.autoRedirectUrl),message=dset.message;autoRedirectURL&&autoRedirectToIdentity&&(message||(window.location=autoRedirectURL));</script>
  </head>

  <body>
    <div class="base_AccessLogo">
        <svg width="165" height="50" fill="none" viewBox="0 0 165 50"><path d="M21.292 15.556h-7.816C17.168 6.435 26.111 0 36.556 0c13.747 0 24.89 11.143 24.89 24.89 0 13.746-11.143 24.89-24.89 24.89-10.445 0-19.388-6.436-23.08-15.557h7.816c3.145 5.132 8.805 8.556 15.265 8.556 9.88 0 17.889-8.009 17.889-17.89C54.446 15.01 46.436 7 36.556 7c-6.46 0-12.12 3.424-15.264 8.556zm-11.958 14l-3.112-3.11H45.89c1.386 0 2.08 1.674 1.1 2.655l-6.222 6.222-2.2-2.2 3.567-3.567H9.334zM32.346 16.66l2.2-2.2 6.222 6.223c.98.98.286 2.655-1.1 2.655H3.11L0 20.226h35.913l-3.567-3.567zM84.109 15.383c1.699 0 2.411-.97 2.578-2.123h2.063c-.243 2.214-1.654 3.943-4.702 3.943-2.851 0-4.884-2.078-4.884-5.309 0-3.185 2.063-5.293 4.884-5.293 3.003 0 4.474 1.698 4.702 3.959h-2.063c-.182-1.153-.925-2.14-2.593-2.14-1.593 0-2.821 1.335-2.806 3.49 0 2.183 1.213 3.473 2.82 3.473zm5.612 1.729V6.343h2.078v10.769H89.72zm6.917-7.507c2.305 0 3.822 1.531 3.822 3.76 0 2.23-1.517 3.823-3.822 3.823-2.276 0-3.808-1.517-3.808-3.746 0-2.245 1.532-3.837 3.808-3.837zm0 1.713c-1.032 0-1.715.834-1.715 2.093 0 1.23.683 2.063 1.715 2.063 1.061 0 1.713-.834 1.713-2.063 0-1.26-.652-2.093-1.713-2.093zm9.494-1.638h2.078v7.432h-2.002l-.061-1.092c-.425.698-1.153 1.168-2.184 1.168-1.699 0-2.563-1.198-2.563-2.73V9.68h2.092v4.292c0 .865.441 1.41 1.275 1.41.773 0 1.365-.621 1.365-1.546V9.68zm8.631-3.337h2.077v10.769h-2.017l-.046-1.077c-.439.668-1.197 1.153-2.305 1.153-1.881 0-3.246-1.365-3.246-3.7 0-2.276 1.29-3.883 3.353-3.883 1.092 0 1.774.439 2.184 1.09V6.344zm-1.775 9.1c1.152 0 1.775-.97 1.775-1.804v-.47c0-.895-.653-1.82-1.76-1.82-.986 0-1.668.818-1.668 2.078 0 1.228.652 2.017 1.653 2.017zm5.824-6.37c0-1.714 1.213-2.927 3.124-2.927.413.003.825.05 1.229.137l-.076 1.743c-.182-.075-.516-.136-.834-.136-.925 0-1.365.5-1.365 1.259v.531h2.154v1.653h-2.155v5.779h-2.076v-5.78h-1.063V9.68h1.062v-.607zm5.369-2.73h2.078v10.769h-2.078V6.343zm6.553 3.262c2.229 0 3.352 1.197 3.352 3.108v4.399h-1.926l-.046-1.016c-.288.47-.986 1.092-2.427 1.092-1.608 0-2.472-.804-2.472-2.048 0-1.804 1.851-2.336 3.867-2.336h.987v-.257c0-.835-.471-1.365-1.396-1.365-.788 0-1.304.379-1.38 1.03h-1.881c.137-1.652 1.426-2.608 3.321-2.608h.001zm-.41 6.081c1.092 0 1.745-.683 1.745-1.274v-.47h-1.078c-1.152 0-1.759.303-1.759.97 0 .47.364.774 1.092.774zm9.055-6.082c.167 0 .379.015.47.061l-.015 2.033c-.121-.046-.228-.077-.515-.077-.941 0-1.699.591-1.821 1.654v3.837h-2.078V9.68h2.002l.046 1.213c.349-.758.94-1.29 1.911-1.29zm7.887 3.853v.394h-5.232c.151 1.137.819 1.714 1.789 1.714 1.002 0 1.366-.516 1.487-.91h1.926c-.243 1.289-1.274 2.532-3.519 2.532-2.26 0-3.685-1.562-3.685-3.776 0-2.245 1.562-3.807 3.715-3.807 2.261 0 3.519 1.578 3.519 3.853zm-3.503-2.305c-.835 0-1.501.47-1.699 1.5h3.261c-.136-1.06-.713-1.5-1.562-1.5zM91.91 42l-1.41-4.287h-7.972L81.118 42h-3.972l7.05-19.742h4.634L95.88 42h-3.97zm-8.318-7.424h5.842l-2.906-8.806-2.936 8.806zm19.189 7.569c-4.232 0-6.936-2.964-6.936-7.166 0-4.26 2.907-7.223 6.992-7.223 4.203 0 6.217 2.532 6.563 5.669h-3.799c-.23-1.353-1.007-2.417-2.648-2.417-1.841 0-3.136 1.38-3.136 3.97 0 2.533 1.236 3.915 3.108 3.915 1.726 0 2.446-1.036 2.675-2.36h3.8c-.374 3.08-2.418 5.612-6.619 5.612zm14.496 0c-4.23 0-6.935-2.964-6.935-7.166 0-4.26 2.906-7.223 6.994-7.223 4.201 0 6.216 2.532 6.561 5.669h-3.799c-.231-1.353-1.007-2.417-2.648-2.417-1.841 0-3.136 1.38-3.136 3.97 0 2.533 1.237 3.915 3.107 3.915 1.727 0 2.446-1.036 2.677-2.36h3.799c-.375 3.08-2.417 5.612-6.619 5.612h-.001zm21.289-7.08v.748h-9.928c.288 2.159 1.554 3.252 3.396 3.252 1.899 0 2.59-.978 2.82-1.726h3.656c-.462 2.446-2.418 4.806-6.678 4.806-4.288 0-6.992-2.964-6.992-7.166 0-4.26 2.963-7.223 7.05-7.223 4.288 0 6.676 2.992 6.676 7.31zm-6.647-4.374c-1.583 0-2.849.892-3.224 2.849h6.188c-.259-2.014-1.353-2.85-2.964-2.85zm19.85 7.31c.029 2.619-2.36 4.144-6.13 4.144-3.971 0-5.986-1.612-6.015-4.547l3.367.029c.087 1.15.864 1.784 2.59 1.784 1.67 0 2.389-.49 2.36-1.295 0-2.648-8.029-.49-8.029-6.188 0-2.59 2.302-4.172 6.015-4.172 3.597 0 5.612 1.669 5.64 4.46h-3.424c-.087-1.151-.864-1.727-2.245-1.727-1.496 0-2.158.518-2.158 1.324 0 2.59 8.029.317 8.029 6.188zm13.23 0c.03 2.619-2.359 4.144-6.129 4.144-3.971 0-5.986-1.612-6.015-4.547l3.367.029c.086 1.15.864 1.784 2.591 1.784 1.669 0 2.387-.49 2.359-1.295 0-2.648-8.029-.49-8.029-6.188 0-2.59 2.302-4.172 6.015-4.172 3.597 0 5.612 1.669 5.641 4.46h-3.425c-.086-1.151-.863-1.727-2.245-1.727-1.496 0-2.158.518-2.158 1.324 0 2.59 8.029.317 8.029 6.188h-.001z" fill="#36393A"/></svg>
    </div>
    <div class="Content">

      <div class="AuthBox">
        <div class="AuthBox-body">
          <a class="OrgAvatarLink" href=''>
            <div class="OrgAvatarLink-logo">
              <img height=50 src="" style="display:none;" onload="this.style.display=''"></img>
            </div>
            <div class="OrgAvatarLink-title">
              speedcf.cloudflareaccess.com
            </div>
          </a>
          <div class="AuthBox-App">
            <div class="App-name">
              fallback-core-origin
            </div>

          </div>
          <div class="AuthBox-Normal">
              <div class="AuthBox-RequestCode ">
                <div class="AuthBox-text">Get a login code emailed to you</div>
                <form class="AuthFormLogin" action='https:&#x2F;&#x2F;speedcf.cloudflareaccess.com&#x2F;cdn-cgi&#x2F;access&#x2F;verify-code&#x2F;fallback-core-origin.speedcdnjs.com?kid&#x3D;5bf8efc97d78564ad5710e878c66913f9a11dd374ea9a4a28ed5ba59bc6b60ad&amp;redirect_url&#x3D;%2Fajax%2Flibs%2Ffont-awesome%2F6.0.0%2Fwebfonts%2Ffa-regular-400.woff&amp;meta&#x3D;****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' method="post" id="totp-form">
                  <div class="AuthFormLogin-row">
                    <label>Email</label>
                    <input class="StandardInput StandardInput-is-block EmailInput" type="email" required
                           placeholder="<EMAIL>" spellcheck="false" autocomplete="off" autocapitalize="none"
                           autofocus name="email">
                    <input hidden type="text" name="client_id" value=''>
                    <input hidden type="text" name="connector_id" value=''>
                    <input hidden type="text" name="connector_type" value=''>
                    <input hidden type="text" name="redirect_url" value=''>
                  </div>
                  <div class="AuthFormLogin-row">
                    <button type="submit" form="totp-form" class="Button Button-is-block Button-is-juicy Button-uses-org-theme-accent-color">
                      Send me a code
                    </button>
                  </div>
                </form>
              </div>

          </div>
        </div>
      </div>
    </div>
    <div class="base_TeamsLogo">
        <svg width="275" height="32" fill="none" viewBox="0 0 275 32"><g clip-path="url(#clip0_51_954)" fill="#222"><path d="M45.4 13.343c-.382-2.173-2.103-3.416-4.318-3.416-2.97 0-5.061 2.279-5.061 6.375 0 4.168 2.102 6.375 5.053 6.375 2.17 0 3.907-1.198 4.325-3.324l3.316.016c-.507 3.466-3.368 6.259-7.686 6.259-4.787 0-8.316-3.466-8.316-9.326s3.573-9.325 8.316-9.325c4.026 0 7.127 2.338 7.686 6.374l-3.316-.008zM54.43 25.373h-3.21V7.216h3.21v18.157zM56.767 18.617c0-4.22 2.535-7.04 6.527-7.04 3.991 0 6.524 2.82 6.524 7.04 0 4.22-2.535 7.021-6.524 7.021s-6.527-2.811-6.527-7.02zm9.796 0c0-2.494-1.08-4.488-3.253-4.488-2.174 0-3.288 1.995-3.288 4.488 0 2.494 1.08 4.467 3.288 4.467 2.207 0 3.253-1.986 3.253-4.475v.008zM80.834 11.756h3.208v13.617h-3.111v-2.42h-.142c-.62 1.524-2.055 2.596-4.094 2.596-2.678 0-4.54-1.84-4.54-5.124v-8.671h3.208v8.172c0 1.729 1.03 2.82 2.572 2.82 1.42 0 2.89-1.028 2.89-3.104l.009-7.886zM86.407 18.58c0-4.538 2.473-7.002 5.62-7.002 2.402 0 3.403 1.445 3.892 2.43h.134V7.216h3.216v18.157h-3.153v-2.147h-.194c-.505.993-1.561 2.386-3.9 2.386-3.213 0-5.615-2.543-5.615-7.032zm9.707-.015c0-2.644-1.136-4.354-3.2-4.354-2.137 0-3.236 1.818-3.236 4.354 0 2.535 1.117 4.414 3.235 4.414 2.05 0 3.2-1.773 3.2-4.414zM109.223 14.237h-2.827v11.136h-3.211V14.237h-2.01v-2.48h2.01v-1.288c0-2.746 1.889-4.105 4.257-4.105a8 8 0 0 1 2.438.373l-.649 2.484a4.062 4.062 0 0 0-1.196-.195c-1.198 0-1.639.594-1.639 1.656v1.074h2.827v2.48zM114.576 25.373h-3.211V7.216h3.211v18.157zM116.859 21.552c0-3.069 2.528-3.857 5.169-4.14 2.402-.264 3.369-.303 3.369-1.225v-.053c0-1.337-.815-2.102-2.305-2.102-1.576 0-2.473.788-2.793 1.729l-2.996-.426c.71-2.483 2.907-3.76 5.781-3.76 2.599 0 5.531 1.083 5.531 4.68v9.115h-3.084V23.5h-.105c-.586 1.142-1.864 2.144-4 2.144-2.607.002-4.567-1.417-4.567-4.092zm8.545-1.05v-1.606c-.415.336-2.102.549-2.943.665-1.434.204-2.506.717-2.506 1.95 0 1.18.956 1.791 2.294 1.791 1.934-.013 3.155-1.298 3.155-2.814v.013zM131.467 11.756h3.111v2.268h.142a3.412 3.412 0 0 1 3.36-2.465c.386.001.771.031 1.151.09v2.953a6.074 6.074 0 0 0-1.487-.186c-1.766 0-3.069 1.221-3.069 2.95v8.007h-3.208V11.756zM139.841 18.643c0-4.204 2.544-7.065 6.446-7.065 3.35 0 6.222 2.102 6.222 6.879v.985h-9.486c.026 2.331 1.401 3.695 3.477 3.695 1.382 0 2.446-.602 2.89-1.755l2.998.338c-.567 2.366-2.748 3.918-5.923 3.918-4.115 0-6.624-2.722-6.624-6.995zm9.594-1.366c-.019-1.84-1.262-3.198-3.104-3.198-1.915 0-3.2 1.461-3.297 3.198h6.401zM161.474 23.323l9.31-13.348h-9.362v-2.76h13.438v2.05l-9.3 13.357h9.355v2.759h-13.441v-2.058zM176.628 18.643c0-4.204 2.546-7.065 6.446-7.065 3.35 0 6.222 2.102 6.222 6.879v.985h-9.494c.029 2.331 1.403 3.695 3.477 3.695 1.382 0 2.446-.602 2.89-1.755l2.998.338c-.567 2.366-2.748 3.918-5.922 3.918-4.108 0-6.617-2.722-6.617-6.995zm9.594-1.366c-.019-1.84-1.259-3.198-3.104-3.198-1.915 0-3.2 1.461-3.297 3.198h6.401zM191.259 11.756h3.114v2.268h.142a3.398 3.398 0 0 1 3.358-2.465c.386.002.772.031 1.154.09v2.953a6.115 6.115 0 0 0-1.49-.187c-1.764 0-3.067 1.222-3.067 2.951v8.007h-3.211V11.756zM199.26 18.617c0-4.22 2.536-7.04 6.525-7.04 3.989 0 6.524 2.82 6.524 7.04 0 4.22-2.535 7.021-6.524 7.021s-6.525-2.811-6.525-7.02zm9.796 0c0-2.494-1.082-4.488-3.253-4.488-2.17 0-3.29 1.995-3.29 4.488 0 2.494 1.083 4.467 3.29 4.467 2.208 0 3.253-1.986 3.253-4.475v.008zM217.064 7.216h14.484v2.759h-5.61v15.398h-3.264V9.975h-5.61v-2.76zM232.024 11.756h3.111v2.268h.142a3.412 3.412 0 0 1 3.361-2.465c.385.001.77.031 1.15.09v2.953a6.081 6.081 0 0 0-1.489-.186c-1.764 0-3.067 1.221-3.067 2.95v8.007h-3.208V11.756zM249.855 11.756h3.209v13.617h-3.112v-2.42h-.152c-.62 1.524-2.057 2.596-4.097 2.596-2.675 0-4.538-1.84-4.538-5.124v-8.671h3.209v8.172c0 1.729 1.03 2.82 2.572 2.82 1.419 0 2.891-1.028 2.891-3.104l.018-7.886zM263.448 15.674c-.247-.896-1.027-1.702-2.507-1.702-1.34 0-2.365.638-2.349 1.56 0 .789.539 1.267 1.924 1.577l2.323.496c2.569.56 3.82 1.748 3.828 3.68 0 2.58-2.412 4.343-5.815 4.343-3.342 0-5.445-1.482-5.841-3.981l3.127-.302c.276 1.224 1.232 1.863 2.704 1.863 1.526 0 2.535-.702 2.535-1.632 0-.77-.583-1.277-1.839-1.55l-2.323-.49c-2.628-.54-3.839-1.852-3.829-3.838 0-2.518 2.223-4.13 5.505-4.13 3.19 0 5.035 1.47 5.479 3.775l-2.922.331zM275.367 14.237h-2.685v7.04c0 1.295.646 1.576 1.453 1.576.317-.004.633-.047.941-.126l.541 2.51a6.44 6.44 0 0 1-1.889.317c-2.41.074-4.273-1.187-4.254-3.75v-7.57h-1.935v-2.48h1.935V8.49h3.208v3.264h2.685v2.483zM5.255 15.742c1.9-.107 2.665-.935 2.665-3.116V3.992a13.401 13.401 0 0 1-5.292 1.962L0 6.322v9.42h5.255zM7.92 20.446c0-2.286-.846-3.085-2.956-3.127H.113c.754 6.075 4.911 10.122 7.807 12.248v-9.12zM21.644 5.954a13.367 13.367 0 0 1-7.22-3.458L12.128 0 9.86 2.483c-.026.026-.15.153-.363.34v9.882c.019 2.12.789 2.93 2.66 3.037h6.43l-2.589-2.585 1.117-1.114 4.488 4.488-4.488 4.49-1.117-1.114 2.589-2.588H12.45c-2.086.042-2.935.823-2.954 3.048v10.248c.705.434 1.18.673 1.267.715l1.374.67 1.372-.67c.439-.215 10.773-5.381 10.773-15.974V6.33l-2.638-.376z"/></g><defs><clipPath id="clip0_51_954"><path fill="#fff" d="M0 0h275v32H0z"/></clipPath></defs></svg>
    </div>

    <script>"use strict";if(document.addEventListener("keyup",function(e){if("BODY"===e.target.nodeName&&e.key>=1&&e.key<=10){var t=document.getElementById("idp"+e.key);window.location=t.getAttribute("href")}},!1),""!==fragment&&fragment.length<100){var services=get(".js-idp")||[];services.map(function(e){return e.href=addFragmentToURLState(e.href),e})}</script>
  </body>

</html>