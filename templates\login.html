{% extends "base.html" %}

{% block title %}登录 - 康达价格管理系统{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
    }
    
    .login-logo {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .login-logo i {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 10px;
    }
    
    .login-logo h3 {
        color: #2c3e50;
        font-weight: bold;
        margin: 0;
    }
    
    .login-logo p {
        color: #6c757d;
        margin: 5px 0 0 0;
        font-size: 0.9rem;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border: 2px solid #e9ecef;
        border-right: none;
        border-radius: 10px 0 0 10px;
    }
    
    .input-group .form-control {
        border-left: none;
        border-radius: 0 10px 10px 0;
    }
    
    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-size: 1.1rem;
        font-weight: bold;
        color: white;
        width: 100%;
        transition: all 0.3s;
    }
    
    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-login:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
    }
    
    .captcha-container {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .captcha-image {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        max-height: 40px;
    }
    
    .captcha-image:hover {
        border-color: #667eea;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        display: none;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
    }
    
    .error-message {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        color: #721c24;
        padding: 12px 15px;
        margin-bottom: 20px;
        display: none;
    }
    
    .success-message {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        color: #155724;
        padding: 12px 15px;
        margin-bottom: 20px;
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-logo">
            <i class="fas fa-chart-line"></i>
            <h3>康达价格管理系统</h3>
            <p>让价格数据管理更智能、更高效</p>
        </div>
        
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>
        
        <form id="login-form">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="请输入用户名" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="请输入密码" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="captcha" class="form-label">验证码</label>
                <div class="captcha-container">
                    <input type="text" class="form-control" id="captcha" name="captcha" 
                           placeholder="验证码" required style="flex: 1;">
                    <img id="captcha-image" class="captcha-image" alt="验证码" title="点击刷新验证码">
                </div>
            </div>
            
            <button type="submit" class="btn btn-login" id="login-btn">
                <i class="fas fa-sign-in-alt me-2"></i>登录
            </button>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                首次使用请联系管理员获取账号
            </small>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div id="loading-overlay" class="loading-overlay">
    <div class="loading-content">
        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
        <h5>正在登录...</h5>
        <p class="text-muted">请稍候</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 页面加载时获取验证码
    refreshCaptcha();
    
    // 点击验证码图片刷新
    $('#captcha-image').click(function() {
        refreshCaptcha();
    });
    
    // 登录表单提交
    $('#login-form').submit(function(e) {
        e.preventDefault();
        handleLogin();
    });
    
    // 回车键提交
    $('#login-form input').keypress(function(e) {
        if (e.which === 13) {
            handleLogin();
        }
    });
});

// 刷新验证码
function refreshCaptcha() {
    showLoading('captcha-image', '加载验证码...');
    
    fetch('/api/get_captcha')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#captcha-image').attr('src', 'data:image/jpeg;base64,' + data.image_base64);
                $('#captcha-image').show();
            } else {
                showError('error-message', '获取验证码失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('获取验证码失败:', error);
            showError('error-message', '获取验证码失败，请检查网络连接');
        });
}

// 处理登录
function handleLogin() {
    const username = $('#username').val().trim();
    const password = $('#password').val().trim();
    const captcha = $('#captcha').val().trim();
    
    // 验证输入
    if (!username || !password || !captcha) {
        showError('error-message', '请填写完整的登录信息');
        return;
    }
    
    // 显示加载状态
    $('#loading-overlay').show();
    $('#login-btn').prop('disabled', true);
    hideMessages();
    
    // 发送登录请求
    fetch('/api/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            password: password,
            captcha: captcha
        })
    })
    .then(response => response.json())
    .then(data => {
        $('#loading-overlay').hide();
        $('#login-btn').prop('disabled', false);
        
        if (data.success) {
            showSuccess('success-message', '登录成功，正在跳转...');
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            showError('error-message', data.message);
            refreshCaptcha(); // 刷新验证码
            $('#captcha').val(''); // 清空验证码输入
        }
    })
    .catch(error => {
        console.error('登录失败:', error);
        $('#loading-overlay').hide();
        $('#login-btn').prop('disabled', false);
        showError('error-message', '登录失败，请检查网络连接');
        refreshCaptcha();
    });
}

// 显示错误消息
function showError(elementId, message) {
    hideMessages();
    const element = $('#' + elementId);
    element.text(message).show();
}

// 显示成功消息
function showSuccess(elementId, message) {
    hideMessages();
    const element = $('#' + elementId);
    element.text(message).show();
}

// 隐藏所有消息
function hideMessages() {
    $('.error-message, .success-message').hide();
}

// 显示加载状态到指定元素
function showLoading(elementId, message) {
    $('#' + elementId).hide();
}
</script>
{% endblock %}