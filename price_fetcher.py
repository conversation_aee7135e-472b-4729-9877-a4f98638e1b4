"""
康达价格管理系统 - 数据抓取模块
基于现有的network_manager.py实现价格数据的自动抓取
"""

import os
import json
import time
import sqlite3
import uuid
import threading
from datetime import datetime, timezone, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
from network_manager import HttpRequestManager
from api_validator import ApiResponseValidator


class RateLimiter:
    """频率限制器"""
    
    def __init__(self, requests_per_second=1, max_requests_per_minute=30):
        self.requests_per_second = requests_per_second
        self.max_requests_per_minute = max_requests_per_minute
        self.request_times = []
        self.lock = threading.Lock()
    
    def wait_if_needed(self):
        """如果需要的话等待"""
        with self.lock:
            current_time = time.time()
            
            # 清理1分钟前的请求记录
            self.request_times = [t for t in self.request_times if current_time - t < 60]
            
            # 检查每分钟请求数限制
            if len(self.request_times) >= self.max_requests_per_minute:
                wait_time = 60 - (current_time - self.request_times[0]) + 1
                if wait_time > 0:
                    print(f"达到每分钟请求限制，等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    current_time = time.time()
            
            # 检查每秒请求数限制
            if self.request_times:
                time_since_last = current_time - self.request_times[-1]
                min_interval = 1.0 / self.requests_per_second
                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    print(f"频率限制等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    current_time = time.time()
            
            # 记录当前请求时间
            self.request_times.append(current_time)


class PriceDataFetcher:
    """价格数据抓取器"""

    def __init__(self, db_path='kangda_prices.db', max_workers=3, requests_per_second=1, max_requests_per_minute=30):
        self.db_path = db_path
        self.http_manager = HttpRequestManager()
        self.base_url = "http://106.12.16.234/ehscare/serviceItem/pageList"
        self.current_session_id = None
        self.session_start_time = None

        # API响应验证器
        self.api_validator = ApiResponseValidator('price_fetcher')

        # 多线程配置
        self.max_workers = max_workers
        self.rate_limiter = RateLimiter(requests_per_second, max_requests_per_minute)
        self.stop_event = threading.Event()
        self.stats_lock = threading.Lock()

        # 统计信息
        self.total_processed = 0
        self.total_failed = 0

        # 错误信息
        self.last_error_message = None

        self.init_database()

    def convert_unix_timestamp_to_datetime(self, timestamp_ms):
        """将Unix时间戳（毫秒）转换为北京时间的datetime对象

        Args:
            timestamp_ms: Unix时间戳（毫秒）

        Returns:
            datetime: 北京时间的datetime对象，如果转换失败返回None
        """
        if not timestamp_ms:
            return None

        try:
            # 转换为秒
            timestamp_s = int(timestamp_ms) / 1000

            # 创建UTC时间
            utc_dt = datetime.fromtimestamp(timestamp_s, tz=timezone.utc)

            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_dt = utc_dt.astimezone(beijing_tz)

            # 返回不带时区信息的datetime对象（数据库存储用）
            return beijing_dt.replace(tzinfo=None)

        except (ValueError, TypeError, OverflowError) as e:
            print(f"[DEBUG] 时间戳转换失败: {timestamp_ms} - {e}")
            return None

    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 检查数据库是否已存在
            if os.path.exists(self.db_path):
                print(f"数据库文件已存在: {self.db_path}")
                # 确保fetch_status表存在（用于并发控制）
                self._ensure_fetch_status_table(conn)
            else:
                # 读取并执行数据库结构脚本
                try:
                    with open('database_schema.sql', 'r', encoding='utf-8') as f:
                        schema = f.read()
                    conn.executescript(schema)
                    print("数据库初始化完成")
                except FileNotFoundError:
                    print("警告: 未找到database_schema.sql文件，请确保数据库结构已正确创建")
                    # 手动创建必要的表
                    self._create_essential_tables(conn)

    def _ensure_fetch_status_table(self, conn):
        """确保fetch_status表存在"""
        cursor = conn.cursor()

        # 启用WAL模式以改善并发性能
        try:
            cursor.execute("PRAGMA journal_mode=WAL")
            print("已启用WAL模式以改善并发性能")
        except Exception as e:
            print(f"启用WAL模式失败: {e}")

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS fetch_status (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                is_running INTEGER DEFAULT 0,
                task_id TEXT,
                user_session_key TEXT,
                username TEXT,
                start_time TIMESTAMP,
                timeout_time TIMESTAMP,
                progress INTEGER DEFAULT 0,
                message TEXT DEFAULT '空闲',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 初始化状态记录（如果不存在）
        cursor.execute("INSERT OR IGNORE INTO fetch_status (id) VALUES (1)")
        conn.commit()
        print("fetch_status表已确保存在")

    def _create_essential_tables(self, conn):
        """创建必要的表结构"""
        cursor = conn.cursor()

        # 启用WAL模式以改善并发性能
        try:
            cursor.execute("PRAGMA journal_mode=WAL")
            print("已启用WAL模式以改善并发性能")
        except Exception as e:
            print(f"启用WAL模式失败: {e}")

        # 创建fetch_status表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS fetch_status (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                is_running INTEGER DEFAULT 0,
                task_id TEXT,
                user_session_key TEXT,
                username TEXT,
                start_time TIMESTAMP,
                timeout_time TIMESTAMP,
                progress INTEGER DEFAULT 0,
                message TEXT DEFAULT '空闲',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 初始化状态记录
        cursor.execute("INSERT OR IGNORE INTO fetch_status (id) VALUES (1)")
        conn.commit()
        print("基本表结构创建完成")

    def start_fetch_session(self):
        """开始新的抓取会话"""
        self.current_session_id = str(uuid.uuid4())
        self.session_start_time = datetime.now()
        print(f"开始抓取会话: {self.current_session_id}")
        return self.current_session_id

    def end_fetch_session(self, conn):
        """结束当前抓取会话"""
        if self.current_session_id:
            session_end_time = datetime.now()
            cursor = conn.cursor()

            # 更新所有会话记录的结束时间
            cursor.execute('''
                UPDATE fetch_logs
                SET session_end_time = ?
                WHERE session_id = ?
            ''', (session_end_time, self.current_session_id))

            print(f"结束抓取会话: {self.current_session_id}")
            self.current_session_id = None
            self.session_start_time = None
    
    def login_if_needed(self, username, password, captcha_path=None):
        """如果需要则进行登录"""
        if captcha_path:
            # 获取验证码
            success, image_data, jsessionid, message = self.http_manager.get_captcha_image()
            if success:
                with open(captcha_path, 'wb') as f:
                    f.write(image_data)
                print(f"验证码已保存到: {captcha_path}")
                captcha = input("请输入验证码: ")
                
                # 登录
                success, message, user_info = self.http_manager.login(
                    username, password, captcha, jsessionid
                )
                if success:
                    print("登录成功!")
                    return True
                else:
                    print(f"登录失败: {message}")
                    return False
            else:
                print(f"获取验证码失败: {message}")
                return False
        else:
            print("使用现有Cookie进行请求")
            return True
    
    def fetch_total_count(self):
        """获取数据总数"""
        params = {
            'source': 1,
            'id': '',
            'keyWord': '',
            'level': '',
            'page': 1,
            'rows': 1  # 只获取1条记录来获取总数
        }

        try:
            print(f"[DEBUG] 发送请求到: {self.base_url}")
            print(f"[DEBUG] 请求参数: {params}")
            print(f"[DEBUG] Session cookies: {len(self.http_manager.session.cookies)} 个")

            response = self.http_manager.session.get(self.base_url, params=params)
            print(f"[DEBUG] 响应状态码: {response.status_code}")
            print(f"[DEBUG] 响应内容: {response.text[:200]}...")

            response.raise_for_status()
            data = response.json()
            print(f"[DEBUG] 解析的JSON数据: {data}")

            # 使用API验证器验证响应
            is_valid, error_message, validated_data = self.api_validator.validate_price_api_response(
                data, f"获取总数 - 页面1"
            )

            if not is_valid:
                print(f"[DEBUG] API响应验证失败: {error_message}")
                # 将错误信息存储到实例变量中，供上层调用者获取
                self.last_error_message = error_message
                return 0

            total = validated_data['total']
            print(f"[DEBUG] 获取到数据总数: {total}")
            return total

        except Exception as e:
            error_msg = f"获取总数时发生异常: {e}"
            print(f"[DEBUG] {error_msg}")
            self.last_error_message = "数据获取失败，可能是因为您没有访问权限或API服务异常"
            return 0
    
    def fetch_page_data_threadsafe(self, page, rows=800, max_retries=3):
        """线程安全的页面数据获取方法"""
        if self.stop_event.is_set():
            return [], 0
            
        # 频率控制
        self.rate_limiter.wait_if_needed()
        
        params = {
            'source': 1,
            'id': '',
            'keyWord': '',
            'level': '',
            'page': page,
            'rows': rows
        }
        
        # 重试机制
        for attempt in range(max_retries):
            try:
                if self.stop_event.is_set():
                    return [], 0
                
                print(f"[线程 {threading.current_thread().name}] 正在获取第 {page} 页数据 (尝试 {attempt + 1}/{max_retries})...")
                
                # 使用独立的http_manager实例避免线程冲突
                response = self.http_manager.session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                data = response.json()

                # 使用API验证器验证响应
                is_valid, error_message, validated_data = self.api_validator.validate_price_api_response(
                    data, f"获取页面数据 - 页面{page}"
                )

                if not is_valid:
                    print(f"[线程 {threading.current_thread().name}] API响应验证失败: {error_message}")
                    # 将错误信息存储到实例变量中
                    self.last_error_message = error_message

                    if attempt < max_retries - 1:
                        # 重试前等待递增时间
                        wait_time = (attempt + 1) * 2
                        print(f"[线程 {threading.current_thread().name}] 将在 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    continue

                rows_data = validated_data['rows']
                total = validated_data['total']
                print(f"[线程 {threading.current_thread().name}] 第 {page} 页获取成功，获得 {len(rows_data)} 条记录")
                return rows_data, total
                    
            except Exception as e:
                print(f"[线程 {threading.current_thread().name}] 获取第 {page} 页时发生错误: {e}")
                
                if attempt < max_retries - 1:
                    # 重试前等待递增时间
                    wait_time = (attempt + 1) * 2
                    print(f"[线程 {threading.current_thread().name}] 将在 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 最后一次尝试失败
                    with self.stats_lock:
                        self.total_failed += 1
        
        return [], 0
    
    def save_service_item(self, conn, item_data):
        """保存检测项目数据"""
        cursor = conn.cursor()
        
        # 保存服务分类
        if item_data.get('serviceCategoryId') and item_data.get('serviceCategoryName'):
            cursor.execute('''
                INSERT OR IGNORE INTO service_categories (id, name)
                VALUES (?, ?)
            ''', (item_data['serviceCategoryId'], item_data['serviceCategoryName']))
        
        # 保存二级分类
        if item_data.get('secondCategoryId') and item_data.get('secondCategoryName'):
            cursor.execute('''
                INSERT OR IGNORE INTO second_categories (id, name, service_category_id)
                VALUES (?, ?, ?)
            ''', (item_data['secondCategoryId'], item_data['secondCategoryName'], 
                  item_data.get('serviceCategoryId')))
        
        # 保存检测项目
        cursor.execute('''
            INSERT OR REPLACE INTO service_items 
            (id, name, number, service_category_id, second_category_id,
             service_category_name, second_category_name, child_item, status, 
             auto_rule, remark, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            item_data['id'],
            item_data['name'],
            item_data['number'],
            item_data.get('serviceCategoryId'),
            item_data.get('secondCategoryId'),
            item_data.get('serviceCategoryName'),
            item_data.get('secondCategoryName'),
            item_data.get('childItem'),
            item_data.get('status', 0),
            item_data.get('autoRule'),
            item_data.get('remark'),
            datetime.now()
        ))
        
        return item_data['id']
    
    def save_check_method(self, conn, method_data, service_item_id, fetch_time):
        """保存检测方法数据"""
        cursor = conn.cursor()

        # 查询现有数据，用于变动检测
        cursor.execute('''
            SELECT deprecated, last_date, price, lowest_bid, cma, cnas, gov_agree, status, name
            FROM check_methods WHERE id = ?
        ''', (method_data['id'],))
        existing_row = cursor.fetchone()

        existing_deprecated = 0  # 默认值
        existing_last_date = None
        existing_price = None
        existing_lowest_bid = None
        existing_cma = None
        existing_cnas = None
        existing_gov_agree = None
        existing_status = None
        existing_name = None

        if existing_row:
            (existing_deprecated, existing_last_date, existing_price, existing_lowest_bid,
             existing_cma, existing_cnas, existing_gov_agree, existing_status, existing_name) = existing_row
            existing_deprecated = existing_deprecated if existing_deprecated is not None else 0

        # 处理lastDate字段 - 转换Unix时间戳为北京时间
        last_date = None
        if 'lastDate' in method_data and method_data['lastDate']:
            last_date = self.convert_unix_timestamp_to_datetime(method_data['lastDate'])
            if last_date:
                print(f"[DEBUG] 转换时间戳 {method_data['lastDate']} -> {last_date}")

        # 检测变动并记录到变动历史表
        if existing_row:  # 如果是更新现有方法
            self._detect_and_record_method_changes(
                cursor, method_data['id'], existing_row, method_data, last_date, fetch_time
            )

        # 保存检测方法，保留现有的deprecated状态
        cursor.execute('''
            INSERT OR REPLACE INTO check_methods
            (id, method_no, name, service_item_id, price, according_by_ratio,
             lowest_bid, cma, cnas, gov_agree, status, remark, method_remark,
             analysis_cost, order_index, last_date, deprecated, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            method_data['id'],
            method_data['methodNo'],
            method_data['name'],
            service_item_id,
            method_data.get('price'),
            method_data.get('accordingByRatio', False),
            method_data.get('lowestBid'),
            method_data.get('cma', 0),
            method_data.get('cnas', 0),
            method_data.get('govAgree', 0),  # 新增：NHC资质字段
            method_data.get('status', 0),
            method_data.get('remark'),
            method_data.get('methodRemark'),
            method_data.get('analysisCost'),
            method_data.get('orderIndex'),
            last_date,  # 新增：最后变动时间
            existing_deprecated,  # 保留现有的废止状态
            datetime.now()
        ))
        
        # 保存样品类型（如果存在）
        sample_types = method_data.get('sampleTypes')
        if sample_types and isinstance(sample_types, (list, tuple)):
            for sample_type in sample_types:
                if sample_type:  # 确保sample_type不为None
                    self.save_sample_type(conn, sample_type, method_data['id'], fetch_time)
        
        # 保存价格历史记录
        self.save_price_history(conn, method_data, service_item_id, None, fetch_time)
        
        return method_data['id']
    
    def save_sample_type(self, conn, sample_data, check_method_id, fetch_time):
        """保存样品类型数据"""
        cursor = conn.cursor()
        
        # 确保type字段不为空，如果为空则使用默认值
        sample_type = sample_data.get('type') or '未指定类型'
        if not sample_type.strip():
            sample_type = '未指定类型'
        
        cursor.execute('''
            INSERT OR REPLACE INTO sample_types 
            (id, check_method_id, type, price, sample_cost, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            sample_data['id'],
            check_method_id,
            sample_type,
            sample_data.get('price'),
            sample_data.get('sampleCost'),
            datetime.now()
        ))
        
        # 保存样品类型的价格历史记录
        self.save_price_history(conn, None, None, sample_data, fetch_time, check_method_id)
        
        return sample_data['id']
    
    def save_price_history(self, conn, method_data=None, service_item_id=None, 
                          sample_data=None, fetch_time=None, check_method_id=None):
        """保存价格历史记录"""
        cursor = conn.cursor()
        
        if method_data:
            # 检测方法价格记录
            # 注意：这里需要同时保存分析价格和最低价格的历史记录
            cursor.execute('''
                INSERT INTO price_history
                (service_item_id, check_method_id, check_method_price, lowest_bid,
                 analysis_cost, cma_status, cnas_status, gov_agree_status, fetch_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                service_item_id,
                method_data['id'],
                method_data.get('price'),  # 分析价格
                method_data.get('lowestBid'), # 最低价格
                method_data.get('analysisCost'),  # 分析成本
                method_data.get('cma', 0),
                method_data.get('cnas', 0),
                method_data.get('govAgree', 0),  # 新增：NHC资质状态
                fetch_time
            ))

            # 如果有最低价格，也需要单独记录（因为price_history表结构限制）
            # 这里我们可以考虑在后续版本中改进数据库结构
        
        if sample_data:
            # 样品类型价格记录
            cursor.execute('''
                INSERT INTO price_history 
                (check_method_id, sample_type_id, sample_type_price, sample_cost, fetch_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                check_method_id,
                sample_data['id'],
                sample_data.get('price'),
                sample_data.get('sampleCost'),
                fetch_time
            ))
    
    def save_fetch_log(self, conn, page, total_records, fetched_records, status='success', error_message=None):
        """保存抓取日志"""
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO fetch_logs
            (session_id, page_number, total_records, fetched_records, status, error_message, fetch_time, session_start_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (self.current_session_id, page, total_records, fetched_records, status, error_message, datetime.now(), self.session_start_time))
    
    def fetch_all_data(self, rows_per_page=800):
        """抓取所有数据"""
        print("开始抓取价格数据...")
        fetch_time = datetime.now()
        
        # 获取总数据量
        total_count = self.fetch_total_count()
        if total_count == 0:
            print("没有数据可抓取")
            return False
        
        # 计算需要抓取的页数
        total_pages = (total_count + rows_per_page - 1) // rows_per_page
        print(f"总共需要抓取 {total_pages} 页数据")
        
        total_processed = 0
        
        with sqlite3.connect(self.db_path) as conn:
            for page in range(1, total_pages + 1):
                try:
                    # 获取页面数据
                    rows_data, total = self.fetch_page_data_threadsafe(page, rows_per_page)
                    
                    if not rows_data:
                        self.save_fetch_log(conn, page, total, 0, 'failed', '获取数据失败')
                        continue
                    
                    # 处理每个检测项目
                    processed_count = 0
                    for item in rows_data:
                        try:
                            # 保存检测项目
                            service_item_id = self.save_service_item(conn, item)
                            
                            # 保存检测方法
                            for method in item.get('checkMethods', []):
                                self.save_check_method(conn, method, service_item_id, fetch_time)
                            
                            processed_count += 1
                            
                        except Exception as e:
                            print(f"处理项目 {item.get('name', 'unknown')} 时出错: {e}")
                    
                    # 记录成功日志
                    self.save_fetch_log(conn, page, total, processed_count)
                    total_processed += processed_count
                    
                    print(f"第 {page}/{total_pages} 页处理完成，本页处理 {processed_count} 个项目")
                    
                    # 添加延时避免请求过快
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"处理第 {page} 页时出错: {e}")
                    with sqlite3.connect(self.db_path) as conn:
                        self.save_fetch_log(conn, page, total_count, 0, 'failed', str(e))
        
        print(f"数据抓取完成！总共处理了 {total_processed} 个项目")

        # 触发价格变动分析缓存更新
        self._update_price_change_cache()

        return True
    
    def process_page_data_threadsafe(self, page, rows_data, fetch_time):
        """线程安全的页面数据处理方法"""
        if self.stop_event.is_set():
            return 0
            
        processed_count = 0
        
        # 使用独立的数据库连接，避免线程冲突
        try:
            with sqlite3.connect(self.db_path) as conn:
                for item_data in rows_data:
                    try:
                        if self.stop_event.is_set():
                            break
                            
                        # 保存检测项目
                        service_item_id = self.save_service_item(conn, item_data)
                        
                        # 保存检测方法
                        check_methods = item_data.get('checkMethods', [])
                        if check_methods and isinstance(check_methods, (list, tuple)):
                            for method in check_methods:
                                if method:
                                    self.save_check_method(conn, method, service_item_id, fetch_time)
                        
                        processed_count += 1
                        
                    except Exception as e:
                        print(f"[线程 {threading.current_thread().name}] 处理项目 {item_data.get('name', 'unknown')} 时出错: {e}")
                        with self.stats_lock:
                            self.total_failed += 1
                
                # 保存抓取日志
                self.save_fetch_log(conn, page, len(rows_data), processed_count, 'success')
                
        except Exception as e:
            print(f"[线程 {threading.current_thread().name}] 处理第 {page} 页数据时发生数据库错误: {e}")
            with self.stats_lock:
                self.total_failed += len(rows_data)
            return 0
        
        # 更新统计信息
        with self.stats_lock:
            self.total_processed += processed_count
            
        return processed_count
    
    def fetch_all_data_with_progress(self, progress_callback=None):
        """抓取所有数据，支持进度回调（保持向后兼容性）"""
        print("开始抓取价格数据...")
        fetch_time = datetime.now()

        # 开始新的抓取会话
        self.start_fetch_session()

        # 获取总数据量
        total_count = self.fetch_total_count()
        if total_count == 0:
            print("没有数据可抓取")
            return False

        # 计算需要抓取的页数
        rows_per_page = 800
        total_pages = (total_count + rows_per_page - 1) // rows_per_page
        print(f"总共需要抓取 {total_pages} 页数据")

        total_processed = 0

        with sqlite3.connect(self.db_path) as conn:
            for page in range(1, total_pages + 1):
                try:
                    # 调用进度回调
                    if progress_callback:
                        progress_callback(page, total_pages, f'正在抓取第 {page}/{total_pages} 页数据...')
                    
                    # 获取页面数据（使用线程安全版本）
                    rows_data, total = self.fetch_page_data_threadsafe(page, rows_per_page)
                    
                    if not rows_data:
                        self.save_fetch_log(conn, page, total, 0, 'failed', '获取数据失败')
                        continue
                    
                    # 处理每个检测项目
                    page_processed = 0
                    for item_data in rows_data:
                        try:
                            # 保存检测项目
                            service_item_id = self.save_service_item(conn, item_data)
                            
                            # 保存检测方法
                            check_methods = item_data.get('checkMethods', [])
                            if check_methods and isinstance(check_methods, (list, tuple)):
                                for method in check_methods:
                                    if method:
                                        self.save_check_method(conn, method, service_item_id, fetch_time)
                            
                            page_processed += 1
                            
                        except Exception as e:
                            print(f"处理项目 {item_data.get('name', 'unknown')} 时出错: {e}")
                    
                    total_processed += page_processed
                    
                    # 保存抓取日志
                    self.save_fetch_log(conn, page, total, page_processed, 'success', f'成功处理 {page_processed} 个项目')
                    print(f"完成第 {page}/{total_pages} 页，处理了 {page_processed} 个项目")
                    
                    # 添加延时避免请求过快
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"处理第 {page} 页时出错: {e}")
                    with sqlite3.connect(self.db_path) as conn:
                        self.save_fetch_log(conn, page, total_count, 0, 'failed', str(e))
        
            # 结束抓取会话
            self.end_fetch_session(conn)

        # 最终进度回调
        if progress_callback:
            progress_callback(total_pages, total_pages, '数据抓取完成')

        print(f"数据抓取完成！总共处理了 {total_processed} 个项目")

        # 触发价格变动分析缓存更新
        self._update_price_change_cache()

        return True

    def fetch_all_data_multithreaded(self, progress_callback=None, rows_per_page=800):
        """多线程抓取所有数据"""
        print("开始多线程抓取价格数据...")
        fetch_time = datetime.now()
        
        # 重置停止事件和统计信息
        self.stop_event.clear()
        with self.stats_lock:
            self.total_processed = 0
            self.total_failed = 0
        
        # 开始新的抓取会话
        self.start_fetch_session()
        
        try:
            # 获取总数据量
            total_count = self.fetch_total_count()
            if total_count == 0:
                print("没有数据可抓取")
                return False
            
            # 计算需要抓取的页数
            total_pages = (total_count + rows_per_page - 1) // rows_per_page
            print(f"总共需要抓取 {total_pages} 页数据，使用 {self.max_workers} 个线程")
            
            # 创建页面任务队列
            page_tasks = list(range(1, total_pages + 1))
            
            # 使用线程池执行任务
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_page = {
                    executor.submit(self._fetch_and_process_page, page, rows_per_page, fetch_time): page
                    for page in page_tasks
                }
                
                # 处理完成的任务
                completed_pages = 0
                for future in as_completed(future_to_page):
                    page = future_to_page[future]
                    
                    try:
                        processed_count = future.result()
                        completed_pages += 1
                        
                        # 调用进度回调
                        if progress_callback:
                            progress_callback(
                                completed_pages, 
                                total_pages, 
                                f'已完成 {completed_pages}/{total_pages} 页，处理了 {processed_count} 个项目'
                            )
                        
                        print(f"完成第 {page} 页，处理了 {processed_count} 个项目 ({completed_pages}/{total_pages})")
                        
                    except Exception as e:
                        print(f"处理第 {page} 页时发生异常: {e}")
                        completed_pages += 1
                        
                        # 记录失败日志
                        try:
                            with sqlite3.connect(self.db_path) as conn:
                                self.save_fetch_log(conn, page, 0, 0, 'failed', str(e))
                        except:
                            pass
                    
                    # 检查是否需要停止
                    if self.stop_event.is_set():
                        print("收到停止信号，正在取消剩余任务...")
                        for remaining_future in future_to_page:
                            remaining_future.cancel()
                        break
            
            # 结束抓取会话
            with sqlite3.connect(self.db_path) as conn:
                self.end_fetch_session(conn)
            
            # 最终进度回调
            if progress_callback:
                progress_callback(total_pages, total_pages, '多线程数据抓取完成')
            
            print(f"多线程数据抓取完成！")
            print(f"总共处理了 {self.total_processed} 个项目")
            print(f"失败数量: {self.total_failed}")

            # 触发价格变动分析缓存更新
            self._update_price_change_cache()

            return True
            
        except Exception as e:
            print(f"多线程抓取过程中发生错误: {e}")
            return False
    
    def _fetch_and_process_page(self, page, rows_per_page, fetch_time):
        """抓取并处理单个页面的数据"""
        try:
            # 获取页面数据
            rows_data, total = self.fetch_page_data_threadsafe(page, rows_per_page)
            
            if not rows_data:
                return 0
            
            # 处理页面数据
            processed_count = self.process_page_data_threadsafe(page, rows_data, fetch_time)
            
            return processed_count
            
        except Exception as e:
            print(f"[线程 {threading.current_thread().name}] 处理第 {page} 页时发生错误: {e}")
            return 0
    
    def stop_fetching(self):
        """停止数据抓取"""
        print("正在停止数据抓取...")
        self.stop_event.set()
    
    def get_fetching_stats(self):
        """获取抓取统计信息"""
        with self.stats_lock:
            return {
                'total_processed': self.total_processed,
                'total_failed': self.total_failed,
                'is_running': not self.stop_event.is_set()
            }
    
    def get_latest_prices(self, limit=50):
        """获取最新的价格数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM price_trend_view 
                ORDER BY fetch_time DESC 
                LIMIT ?
            ''', (limit,))
            
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
    
    def get_price_trends(self, service_item_id=None, method_id=None, days=30):
        """获取价格趋势数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            where_clause = "WHERE fetch_time >= datetime('now', '-{} days')".format(days)
            params = []
            
            if service_item_id:
                where_clause += " AND service_item_id = ?"
                params.append(service_item_id)
            
            if method_id:
                where_clause += " AND check_method_id = ?"
                params.append(method_id)
            
            query = f'''
                SELECT * FROM price_trend_view 
                {where_clause}
                ORDER BY fetch_time ASC
            '''
            
            cursor.execute(query, params)
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results

    def _update_price_change_cache(self):
        """更新价格变动分析缓存和统计数据缓存"""
        try:
            # 动态导入避免循环依赖
            from cache_manager import update_price_change_cache, update_statistics_cache
            from price_manager import PriceDataManager

            print("开始更新缓存数据...")

            # 创建价格管理器实例
            price_manager = PriceDataManager(self.db_path)

            # 在后台线程中更新缓存，避免阻塞数据抓取流程
            import threading
            def update_cache_background():
                try:
                    # 更新价格变动分析缓存
                    price_success = update_price_change_cache(price_manager, days=7)
                    if price_success:
                        print("✅ 价格变动分析缓存更新完成")
                    else:
                        print("❌ 价格变动分析缓存更新失败")

                    # 更新统计数据缓存
                    stats_success = update_statistics_cache(price_manager)
                    if stats_success:
                        print("✅ 统计数据缓存更新完成")
                    else:
                        print("❌ 统计数据缓存更新失败")

                    # 优先使用增量更新统计汇总表
                    from cache_manager import update_statistics_incrementally, update_statistics_summary_table

                    # 尝试增量更新
                    incremental_success = update_statistics_incrementally()
                    if incremental_success:
                        print("✅ 统计数据增量更新完成")
                    else:
                        print("❌ 增量更新失败，回退到全量更新")
                        # 回退到全量更新
                        summary_success = update_statistics_summary_table(price_manager)
                        if summary_success:
                            print("✅ 统计汇总表全量更新完成")
                        else:
                            print("❌ 统计汇总表全量更新失败")

                    if price_success and stats_success:
                        print("🎉 所有缓存更新完成")
                    else:
                        print("⚠️  部分缓存更新失败")

                except Exception as e:
                    print(f"更新缓存时出错: {e}")

            # 启动后台更新线程
            cache_thread = threading.Thread(target=update_cache_background, daemon=True)
            cache_thread.start()

        except ImportError:
            print("缓存管理模块未找到，跳过缓存更新")
        except Exception as e:
            print(f"触发缓存更新时出错: {e}")

    def _detect_and_record_method_changes(self, cursor, method_id, existing_data, new_data, new_last_date, fetch_time):
        """检测方法变动并记录到变动历史表"""
        try:
            # 解析现有数据
            (existing_deprecated, existing_last_date, existing_price, existing_lowest_bid,
             existing_cma, existing_cnas, existing_gov_agree, existing_status, existing_name) = existing_data

            # 获取当前会话ID
            session_id = getattr(self, 'current_session_id', None) or f"fetch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            changes_detected = []

            # 检测 last_date 变动
            if existing_last_date != new_last_date and new_last_date is not None:
                changes_detected.append({
                    'field_name': 'last_date',
                    'old_value': str(existing_last_date) if existing_last_date else None,
                    'new_value': str(new_last_date),
                    'change_type': 'method_update'
                })

            # 检测价格变动
            new_price = new_data.get('price')
            if existing_price != new_price and new_price is not None:
                changes_detected.append({
                    'field_name': 'price',
                    'old_value': str(existing_price) if existing_price else None,
                    'new_value': str(new_price),
                    'change_type': 'price_change'
                })

            # 检测最低价格变动
            new_lowest_bid = new_data.get('lowestBid')
            if existing_lowest_bid != new_lowest_bid and new_lowest_bid is not None:
                changes_detected.append({
                    'field_name': 'lowest_bid',
                    'old_value': str(existing_lowest_bid) if existing_lowest_bid else None,
                    'new_value': str(new_lowest_bid),
                    'change_type': 'price_change'
                })

            # 检测资质变动
            new_cma = new_data.get('cma', 0)
            if existing_cma != new_cma:
                changes_detected.append({
                    'field_name': 'cma',
                    'old_value': str(existing_cma) if existing_cma is not None else None,
                    'new_value': str(new_cma),
                    'change_type': 'qualification_change'
                })

            new_cnas = new_data.get('cnas', 0)
            if existing_cnas != new_cnas:
                changes_detected.append({
                    'field_name': 'cnas',
                    'old_value': str(existing_cnas) if existing_cnas is not None else None,
                    'new_value': str(new_cnas),
                    'change_type': 'qualification_change'
                })

            new_gov_agree = new_data.get('govAgree', 0)
            if existing_gov_agree != new_gov_agree:
                changes_detected.append({
                    'field_name': 'gov_agree',
                    'old_value': str(existing_gov_agree) if existing_gov_agree is not None else None,
                    'new_value': str(new_gov_agree),
                    'change_type': 'qualification_change'
                })

            # 检测状态变动
            new_status = new_data.get('status', 0)
            if existing_status != new_status:
                changes_detected.append({
                    'field_name': 'status',
                    'old_value': str(existing_status) if existing_status is not None else None,
                    'new_value': str(new_status),
                    'change_type': 'status_change'
                })

            # 检测名称变动
            new_name = new_data.get('name')
            if existing_name != new_name and new_name is not None:
                changes_detected.append({
                    'field_name': 'name',
                    'old_value': existing_name,
                    'new_value': new_name,
                    'change_type': 'name_change'
                })

            # 记录所有检测到的变动
            for change in changes_detected:
                # 使用 new_last_date 作为变动日期，如果没有则使用当前日期
                change_date = new_last_date.date() if new_last_date else fetch_time.date()

                cursor.execute('''
                    INSERT INTO method_change_history
                    (method_id, change_date, detected_time, change_type, field_name,
                     old_value, new_value, fetch_session_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    method_id,
                    change_date,
                    fetch_time,
                    change['change_type'],
                    change['field_name'],
                    change['old_value'],
                    change['new_value'],
                    session_id
                ))

                print(f"[CHANGE] 方法 {method_id} 的 {change['field_name']} 从 '{change['old_value']}' 变更为 '{change['new_value']}'")

            if changes_detected:
                print(f"[INFO] 检测到方法 {method_id} 有 {len(changes_detected)} 项变动")

        except Exception as e:
            print(f"[ERROR] 记录方法变动时出错: {e}")
            # 不抛出异常，避免影响正常的数据抓取流程


def main():
    """主函数示例"""
    # 配置多线程抓取器
    # max_workers: 最大并发线程数 (建议2-4个)
    # requests_per_second: 每秒最大请求数
    # max_requests_per_minute: 每分钟最大请求数
    fetcher = PriceDataFetcher(
        max_workers=3,
        requests_per_second=1,
        max_requests_per_minute=30
    )
    
    username = "15150417135"  # 替换为实际用户名
    password = "your_password"  # 替换为实际密码
    
    # 登录
    if fetcher.login_if_needed(username, password):
        print("登录成功，开始多线程抓取...")
        
        # 定义进度回调函数
        def progress_callback(current, total, message):
            print(f"进度: {current}/{total} - {message}")
        
        # 多线程抓取数据
        success = fetcher.fetch_all_data_multithreaded(
            progress_callback=progress_callback,
            rows_per_page=800
        )
        
        if success:
            print("多线程抓取完成！")
            
            # 获取统计信息
            stats = fetcher.get_fetching_stats()
            print(f"处理成功: {stats['total_processed']} 个项目")
            print(f"处理失败: {stats['total_failed']} 个项目")
            
            # 查看最新数据
            latest_data = fetcher.get_latest_prices(10)
            print("\n最新的10条价格记录:")
            for record in latest_data:
                print(f"{record['service_item_name']} - {record['check_method_name']}: ¥{record['check_method_price']}")
        else:
            print("多线程抓取失败")
    else:
        print("登录失败")


def test_rate_limiter():
    """测试频率限制器"""
    print("测试频率限制器...")
    rate_limiter = RateLimiter(requests_per_second=2, max_requests_per_minute=10)
    
    for i in range(15):
        print(f"请求 {i+1}")
        rate_limiter.wait_if_needed()
        print(f"请求 {i+1} 完成")


if __name__ == "__main__":
    # 可以选择运行主函数或测试函数
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_rate_limiter()
    else:
        main()