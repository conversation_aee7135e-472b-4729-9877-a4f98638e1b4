{% extends "base.html" %}

{% block title %}标准方法管理 - 康达价格管理系统{% endblock %}

{% block extra_css %}
<style>
/* 表格列分隔线样式 */
.column-divider th,
.column-divider td {
    border-right: 1px solid #dee2e6;
}

.column-divider th:last-child,
.column-divider td:last-child {
    border-right: none;
}

/* 表格行样式 */
.table-sm td {
    padding: 0.5rem 0.75rem;
    vertical-align: top;
}

/* 确保内容换行 */
.table td {
    word-wrap: break-word;
    word-break: break-all;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-book me-2"></i>标准方法管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="showAddMethodModal()">
                <i class="fas fa-plus me-1"></i>添加方法
            </button>
            <button type="button" class="btn btn-success" onclick="showImportModal()">
                <i class="fas fa-upload me-1"></i>Excel导入
            </button>
            <button type="button" class="btn btn-info" onclick="exportToExcel()">
                <i class="fas fa-download me-1"></i>Excel导出
            </button>
        </div>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-warning" onclick="showAliasModal()">
                <i class="fas fa-tags me-1"></i>别名管理
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-danger" onclick="showClearDataModal()">
                <i class="fas fa-trash me-1"></i>清空数据
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选区域 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>搜索筛选
        </h5>
    </div>
    <div class="card-body">
        <form id="searchForm" class="row g-3">
            <div class="col-md-3">
                <label for="keyword" class="form-label">关键词</label>
                <input type="text" class="form-control" id="keyword" placeholder="标准号、标准名称">
            </div>
            <div class="col-md-2">
                <label for="scopeFilter" class="form-label">适用范围</label>
                <select class="form-select" id="scopeFilter">
                    <option value="">全部</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="appendixFilter" class="form-label">附录方法</label>
                <select class="form-select" id="appendixFilter">
                    <option value="">全部</option>
                    <option value="1">是</option>
                    <option value="0">否</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="informativeFilter" class="form-label">资料性附录</label>
                <select class="form-select" id="informativeFilter">
                    <option value="">全部</option>
                    <option value="1">是</option>
                    <option value="0">否</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="itemFilter" class="form-label">检测项目</label>
                <input type="text" class="form-control" id="itemFilter" placeholder="输入检测项目名称">
                <div class="form-text">支持模糊匹配，如：铅、pH</div>
            </div>
            <div class="col-md-2">
                <label for="statusFilter" class="form-label">状态</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部</option>
                    <option value="0">有效</option>
                    <option value="1">废止</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 数据表格 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>标准方法列表
        </h5>
        <span id="totalCount" class="badge bg-primary">0</span>
    </div>
    <div class="card-body">
        <div id="loadingIndicator" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载数据...</p>
        </div>
        
        <div id="methodsTable" style="display: none;">
            <div class="table-responsive">
                <table class="table table-sm table-hover mb-0 column-divider" style="font-size: 0.95rem;">
                    <thead class="table-light">
                        <tr>
                            <th style="color: black;" width="15%">标准号</th>
                            <th style="color: black;" width="25%">标准名称</th>
                            <th style="color: black;" width="20%">适用范围</th>
                            <th style="color: black;" width="25%">检测项目</th>
                            <th style="color: black;" width="8%">状态</th>
                            <th style="color: black;" width="7%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="methodsTableBody">
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="分页导航">
                <ul class="pagination justify-content-center" id="pagination">
                </ul>
            </nav>
        </div>
        
        <div id="noDataMessage" class="text-center py-4" style="display: none;">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <p class="text-muted">暂无数据</p>
        </div>
    </div>
</div>

<!-- 添加/编辑方法模态框 -->
<div class="modal fade" id="methodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="methodModalTitle">添加标准方法</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="methodForm">
                    <input type="hidden" id="methodId">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="standardNumber" class="form-label">标准号 *</label>
                            <input type="text" class="form-control" id="standardNumber" required>
                        </div>
                        <div class="col-md-6">
                            <label for="standardYear" class="form-label">年份 *</label>
                            <input type="text" class="form-control" id="standardYear" required>
                        </div>
                        <div class="col-12">
                            <label for="standardName" class="form-label">标准名称 *</label>
                            <input type="text" class="form-control" id="standardName" required>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isAppendixMethod">
                                <label class="form-check-label" for="isAppendixMethod">
                                    是否为附录方法
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="appendixNumber" class="form-label">附录号</label>
                            <input type="text" class="form-control" id="appendixNumber" placeholder="如：附录A">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isInformativeAppendix">
                                <label class="form-check-label" for="isInformativeAppendix">
                                    是否属于资料性附录
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="methodStatus" class="form-label">状态</label>
                            <select class="form-select" id="methodStatus">
                                <option value="0">有效</option>
                                <option value="1">废止</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="applicableScopes" class="form-label">适用范围</label>
                            <select class="form-select" id="applicableScopes" multiple>
                            </select>
                            <div class="form-text">按住Ctrl键可选择多个范围</div>
                        </div>
                        <div class="col-12">
                            <label for="detectionItems" class="form-label">检测项目</label>
                            <textarea class="form-control" id="detectionItems" rows="3"
                                      placeholder="请输入检测项目，多个项目用顿号（、）分隔，如：pH、COD、铅、镉"></textarea>
                            <div class="form-text">多个检测项目请用顿号（、）分隔</div>
                        </div>
                        <div class="col-12">
                            <label for="methodRemark" class="form-label">备注</label>
                            <textarea class="form-control" id="methodRemark" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveMethod()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel导入模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Excel导入</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="excelFile" class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls">
                </div>
                <div class="alert alert-info">
                    <h6>Excel文件格式要求：</h6>
                    <ul class="mb-0">
                        <li>第1列：标准号</li>
                        <li>第2列：附录方法（空值表示否）</li>
                        <li>第3列：资料性附录（非附录方法时填写"/"）</li>
                        <li>第4列：标准名称</li>
                        <li>第5列：适用范围（多个范围用逗号分隔）</li>
                        <li>第6列：检测项目（需要识别和正确显示上下标格式）</li>
                    </ul>
                </div>
                <div id="importProgress" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="importStatus"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="importExcel()">开始导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 智能提示模态框 -->
<div class="modal fade" id="alternativesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">可用替代方法</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="alternativesContent">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 清空数据模态框 -->
<div class="modal fade" id="clearDataModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>清空标准方法数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-warning me-2"></i>危险操作警告</h6>
                    <p class="mb-2">此操作将清空所有标准方法相关数据，包括：</p>
                    <ul class="mb-2">
                        <li>所有标准方法记录</li>
                        <li>适用范围数据</li>
                        <li>检测项目数据</li>
                        <li>评价方法数据</li>
                        <li>所有关联关系</li>
                    </ul>
                    <p class="mb-0"><strong>此操作不可逆转，请谨慎操作！</strong></p>
                </div>

                <div id="clearDataStats" class="mb-3">
                    <h6>当前数据统计：</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">标准方法：<span id="methodCount">-</span> 条</small><br>
                            <small class="text-muted">适用范围：<span id="scopeCount">-</span> 条</small><br>
                            <small class="text-muted">检测项目：<span id="itemCount">-</span> 条</small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">评价方法：<span id="evaluationCount">-</span> 条</small><br>
                            <small class="text-muted">关联关系：<span id="relationCount">-</span> 条</small>
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="confirmClear">
                    <label class="form-check-label text-danger" for="confirmClear">
                        我确认要清空所有标准方法数据，并了解此操作不可逆转
                    </label>
                </div>

                <div class="mb-3">
                    <label for="clearConfirmText" class="form-label">请输入 "清空数据" 确认操作：</label>
                    <input type="text" class="form-control" id="clearConfirmText" placeholder="请输入：清空数据">
                </div>

                <div id="clearProgress" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="clearStatus"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="executeClearData()" id="clearDataBtn" disabled>
                    <i class="fas fa-trash me-1"></i>确认清空
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 别名管理模态框 -->
<div class="modal fade" id="aliasModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tags me-2"></i>检测项目别名管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 搜索区域 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="aliasSearchKeyword"
                                   placeholder="搜索检测项目名称或别名...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchDetectionItems()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-success" onclick="showAddAliasForm()">
                            <i class="fas fa-plus me-1"></i>添加别名
                        </button>
                    </div>
                </div>

                <!-- 检测项目列表 -->
                <div id="detectionItemsList">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载检测项目...</p>
                    </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="检测项目分页" id="aliasModalPagination" style="display: none;">
                    <ul class="pagination justify-content-center" id="aliasModalPaginationList">
                    </ul>
                </nav>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑别名模态框 -->
<div class="modal fade" id="editAliasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAliasModalTitle">编辑别名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAliasForm">
                    <input type="hidden" id="editAliasItemId">

                    <div class="mb-3">
                        <label class="form-label">检测项目</label>
                        <div class="form-control-plaintext" id="editAliasItemName"></div>
                    </div>

                    <div class="mb-3">
                        <label for="editAliasInput" class="form-label">别名设置</label>
                        <div id="aliasInputContainer">
                            <!-- 动态生成的别名输入框 -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addAliasInput()">
                            <i class="fas fa-plus me-1"></i>添加别名
                        </button>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showAliasesInList">
                            <label class="form-check-label" for="showAliasesInList">
                                在标准方法管理界面的检测项目列表中显示别名
                            </label>
                        </div>
                        <div class="form-text">
                            勾选后，别名将在标准方法管理界面中与主名称一起显示
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAliases()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 别名管理模态框 -->
<div class="modal fade" id="aliasModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tags me-2"></i>检测项目别名管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 搜索区域 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="aliasSearchKeyword"
                                   placeholder="搜索检测项目名称或别名...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchDetectionItems()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 检测项目列表 -->
                <div id="detectionItemsList">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载检测项目...</p>
                    </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="检测项目分页" id="aliasModalPagination" style="display: none;">
                    <ul class="pagination justify-content-center" id="aliasModalPaginationList">
                    </ul>
                </nav>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑别名模态框 -->
<div class="modal fade" id="editAliasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAliasModalTitle">编辑别名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAliasForm">
                    <input type="hidden" id="editAliasItemId">

                    <div class="mb-3">
                        <label class="form-label">检测项目</label>
                        <div class="form-control-plaintext" id="editAliasItemName"></div>
                    </div>

                    <div class="mb-3">
                        <label for="editAliasInput" class="form-label">别名设置</label>
                        <div id="aliasInputContainer">
                            <!-- 动态生成的别名输入框 -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="addAliasInput()">
                            <i class="fas fa-plus me-1"></i>添加别名
                        </button>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showAliasesInList">
                            <label class="form-check-label" for="showAliasesInList">
                                在标准方法管理界面的检测项目列表中显示别名
                            </label>
                        </div>
                        <div class="form-text">
                            勾选后，别名将在标准方法管理界面中与主名称一起显示
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAliases()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentFilters = {};
let allScopes = [];

$(document).ready(function() {
    // 加载初始数据
    loadScopes();
    loadMethods();
    
    // 绑定搜索表单
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadMethods();
    });
    
    // 绑定附录方法复选框事件
    $('#isAppendixMethod').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('#appendixNumber').prop('disabled', !isChecked);
        $('#isInformativeAppendix').prop('disabled', !isChecked);
        if (!isChecked) {
            $('#appendixNumber').val('');
            $('#isInformativeAppendix').prop('checked', false);
        }
    });

    // 绑定清空数据确认事件
    $('#confirmClear, #clearConfirmText').on('change input', function() {
        const isChecked = $('#confirmClear').is(':checked');
        const textConfirm = $('#clearConfirmText').val() === '清空数据';
        $('#clearDataBtn').prop('disabled', !(isChecked && textConfirm));
    });
});

// 加载适用范围
function loadScopes() {
    apiRequest('/api/standard_methods/scopes')
        .then(data => {
            if (data.success) {
                allScopes = data.data;
                updateScopeSelects();
            }
        });
}

// 更新适用范围选择框
function updateScopeSelects() {
    const filterSelect = $('#scopeFilter');
    const modalSelect = $('#applicableScopes');
    
    filterSelect.empty().append('<option value="">全部</option>');
    modalSelect.empty();
    
    allScopes.forEach(scope => {
        filterSelect.append(`<option value="${scope.id}">${scope.name}</option>`);
        modalSelect.append(`<option value="${scope.id}">${scope.name}</option>`);
    });
}

// 加载标准方法列表
function loadMethods() {
    $('#loadingIndicator').show();
    $('#methodsTable').hide();
    $('#noDataMessage').hide();

    // 获取筛选条件
    currentFilters = {
        keyword: $('#keyword').val(),
        scope_id: $('#scopeFilter').val(),
        item_name: $('#itemFilter').val(), // 改为检测项目名称
        is_appendix: $('#appendixFilter').val(),
        is_informative: $('#informativeFilter').val(),
        status: $('#statusFilter').val(),
        page: currentPage,
        per_page: 20
    };

    const params = new URLSearchParams();
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key]) {
            params.append(key, currentFilters[key]);
        }
    });

    apiRequest(`/api/standard_methods?${params.toString()}`)
        .then(data => {
            $('#loadingIndicator').hide();

            if (data.success) {
                displayMethods(data.data);
            } else {
                showError('methodsTable', data.message);
            }
        })
        .catch(error => {
            $('#loadingIndicator').hide();
            showError('methodsTable', '加载数据失败');
        });
}

// 生成检测项目HTML，支持智能提示
function generateDetectionItemsHtml(method) {
    if (!method.detection_items) {
        return '-';
    }

    // 如果不是资料性附录或没有替代方法，普通显示
    if (!method.is_informative_appendix || !method.has_alternatives || !method.item_alternatives) {
        return method.detection_items;
    }

    // 按检测项目分别处理
    const items = method.detection_items.split('、').map(item => item.trim());
    const processedItems = items.map(item => {
        // 检查该项目是否有替代方法（新的数据结构是按适用范围分组的）
        if (method.item_alternatives[item] && Object.keys(method.item_alternatives[item]).length > 0) {
            const tooltipContent = generateItemTooltipContent(item, method.item_alternatives[item]);
            const tooltipId = `tooltip-${method.id}-${item.replace(/[^a-zA-Z0-9]/g, '')}`;

            return `<span style="color: red; cursor: help;"
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          data-bs-html="true"
                          data-bs-title="${tooltipContent}"
                          id="${tooltipId}">
                        ${item}
                    </span>`;
        } else {
            // 没有替代方法，正常显示
            return item;
        }
    });

    return processedItems.join('、');
}

// 生成单个检测项目的悬浮提示内容
function generateItemTooltipContent(itemName, scopeGroupedAlternatives) {
    if (!scopeGroupedAlternatives || Object.keys(scopeGroupedAlternatives).length === 0) {
        return `暂无 ${itemName} 的替代方法`;
    }

    let content = `<div class="text-start"><strong>${itemName} 的替代方法：</strong><br><br>`;

    // 按适用范围分组显示
    Object.keys(scopeGroupedAlternatives).forEach(scopeName => {
        const alternatives = scopeGroupedAlternatives[scopeName];

        content += `<div class="mb-2">`;
        content += `<small class="text-muted fw-bold">${scopeName}：</small><br>`;

        // 去重相同的方法
        const uniqueMethods = {};
        alternatives.forEach(alt => {
            const key = alt.full_standard_number;
            if (!uniqueMethods[key]) {
                uniqueMethods[key] = alt;
            }
        });

        Object.values(uniqueMethods).forEach(alt => {
            const methodType = alt.is_informative_appendix ?
                '<span class="badge badge-sm bg-warning text-dark">资料性</span>' :
                '<span class="badge badge-sm bg-success">规范性</span>';
            content += `<small>• ${alt.full_standard_number} ${methodType}<br>`;
            content += `&nbsp;&nbsp;${alt.standard_name.substring(0, 35)}${alt.standard_name.length > 35 ? '...' : ''}</small><br>`;
            if (alt.matched_item_display && alt.matched_item_display !== itemName) {
                content += `&nbsp;&nbsp;<em class="text-muted">匹配: ${alt.matched_item_display}</em><br>`;
            }
        });
        content += '</div>';
    });

    content += '</div>';
    return content.replace(/"/g, '&quot;');
}

// 生成悬浮提示内容（保留兼容性）
function generateTooltipContent(alternativesInfo) {
    if (!alternativesInfo || alternativesInfo.length === 0) {
        return '暂无替代方法';
    }

    let content = '<div class="text-start"><strong>可用替代方法：</strong><br>';

    // 按适用范围分组
    const groupedByScope = {};
    alternativesInfo.forEach(alt => {
        if (!groupedByScope[alt.scope_name]) {
            groupedByScope[alt.scope_name] = [];
        }
        groupedByScope[alt.scope_name].push(alt);
    });

    Object.keys(groupedByScope).forEach(scopeName => {
        content += `<div class="mb-1"><small class="text-muted">${scopeName}：</small><br>`;
        groupedByScope[scopeName].forEach(alt => {
            const methodType = alt.is_informative_appendix ?
                '<span class="badge badge-sm bg-warning text-dark">资料性</span>' :
                '<span class="badge badge-sm bg-success">规范性</span>';
            content += `<small>• ${alt.full_standard_number} ${methodType}<br>`;
            content += `&nbsp;&nbsp;${alt.standard_name}</small><br>`;
        });
        content += '</div>';
    });

    content += '</div>';
    return content.replace(/"/g, '&quot;');
}

// 显示标准方法列表
function displayMethods(data) {
    const tbody = $('#methodsTableBody');
    tbody.empty();

    $('#totalCount').text(data.total);

    if (data.methods.length === 0) {
        $('#noDataMessage').show();
        return;
    }

    $('#methodsTable').show();

    data.methods.forEach(method => {
        // 构建标准号显示内容
        let standardNumberHtml = `<strong>${method.full_standard_number}</strong>`;

        // 如果是附录方法，添加附录信息并根据资料性附录设置颜色
        if (method.is_appendix_method && method.appendix_number) {
            const appendixColor = method.is_informative_appendix ? '#856404' : '#198754'; // 暗黄色 : 绿色
            standardNumberHtml += `<br><small style="color: ${appendixColor};">${method.appendix_number}</small>`;

            // 如果是资料性附录且有替代方法，显示提示
            if (method.is_informative_appendix && method.has_alternatives) {
                standardNumberHtml += `<br><small class="text-danger cursor-pointer" onclick="showAlternatives(${method.id})" title="点击查看替代方法">
                    <i class="fas fa-exclamation-triangle"></i> 有新方法
                </small>`;
            }
        }

        const row = $(`
            <tr>
                <td style="vertical-align: top;">
                    ${standardNumberHtml}
                </td>
                <td style="vertical-align: top; word-wrap: break-word;">
                    ${method.standard_name}
                </td>
                <td style="vertical-align: top; word-wrap: break-word;">
                    ${method.applicable_scopes || '-'}
                </td>
                <td style="vertical-align: top; word-wrap: break-word;">
                    ${generateDetectionItemsHtml(method)}
                </td>
                <td style="vertical-align: top; text-align: center;">
                    <span class="badge ${method.status === 0 ? 'bg-success' : 'bg-danger'}">
                        ${method.status === 0 ? '有效' : '废止'}
                    </span>
                </td>
                <td style="vertical-align: top; text-align: center;">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" onclick="editMethod(${method.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteMethod(${method.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });

    // 更新分页
    updatePagination(data);

    // 初始化Bootstrap tooltips
    initializeTooltips();
}

// 初始化悬浮提示
function initializeTooltips() {
    // 销毁现有的tooltips
    $('[data-bs-toggle="tooltip"]').tooltip('dispose');

    // 重新初始化tooltips
    $('[data-bs-toggle="tooltip"]').tooltip({
        html: true,
        trigger: 'hover focus',
        delay: { show: 300, hide: 100 }
    });
}

// 更新分页
function updatePagination(data) {
    const pagination = $('#pagination');
    pagination.empty();

    const totalPages = Math.ceil(data.total / data.per_page);

    if (totalPages <= 1) return;

    // 上一页
    if (data.page > 1) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${data.page - 1})">上一页</a>
            </li>
        `);
    }

    // 页码
    const startPage = Math.max(1, data.page - 2);
    const endPage = Math.min(totalPages, data.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        pagination.append(`
            <li class="page-item ${i === data.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    if (data.page < totalPages) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${data.page + 1})">下一页</a>
            </li>
        `);
    }
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadMethods();
}

// 显示添加方法模态框
function showAddMethodModal() {
    $('#methodModalTitle').text('添加标准方法');
    $('#methodForm')[0].reset();
    $('#methodId').val('');
    $('#isAppendixMethod').trigger('change');
    $('#methodModal').modal('show');
}

// 编辑方法
function editMethod(methodId) {
    apiRequest(`/api/standard_methods/${methodId}`)
        .then(data => {
            if (data.success) {
                const method = data.data;
                $('#methodModalTitle').text('编辑标准方法');
                $('#methodId').val(method.id);
                $('#standardNumber').val(method.standard_number);
                $('#standardYear').val(method.standard_year);
                $('#standardName').val(method.standard_name);
                $('#isAppendixMethod').prop('checked', method.is_appendix_method);
                $('#appendixNumber').val(method.appendix_number || '');
                $('#isInformativeAppendix').prop('checked', method.is_informative_appendix);
                $('#methodStatus').val(method.status);
                $('#methodRemark').val(method.remark || '');

                // 设置适用范围和检测项目
                if (method.applicable_scope_ids) {
                    $('#applicableScopes').val(method.applicable_scope_ids);
                }
                // 设置检测项目文本（从detection_items字段获取）
                if (method.detection_items) {
                    $('#detectionItems').val(method.detection_items);
                } else {
                    $('#detectionItems').val('');
                }

                $('#isAppendixMethod').trigger('change');
                $('#methodModal').modal('show');
            } else {
                alert('获取方法信息失败：' + data.message);
            }
        });
}

// 保存方法
function saveMethod() {
    const formData = {
        standard_number: $('#standardNumber').val(),
        standard_year: $('#standardYear').val(),
        standard_name: $('#standardName').val(),
        is_appendix_method: $('#isAppendixMethod').is(':checked'),
        appendix_number: $('#appendixNumber').val(),
        is_informative_appendix: $('#isInformativeAppendix').is(':checked'),
        status: parseInt($('#methodStatus').val()),
        remark: $('#methodRemark').val(),
        applicable_scopes: $('#applicableScopes').val() || [],
        detection_items_text: $('#detectionItems').val() || ''
    };

    const methodId = $('#methodId').val();
    const url = methodId ? `/api/standard_methods/${methodId}` : '/api/standard_methods';
    const method = methodId ? 'PUT' : 'POST';

    apiRequest(url, {
        method: method,
        body: JSON.stringify(formData)
    })
    .then(data => {
        if (data.success) {
            $('#methodModal').modal('hide');
            loadMethods();
            alert(data.message);
        } else {
            alert('保存失败：' + data.message);
        }
    });
}

// 删除方法
function deleteMethod(methodId) {
    if (confirm('确定要删除这个标准方法吗？此操作不可恢复。')) {
        apiRequest(`/api/standard_methods/${methodId}`, {
            method: 'DELETE'
        })
        .then(data => {
            if (data.success) {
                loadMethods();
                alert(data.message);
            } else {
                alert('删除失败：' + data.message);
            }
        });
    }
}

// 显示导入模态框
function showImportModal() {
    $('#importModal').modal('show');
}

// Excel导入
function importExcel() {
    const fileInput = $('#excelFile')[0];
    if (!fileInput.files.length) {
        alert('请选择Excel文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);

    $('#importProgress').show();
    $('.progress-bar').css('width', '0%');
    $('#importStatus').text('正在上传文件...');

    fetch('/api/standard_methods/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('.progress-bar').css('width', '100%');
            $('#importStatus').html(`
                <div class="alert alert-success">
                    ${data.message}<br>
                    成功：${data.data.success_count}条，失败：${data.data.error_count}条
                </div>
            `);
            setTimeout(() => {
                $('#importModal').modal('hide');
                loadMethods();
            }, 2000);
        } else {
            $('#importStatus').html(`
                <div class="alert alert-danger">
                    导入失败：${data.message}
                </div>
            `);
        }
    })
    .catch(error => {
        $('#importStatus').html(`
            <div class="alert alert-danger">
                导入失败：网络错误
            </div>
        `);
    });
}

// Excel导出
function exportToExcel() {
    // 显示加载提示
    const loadingToast = $(`
        <div class="toast position-fixed top-0 end-0 m-3" style="z-index: 9999;">
            <div class="toast-body bg-info text-white">
                <i class="fas fa-spinner fa-spin me-2"></i>正在导出Excel文件，请稍候...
            </div>
        </div>
    `);
    
    $('body').append(loadingToast);
    loadingToast.toast({ delay: 5000 }).toast('show');
    
    // 创建下载链接
    const downloadUrl = '/api/standard_methods/export';
    
    // 创建临时下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 延迟隐藏加载提示
    setTimeout(() => {
        loadingToast.toast('hide');
        setTimeout(() => loadingToast.remove(), 500);
    }, 2000);
}

// 显示替代方法
function showAlternatives(methodId) {
    apiRequest(`/api/standard_methods/${methodId}/alternatives`)
        .then(data => {
            if (data.success) {
                const alternatives = data.data;
                let content = '<div class="list-group">';

                if (alternatives.length === 0) {
                    content += '<p class="text-muted">暂无替代方法</p>';
                } else {
                    alternatives.forEach(alt => {
                        content += `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${alt.new_method_number}</h6>
                                    <small class="text-muted">${alt.effective_date || ''}</small>
                                </div>
                                <p class="mb-1">${alt.new_method_name}</p>
                                <small class="text-muted">
                                    适用范围：${alt.scope_name} | 检测项目：${alt.item_name}
                                    ${alt.remark ? `<br>备注：${alt.remark}` : ''}
                                </small>
                            </div>
                        `;
                    });
                }

                content += '</div>';
                $('#alternativesContent').html(content);
                $('#alternativesModal').modal('show');
            }
        });
}

// 显示清空数据模态框
function showClearDataModal() {
    // 重置表单
    $('#confirmClear').prop('checked', false);
    $('#clearConfirmText').val('');
    $('#clearDataBtn').prop('disabled', true);
    $('#clearProgress').hide();

    // 加载数据统计
    loadDataStats();

    $('#clearDataModal').modal('show');
}

// 加载数据统计
function loadDataStats() {
    // 获取标准方法数量
    apiRequest('/api/standard_methods?per_page=1')
        .then(data => {
            if (data.success) {
                $('#methodCount').text(data.data.total || 0);
            }
        });

    // 获取适用范围数量
    apiRequest('/api/standard_methods/scopes')
        .then(data => {
            if (data.success) {
                $('#scopeCount').text(data.data.length || 0);
            }
        });

    // 获取检测项目数量
    apiRequest('/api/standard_methods/items')
        .then(data => {
            if (data.success) {
                $('#itemCount').text(data.data.length || 0);
            }
        });

    // 获取评价方法数量
    apiRequest('/api/evaluation_methods?per_page=1')
        .then(data => {
            if (data.success) {
                $('#evaluationCount').text(data.data.total || 0);
            }
        });

    // 计算关联关系数量（简化显示）
    $('#relationCount').text('计算中...');
    setTimeout(() => {
        const methodCount = parseInt($('#methodCount').text()) || 0;
        const scopeCount = parseInt($('#scopeCount').text()) || 0;
        const itemCount = parseInt($('#itemCount').text()) || 0;
        const estimatedRelations = methodCount * 2; // 估算关联数量
        $('#relationCount').text(estimatedRelations);
    }, 1000);
}

// 执行清空数据
function executeClearData() {
    const isChecked = $('#confirmClear').is(':checked');
    const textConfirm = $('#clearConfirmText').val() === '清空数据';

    if (!isChecked || !textConfirm) {
        alert('请确认所有必要的选项和输入');
        return;
    }

    // 显示进度
    $('#clearProgress').show();
    $('.progress-bar').css('width', '10%');
    $('#clearStatus').html('<div class="text-info">正在清空数据，请稍候...</div>');
    $('#clearDataBtn').prop('disabled', true);

    // 执行清空操作
    apiRequest('/api/standard_methods/clear', {
        method: 'POST',
        body: JSON.stringify({
            confirm: true,
            timestamp: new Date().getTime()
        })
    })
    .then(data => {
        $('.progress-bar').css('width', '100%');

        if (data.success) {
            $('#clearStatus').html(`
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>数据清空成功！
                    <br><small>${data.message}</small>
                </div>
            `);

            // 延迟关闭模态框并刷新页面
            setTimeout(() => {
                $('#clearDataModal').modal('hide');
                loadMethods(); // 刷新方法列表
                alert('标准方法数据已清空完成！');
            }, 2000);
        } else {
            $('#clearStatus').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>清空失败：${data.message}
                </div>
            `);
            $('#clearDataBtn').prop('disabled', false);
        }
    })
    .catch(error => {
        $('.progress-bar').css('width', '100%').addClass('bg-danger');
        $('#clearStatus').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>清空失败：网络错误
            </div>
        `);
        $('#clearDataBtn').prop('disabled', false);
    });
}

// ==================== 别名管理功能 ====================

let currentAliasPage = 1;
let currentAliasKeyword = '';

// 显示别名管理模态框
function showAliasModal() {
    $('#aliasModal').modal('show');
    currentAliasPage = 1;
    currentAliasKeyword = '';
    loadDetectionItems();
}

// 加载检测项目列表
function loadDetectionItems() {
    const params = new URLSearchParams({
        keyword: currentAliasKeyword,
        page: currentAliasPage,
        per_page: 10
    });

    apiRequest(`/api/detection_items/search?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayDetectionItems(data.data);
            } else {
                $('#detectionItemsList').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>${data.message}
                    </div>
                `);
            }
        })
        .catch(error => {
            $('#detectionItemsList').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>加载失败：网络错误
                </div>
            `);
        });
}

// 显示检测项目列表
function displayDetectionItems(data) {
    const container = $('#detectionItemsList');

    if (data.items.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无检测项目</p>
            </div>
        `);
        $('#aliasModalPagination').hide();
        return;
    }

    let html = '<div class="list-group">';

    data.items.forEach(item => {
        const aliasCount = item.aliases ? item.aliases.length : 0;
        const aliasText = aliasCount > 0 ? item.aliases.join(', ') : '暂无别名';
        const showInListBadge = item.show_aliases_in_list ?
            '<span class="badge bg-info ms-2">显示</span>' : '';

        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            ${item.display_name || item.name}
                            ${showInListBadge}
                        </h6>
                        <p class="mb-1 text-muted">
                            <small>别名 (${aliasCount}个): ${aliasText}</small>
                        </p>
                        ${item.chemical_formula ? `<small class="text-muted">化学式: ${item.chemical_formula}</small>` : ''}
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="editItemAliases(${item.id})">
                            <i class="fas fa-edit me-1"></i>编辑别名
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.html(html);

    // 显示分页
    displayAliasModalPagination(data);
}

// 显示分页
function displayAliasModalPagination(data) {
    const pagination = $('#aliasModalPaginationList');
    pagination.empty();

    if (data.total <= data.per_page) {
        $('#aliasModalPagination').hide();
        return;
    }

    $('#aliasModalPagination').show();

    const totalPages = Math.ceil(data.total / data.per_page);
    const currentPage = data.page;

    // 上一页
    if (currentPage > 1) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeAliasPage(${currentPage - 1})">上一页</a>
            </li>
        `);
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="changeAliasPage(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    if (currentPage < totalPages) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeAliasPage(${currentPage + 1})">下一页</a>
            </li>
        `);
    }
}

// 切换页面
function changeAliasPage(page) {
    currentAliasPage = page;
    loadDetectionItems();
}

// 搜索检测项目
function searchDetectionItems() {
    currentAliasKeyword = $('#aliasSearchKeyword').val();
    currentAliasPage = 1;
    loadDetectionItems();
}

// 编辑项目别名
function editItemAliases(itemId) {
    apiRequest(`/api/detection_items/${itemId}/aliases`)
        .then(data => {
            if (data.success) {
                showEditAliasModal(data.data);
            } else {
                alert('获取别名信息失败：' + data.message);
            }
        })
        .catch(error => {
            alert('获取别名信息失败：网络错误');
        });
}

// 显示编辑别名模态框
function showEditAliasModal(itemData) {
    $('#editAliasItemId').val(itemData.id);
    $('#editAliasItemName').text(itemData.display_name || itemData.name);
    $('#showAliasesInList').prop('checked', itemData.show_aliases_in_list);

    // 清空别名输入容器
    $('#aliasInputContainer').empty();

    // 添加现有别名
    if (itemData.aliases && itemData.aliases.length > 0) {
        itemData.aliases.forEach(alias => {
            addAliasInput(alias);
        });
    } else {
        // 如果没有别名，添加一个空的输入框
        addAliasInput();
    }

    $('#editAliasModal').modal('show');
}

// 添加别名输入框
function addAliasInput(value = '') {
    const container = $('#aliasInputContainer');
    const index = container.children().length;

    const inputHtml = `
        <div class="input-group mb-2" data-alias-index="${index}">
            <input type="text" class="form-control alias-input" value="${value}"
                   placeholder="输入别名...">
            <button class="btn btn-outline-danger" type="button" onclick="removeAliasInput(${index})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    container.append(inputHtml);
}

// 移除别名输入框
function removeAliasInput(index) {
    $(`[data-alias-index="${index}"]`).remove();
}

// 保存别名
function saveAliases() {
    const itemId = $('#editAliasItemId').val();
    const showInList = $('#showAliasesInList').is(':checked');

    // 收集所有别名
    const aliases = [];
    $('.alias-input').each(function() {
        const value = $(this).val().trim();
        if (value && !aliases.includes(value)) {
            aliases.push(value);
        }
    });

    const data = {
        aliases: aliases,
        show_in_list: showInList
    };

    apiRequest(`/api/detection_items/${itemId}/aliases`, {
        method: 'PUT',
        body: JSON.stringify(data)
    })
    .then(response => {
        if (response.success) {
            $('#editAliasModal').modal('hide');
            loadDetectionItems(); // 重新加载列表
            alert('别名保存成功！');
        } else {
            alert('保存失败：' + response.message);
        }
    })
    .catch(error => {
        alert('保存失败：网络错误');
    });
}

// 绑定搜索框回车事件
$(document).ready(function() {
    $('#aliasSearchKeyword').on('keypress', function(e) {
        if (e.which === 13) {
            searchDetectionItems();
        }
    });
});
</script>
{% endblock %}
