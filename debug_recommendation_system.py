#!/usr/bin/env python3
"""
调试智能推荐系统脚本
检查数据库中的数据和推荐逻辑
"""

import sqlite3
import json
from standard_methods_manager import StandardMethodsManager

def debug_recommendation_system():
    """调试推荐系统"""
    print("="*60)
    print("智能推荐系统调试")
    print("="*60)
    
    # 初始化管理器
    manager = StandardMethodsManager()
    
    # 1. 检查评价方法数据
    print("\n1. 检查评价方法数据:")
    with manager.get_connection() as conn:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT em.id, em.name, aps.name as scope_name, em.is_active
            FROM evaluation_methods em
            JOIN applicable_scopes aps ON em.applicable_scope_id = aps.id
            ORDER BY em.id
        """)
        
        methods = cursor.fetchall()
        print(f"找到 {len(methods)} 个评价方法:")
        for method in methods:
            print(f"  ID: {method[0]}, 名称: {method[1]}, 适用范围: {method[2]}, 活跃: {method[3]}")
    
    # 2. 检查推荐配置数据
    print("\n2. 检查推荐配置数据:")
    with manager.get_connection() as conn:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT er.id, er.evaluation_method_id, 
                   COALESCE(di.name, er.custom_detection_item_name) as item_name,
                   sm.full_standard_number, er.priority, er.reason
            FROM evaluation_recommendations er
            LEFT JOIN detection_items di ON er.detection_item_id = di.id
            LEFT JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id
            ORDER BY er.evaluation_method_id, er.id
            LIMIT 10
        """)
        
        recommendations = cursor.fetchall()
        print(f"找到 {len(recommendations)} 条推荐配置（显示前10条）:")
        for rec in recommendations:
            print(f"  推荐ID: {rec[0]}, 评价方法ID: {rec[1]}, 检测项目: {rec[2]}, 标准方法: {rec[3]}, 优先级: {rec[4]}")
    
    # 3. 检查检测项目数据
    print("\n3. 检查检测项目数据:")
    with manager.get_connection() as conn:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, display_name, aliases
            FROM detection_items
            WHERE name IN ('镉', '铅', '汞', '铬', '砷', 'Cd', 'Pb', 'Hg', 'Cr', 'As')
            ORDER BY name
        """)
        
        items = cursor.fetchall()
        print(f"找到 {len(items)} 个相关检测项目:")
        for item in items:
            aliases = item[3] if item[3] else "无"
            print(f"  ID: {item[0]}, 名称: {item[1]}, 显示名: {item[2]}, 别名: {aliases}")
    
    # 4. 测试推荐逻辑
    print("\n4. 测试推荐逻辑:")
    
    # 获取第一个评价方法进行测试
    if methods:
        test_method_id = methods[0][0]
        test_scope_id = None
        
        # 获取该评价方法的适用范围
        with manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT applicable_scope_id FROM evaluation_methods WHERE id = ?
            """, (test_method_id,))
            result = cursor.fetchone()
            if result:
                test_scope_id = result[0]
        
        if test_scope_id:
            print(f"使用评价方法ID: {test_method_id}, 适用范围ID: {test_scope_id}")
            
            # 测试不同的检测项目
            test_items = ['镉', 'Cd', '铅', 'Pb', '汞']
            
            for item in test_items:
                print(f"\n  测试检测项目: '{item}'")
                
                # 调用查找推荐配置方法
                recommendations = manager._find_evaluation_recommendations(test_method_id, item)
                print(f"    找到 {len(recommendations)} 条推荐配置")
                
                for i, rec in enumerate(recommendations[:3]):  # 只显示前3条
                    print(f"      {i+1}. 方法ID: {rec.get('method_id')}, 标准号: {rec.get('full_standard_number')}, 匹配类型: {rec.get('match_type')}")
    
    # 5. 检查标准化名称功能
    print("\n5. 检查标准化名称功能:")
    test_names = ['镉', 'Cd', '铅', 'Pb']
    for name in test_names:
        normalized = manager._normalize_item_name(name)
        print(f"  '{name}' -> {normalized}")
    
    # 6. 完整推荐测试 - 使用有推荐配置的评价方法
    print("\n6. 完整推荐测试:")

    # 使用评价方法ID 10（GB 5085.3-2007），它有推荐配置
    active_method_id = 10
    active_scope_id = None

    # 获取该评价方法的适用范围
    with manager.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT applicable_scope_id FROM evaluation_methods WHERE id = ?
        """, (active_method_id,))
        result = cursor.fetchone()
        if result:
            active_scope_id = result[0]

    if active_scope_id:
        test_text = "镉、铅、汞"
        print(f"使用评价方法ID: {active_method_id}, 适用范围ID: {active_scope_id}")
        print(f"测试输入: '{test_text}'")

        result = manager.get_evaluation_based_recommendations(
            active_method_id, active_scope_id, test_text
        )

        print(f"推荐结果:")
        print(f"  成功: {result.get('success')}")
        if result.get('success'):
            data = result.get('data', {})
            print(f"  匹配项目: {data.get('matched_items', [])}")
            print(f"  未匹配项目: {data.get('unmatched_items', [])}")
            print(f"  推荐方案数量: {data.get('total_schemes', 0)}")

            # 显示推荐方案详情
            schemes = data.get('recommended_schemes', [])
            for i, scheme in enumerate(schemes[:3]):  # 只显示前3个
                print(f"    方案{i+1}: {scheme.get('full_standard_number')} - 覆盖{scheme.get('covered_items_count')}个项目")
        else:
            print(f"  错误信息: {result.get('message')}")

    # 7. 测试具体的检测项目查找
    print("\n7. 测试具体的检测项目查找:")
    test_items = ['镉', '铜', '锌']  # 根据调试结果，这些项目有推荐配置

    for item in test_items:
        print(f"\n  测试检测项目: '{item}' (评价方法ID: {active_method_id})")
        recommendations = manager._find_evaluation_recommendations(active_method_id, item)
        print(f"    找到 {len(recommendations)} 条推荐配置")

        for i, rec in enumerate(recommendations[:2]):  # 只显示前2条
            method_id = rec.get('method_id')
            print(f"      {i+1}. 方法ID: {method_id}, 标准号: {rec.get('full_standard_number')}, 匹配类型: {rec.get('match_type')}")

            # 测试可用性检查
            is_available = manager._check_method_availability(method_id, active_scope_id, item)
            print(f"          可用性检查: {is_available}")

            # 获取方法详细信息
            with manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT is_informative_appendix, standard_year, status
                    FROM standard_methods WHERE id = ?
                """, (method_id,))
                method_detail = cursor.fetchone()
                if method_detail:
                    print(f"          方法详情: 资料性附录={method_detail[0]}, 年份={method_detail[1]}, 状态={method_detail[2]}")

                    # 如果是资料性附录方法，查看是否有更新的方法
                    if method_detail[0]:  # 是资料性附录方法
                        normalized_names = manager._normalize_item_name(item)
                        placeholders = ','.join(['?'] * len(normalized_names))

                        cursor.execute(f"""
                            SELECT sm.id, sm.full_standard_number, sm.is_informative_appendix, sm.standard_year
                            FROM standard_methods sm
                            JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
                            JOIN standard_method_items smi ON sm.id = smi.standard_method_id
                            JOIN detection_items di ON smi.detection_item_id = di.id
                            WHERE sms.applicable_scope_id = ?
                              AND sm.id != ?
                              AND sm.status = 0
                              AND (di.name IN ({placeholders}) OR di.display_name IN ({placeholders}))
                              AND (
                                  sm.is_informative_appendix = 0
                                  OR (sm.is_informative_appendix = 1 AND CAST(sm.standard_year AS INTEGER) > ?)
                              )
                        """, [active_scope_id, method_id] + normalized_names + normalized_names + [int(method_detail[1]) if method_detail[1] and method_detail[1].isdigit() else 0])

                        newer_methods = cursor.fetchall()
                        print(f"          找到 {len(newer_methods)} 个更新的方法:")
                        for newer in newer_methods[:3]:  # 只显示前3个
                            print(f"            - {newer[1]} (资料性附录={newer[2]}, 年份={newer[3]})")

if __name__ == "__main__":
    debug_recommendation_system()
