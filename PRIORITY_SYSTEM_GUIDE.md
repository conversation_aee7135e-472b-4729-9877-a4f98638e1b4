# 推荐方法优先级系统说明

## 优先级定义

评价管理系统中的推荐方法优先级采用数值表示，定义如下：

| 优先级数值 | 优先级名称 | 说明 |
|-----------|-----------|------|
| **1** | **高** | 最优先推荐的标准方法 |
| **2** | **中** | 次优先推荐的标准方法 |
| **3** | **低** | 备选的标准方法 |

## 导入时的优先级设定

### Excel导入推荐配置

当通过Excel文件导入推荐配置时，系统会自动设定优先级：

- **默认优先级**: `1`（高优先级）
- **设定理由**: 导入的推荐方法通常是经过专业评估的首选方法
- **可后续调整**: 导入后可在界面中手动调整优先级

### 导入逻辑代码位置

```python
# standard_methods_manager.py 第2298行和第2312行
priority = 1  # 高优先级（1=高，2=中，3=低）
```

### Excel文件格式要求

- **第1列**: 检测项目名称（支持别名匹配）
- **第2列**: 推荐标准方法（支持多个方法，用顿号"、"分隔）
- **优先级**: 自动设为高优先级（1）

## 手动添加时的优先级设定

### 前端界面设定

在评价方法编辑界面中，用户可以手动选择优先级：

```html
<select class="form-select priority-select">
    <option value="1">高</option>
    <option value="2">中</option>
    <option value="3">低</option>
</select>
```

### 默认值

- **新增推荐配置**: 默认选择"高"优先级（value="1"）
- **编辑现有配置**: 保持原有优先级设定

## 优先级的使用场景

### 1. 推荐方法排序

在生成推荐方案时，系统会按优先级排序：
- 高优先级（1）的方法优先显示
- 同等优先级按其他条件（如覆盖项目数量）排序

### 2. 报告生成

在生成检测方案报告时：
- 优先推荐高优先级的标准方法
- 在方法选择说明中体现优先级信息

### 3. 统计分析

在系统统计中：
- 可按优先级分组统计推荐方法数量
- 分析不同优先级方法的使用频率

## 历史问题修复

### 发现的问题

在2025年8月2日的系统检查中发现：
- Excel导入功能错误地将优先级设为`3`（低优先级）
- 但注释和用户期望是高优先级
- 导致93条导入记录的优先级错误

### 修复措施

1. **代码修复**: 将导入时的优先级从`3`改为`1`
2. **数据修复**: 使用`fix_priority_data.py`脚本修正现有数据
3. **文档更新**: 明确优先级定义和使用说明

### 修复结果

- ✅ 所有Excel导入记录的优先级已修正为1（高优先级）
- ✅ 新的导入功能正确设置优先级为1
- ✅ 数据库备份已保存，可回滚

## 最佳实践建议

### 1. 优先级设定原则

- **高优先级（1）**: 
  - 国家标准或行业标准
  - 检测精度高、可靠性强的方法
  - 实验室常用、成熟的检测方法

- **中优先级（2）**:
  - 备选的标准方法
  - 特定条件下适用的方法
  - 新发布但尚未广泛应用的方法

- **低优先级（3）**:
  - 临时性或特殊情况下的方法
  - 即将废止的旧标准
  - 实验性或研究性方法

### 2. 导入建议

- 导入前仔细检查Excel文件格式
- 导入后验证优先级设定是否符合预期
- 根据实际需要调整个别方法的优先级

### 3. 维护建议

- 定期审查推荐方法的优先级设定
- 根据标准更新情况调整优先级
- 保持优先级设定的一致性和合理性

## 技术实现细节

### 数据库结构

```sql
CREATE TABLE evaluation_recommendations (
    id INTEGER PRIMARY KEY,
    evaluation_method_id INTEGER,
    detection_item_id INTEGER,
    recommended_standard_method_id INTEGER,
    priority INTEGER DEFAULT 1,  -- 默认高优先级
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 相关文件

- `standard_methods_manager.py`: 导入逻辑实现
- `templates/evaluation_methods.html`: 前端界面
- `fix_priority_data.py`: 数据修复脚本
- `web_app.py`: API接口

### 监控和验证

可使用以下SQL查询验证优先级分布：

```sql
-- 查看优先级分布
SELECT priority, COUNT(*) as count
FROM evaluation_recommendations 
GROUP BY priority 
ORDER BY priority;

-- 查看Excel导入记录的优先级
SELECT priority, COUNT(*) as count
FROM evaluation_recommendations 
WHERE reason LIKE 'Excel批量导入%'
GROUP BY priority;
```

## 联系信息

如有优先级设定相关问题，请联系系统管理员或查看相关技术文档。
