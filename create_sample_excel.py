#!/usr/bin/env python3
"""
创建标准方法管理示例Excel文件
"""

import pandas as pd
from datetime import datetime

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 示例数据
    data = [
        {
            '标准号': 'GB-5085.3-2007',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '危险废物鉴别标准 浸出毒性鉴别',
            '适用范围': '固体废物浸出',
            '检测项目': 'Pb、Cd、Hg、As、Cr⁶⁺'
        },
        {
            '标准号': 'HJ-557-2010',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '固体废物 浸出毒性浸出方法 水平振荡法',
            '适用范围': '固体废物浸出',
            '检测项目': 'Pb、Cd、Hg、As、Cr、Cu、Zn、Ni'
        },
        {
            '标准号': 'GB/T-15555.12-1995',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '固体废物 浸出毒性浸出方法 醋酸缓冲溶液法',
            '适用范围': '固体废物浸出',
            '检测项目': 'Pb、Cd、Hg、As、Cr⁶⁺'
        },
        {
            '标准号': 'HJ-299-2007',
            '附录方法': '附录A',
            '资料性附录': '是',
            '标准名称': '固体废物 浸出毒性浸出方法 硫酸硝酸法',
            '适用范围': '固体废物浸出',
            '检测项目': 'Pb、Cd、Hg、As'
        },
        {
            '标准号': 'GB-15618-2018',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '土壤环境质量 农用地土壤污染风险管控标准',
            '适用范围': '土壤',
            '检测项目': 'Pb、Cd、Hg、As、Cr'
        },
        {
            '标准号': 'HJ-803-2016',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '土壤和沉积物 12种金属元素的测定 王水提取-电感耦合等离子体质谱法',
            '适用范围': '土壤',
            '检测项目': 'Pb、Cd、Hg、As、Cr、Cu、Zn、Ni'
        },
        {
            '标准号': 'GB-3838-2002',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '地表水环境质量标准',
            '适用范围': '水质',
            '检测项目': 'pH、DO、COD、BOD₅、NH₃、TP、TN'
        },
        {
            '标准号': 'HJ-828-2017',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '水质 化学需氧量的测定 重铬酸盐法',
            '适用范围': '水质',
            '检测项目': 'COD'
        },
        {
            '标准号': 'HJ-505-2009',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '水质 五日生化需氧量的测定 稀释与接种法',
            '适用范围': '水质',
            '检测项目': 'BOD₅'
        },
        {
            '标准号': 'GB-3095-2012',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '环境空气质量标准',
            '适用范围': '大气',
            '检测项目': 'SO₂、NO₂、O₃、CO'
        },
        {
            '标准号': 'HJ-482-2009',
            '附录方法': '附录B',
            '资料性附录': '是',
            '标准名称': '环境空气 二氧化硫的测定 甲醛吸收-副玫瑰苯胺分光光度法',
            '适用范围': '大气',
            '检测项目': 'SO₂'
        },
        {
            '标准号': 'GB-12348-2008',
            '附录方法': '',
            '资料性附录': '/',
            '标准名称': '工业企业厂界环境噪声排放标准',
            '适用范围': '噪声',
            '检测项目': '等效连续A声级'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    filename = f'标准方法管理示例数据_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"示例Excel文件已创建: {filename}")
    print(f"包含 {len(data)} 条示例数据")
    
    # 显示数据预览
    print("\n数据预览:")
    print(df.to_string(index=False))
    
    return filename

if __name__ == "__main__":
    create_sample_excel()
