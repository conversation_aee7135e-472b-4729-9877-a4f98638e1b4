<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}康达价格管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS (本地) -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome (本地) -->
    <link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet">
    <!-- Chart.js (本地) -->
    <script src="{{ url_for('static', filename='js/chart.min.js') }}"></script>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stat-card h4 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }

        .stat-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }

        /* 紧凑型统计卡片样式 */
        .stat-card-compact {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card-compact:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-number-compact {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .stat-label-compact {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        .stat-sub-info {
            font-size: 0.75rem;
        }

        .text-white-75 {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        .text-white-50 {
            color: rgba(255, 255, 255, 0.5) !important;
        }

        /* 统计进度条样式 */
        .stat-progress-container {
            margin-top: 0.5rem;
        }

        .stat-progress {
            height: 8px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .stat-progress .progress-bar {
            transition: width 0.6s ease;
        }

        .stat-progress-labels {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-label-item {
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
        }

        .stat-label-separator {
            color: rgba(255, 255, 255, 0.5);
            margin: 0 0.25rem;
        }

        .stat-percentage {
            font-size: 0.7rem;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .loading i {
            font-size: 2rem;
            color: #6c757d;
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            border: none;
            border-radius: 8px;
        }
        
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
        }
        
        .form-control {
            border-radius: 6px;
        }
        
        .table {
            background-color: white;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        /* 表格列分割线样式 */
        .table.column-divider th,
        .table.column-divider td {
            border-right: 1px solid #dee2e6;
        }
        .table.column-divider th:last-child,
        .table.column-divider td:last-child {
            border-right: none;
        }
        
        /* 表格单元格完全居中对齐样式 */
        .table th,
        .table td {
            text-align: center;
            vertical-align: middle !important;
        }
        
        /* 确保所有表格内容居中对齐 */
        .table-responsive table th,
        .table-responsive table td {
            text-align: center;
            vertical-align: middle;
        }
        
        /* 覆盖Bootstrap默认对齐方式 */
        .table th.text-start,
        .table td.text-start {
            text-align: center !important;
        }
        
        .table th.text-end,
        .table td.text-end {
            text-align: center !important;
        }
        
        .badge {
            font-size: 0.8em;
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        .footer {
            background-color: #343a40;
            color: #adb5bd;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 10px;
            }
        }

        /* 骨架屏样式 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .skeleton-chart {
            height: 300px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .skeleton-text {
            height: 20px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .skeleton-text.short {
            width: 60%;
        }

        .skeleton-text.medium {
            width: 80%;
        }

        .skeleton-text.long {
            width: 100%;
        }

        /* 加载进度指示器 */
        .loading-progress {
            position: relative;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #6c757d;
            font-size: 14px;
        }

        /* 图表容器优化 */
        .chart-container {
            position: relative;
            min-height: 300px;
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(248, 249, 250, 0.9);
            z-index: 10;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-chart-line me-2"></i>康达价格管理系统
            </a>
            
            {% if request.endpoint != 'login' %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'dashboard' else '' }}"
                           href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>控制台
                        </a>
                    </li>

                    <!-- 价格管理下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {{ 'active' if request.endpoint in ['data_fetch', 'analysis_methods', 'method_price_overview'] else '' }}"
                           href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-dollar-sign me-1"></i>价格管理
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item {{ 'active' if request.endpoint == 'data_fetch' else '' }}"
                                   href="{{ url_for('data_fetch') }}">
                                    <i class="fas fa-download me-2"></i>数据抓取
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ 'active' if request.endpoint == 'analysis_methods' else '' }}"
                                   href="{{ url_for('analysis_methods') }}">
                                    <i class="fas fa-microscope me-2"></i>分析方法
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ 'active' if request.endpoint == 'method_price_overview' else '' }}"
                                   href="{{ url_for('method_price_overview') }}">
                                    <i class="fas fa-chart-line me-2"></i>方法价格全览
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 标准方法管理 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {{ 'active' if request.endpoint in ['standard_methods', 'evaluation_methods'] else '' }}"
                           href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-book me-1"></i>标准方法
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item {{ 'active' if request.endpoint == 'standard_methods' else '' }}"
                                   href="{{ url_for('standard_methods') }}">
                                    <i class="fas fa-list me-2"></i>方法管理
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ 'active' if request.endpoint == 'evaluation_methods' else '' }}"
                                   href="{{ url_for('evaluation_methods') }}">
                                    <i class="fas fa-star me-2"></i>评价管理
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
            
            <div class="navbar-nav ms-auto">
                {% if session.user_info %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ session.user_info.staffName or session.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog me-2"></i>系统设置
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <!-- 主内容区域 -->
        <main class="px-4 main-content">
            {% block content %}{% endblock %}
        </main>
    </div>
    
    <!-- 底部 -->
    {% if request.endpoint != 'login' %}
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 康达价格管理系统. 让价格数据管理更智能、更高效！</p>
        </div>
    </footer>
    {% endif %}
    
    <!-- Bootstrap JS (本地) -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery (本地) -->
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    
    <script>
        // 全局JavaScript函数
        
        // 显示加载状态
        function showLoading(elementId, message = '加载中...') {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p class="mt-2">${message}</p>
                    </div>
                `;
            }
        }
        
        // 显示错误消息
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>${message}
                    </div>
                `;
            }
        }
        
        // 显示成功消息
        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>${message}
                    </div>
                `;
            }
        }
        
        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // AJAX请求封装
        function apiRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',  // 确保包含cookies
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            return fetch(url, finalOptions)
                .then(response => {
                    if (response.status === 302 || response.redirected) {
                        // 如果被重定向到登录页面，刷新页面
                        window.location.reload();
                        return { success: false, message: '会话已过期，请重新登录' };
                    }

                    // 检查响应内容类型
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        // 如果不是JSON响应，可能是登录页面
                        if (response.url.includes('/login') || response.status === 401) {
                            window.location.href = '/login';
                            return { success: false, message: '登录已过期，请重新登录' };
                        }
                        throw new Error('响应不是有效的JSON格式');
                    }

                    return response.json();
                })
                .catch(error => {
                    console.error('API请求失败:', error);

                    // 检查是否是JSON解析错误（可能是登录页面）
                    if (error.message && error.message.includes('JSON')) {
                        return { success: false, message: '登录已过期，请重新登录' };
                    }

                    return { success: false, message: '网络请求失败: ' + error.message };
                });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>