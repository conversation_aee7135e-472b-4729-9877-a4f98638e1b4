# 优化智能推荐系统指南

## 优化概述

根据用户需求，对智能推荐系统进行了全面优化，重点改进了推荐逻辑、可用性检查和显示方式。

## 核心优化内容

### 1. 资料性附录方法可用性检查

#### 检查逻辑
- **检查条件**: 当推荐的标准方法属于资料性附录方法时
- **检查规则**: 在当前适用范围中，如果该检测项目存在更新的标准方法，则该资料性附录方法不可用
- **判断标准**: 
  - 非资料性附录方法优先于资料性附录方法
  - 更新年份的资料性附录方法优先于旧版本

#### 实现方法
```python
def _check_method_availability(self, method_id: int, applicable_scope_id: int, item_text: str) -> bool:
    """检查方法可用性 - 特别是资料性附录方法"""
    # 1. 获取方法信息
    # 2. 如果不是资料性附录方法，直接可用
    # 3. 对于资料性附录方法，检查是否有更新的方法
    # 4. 查找该检测项目在当前适用范围内是否有更新的方法
```

### 2. 优化推荐策略

#### 推荐优先级
1. **覆盖项目数量优先**: 能检测项目数量越多的方法优先级越高
2. **配置优先级**: 在覆盖项目数量相同时，按评价方法中设定的优先级排序
3. **可用性过滤**: 只推荐可用的方法（通过资料性附录检查）

#### 推荐算法
```python
def _get_optimized_recommendations(self, evaluation_method_id: int, applicable_scope_id: int, 
                                 detection_items: List[str]) -> Dict[str, Any]:
    """获取优化的推荐方案 - 优先推荐能检测多个项目的方法"""
    # 1. 收集所有可用的推荐配置
    # 2. 检查每个方法的可用性
    # 3. 按覆盖项目数量和优先级排序
    # 4. 生成推荐方案
```

### 3. 推荐结果显示格式

#### 检测方案展示
- **格式**: "{标准方法名称}，检测{项目1、项目2、项目3}等项目"
- **排序**: 按覆盖项目数量降序排列
- **标识**: 最优方案用皇冠图标标识

#### 显示内容
```html
<div class="recommendation-scheme-text">
    <strong>GB 5085.3-2007</strong>，检测<strong>镉、铅、汞</strong>等项目
</div>
```

### 4. 交互增强 - 悬浮窗功能

#### 悬浮窗触发
- **触发条件**: 鼠标悬停在检测项目上
- **显示内容**: 该检测项目在当前适用范围内的所有可用标准方法列表
- **可用性标识**: 清晰显示每个方法的可用状态

#### 悬浮窗内容
- 方法总数和可用方法数量
- 每个方法的详细信息（标准号、名称、可用性）
- 资料性附录方法的特殊标识
- 可用性原因说明

## 技术实现细节

### 1. 后端API优化

#### 新增API接口
```
GET /api/detection_items/{item_name}/alternatives?scope_id={scope_id}
```
用于获取检测项目的所有可用标准方法（悬浮窗使用）。

#### 重构推荐API
```
POST /api/intelligent_recommendations
```
返回优化的推荐方案结构：
```json
{
    "success": true,
    "data": {
        "evaluation_method": {...},
        "input_items": [...],
        "matched_items": [...],
        "unmatched_items": [...],
        "recommended_schemes": [...],  // 新增：推荐方案
        "item_details": {...},        // 新增：项目详情
        "total_schemes": 5,
        "match_rate": 0.8
    }
}
```

### 2. 前端界面优化

#### 推荐结果展示
- **方案卡片**: 突出显示推荐方案，按覆盖项目数量排序
- **项目详情**: 显示每个输入项目的匹配状态
- **悬浮窗**: 鼠标悬停显示项目的所有可用方法

#### 用户体验增强
- **视觉层次**: 最优方案用特殊样式突出显示
- **交互反馈**: 悬浮效果和动画增强用户体验
- **信息密度**: 合理组织信息，避免信息过载

### 3. 可用性检查逻辑

#### 检查流程
1. **方法类型判断**: 区分资料性附录方法和普通方法
2. **范围内查找**: 在当前适用范围内查找相同检测项目的其他方法
3. **优先级比较**: 比较方法类型和年份
4. **可用性判定**: 确定方法是否可用

#### 查询逻辑
```sql
SELECT COUNT(*)
FROM standard_methods sm
JOIN standard_method_scopes sms ON sm.id = sms.standard_method_id
JOIN standard_method_items smi ON sm.id = smi.standard_method_id
JOIN detection_items di ON smi.detection_item_id = di.id
WHERE sms.applicable_scope_id = ?
  AND sm.id != ?
  AND sm.status = 0
  AND (di.name IN (...) OR di.display_name IN (...))
  AND (
      sm.is_informative_appendix = 0
      OR (sm.is_informative_appendix = 1 AND CAST(sm.standard_year AS INTEGER) > ?)
  )
```

## 优化效果

### 1. 推荐质量提升
- **智能筛选**: 自动过滤不可用的资料性附录方法
- **效率优先**: 优先推荐能检测多个项目的方法
- **准确性**: 基于评价方法的推荐配置，确保推荐的准确性

### 2. 用户体验改善
- **清晰展示**: 以检测方案的形式展示推荐结果
- **信息丰富**: 悬浮窗提供详细的方法信息
- **操作便捷**: 一目了然的推荐方案，便于用户选择

### 3. 系统一致性
- **逻辑统一**: 与现有方法管理界面的可用性检查逻辑保持一致
- **标准规范**: 遵循系统既有的设计规范和交互模式

## 使用示例

### 输入
- **评价方法**: "危险废物鉴别"
- **适用范围**: "固体废物"
- **检测项目**: "镉、铅、汞、铬、砷"

### 输出
```
推荐检测方案：

1. 👑 GB 5085.3-2007，检测镉、铅、汞、铬、砷等项目
   [高优先级] [覆盖5个项目]

2. GB/T 15555.2-1995，检测镉、铅等项目
   [中优先级] [覆盖2个项目]

3. HJ 781-2016，检测汞等项目
   [高优先级] [覆盖1个项目]
```

### 悬浮窗示例
当鼠标悬停在"镉"上时，显示：
```
镉 - 可用方法
共找到 3 个方法，其中 2 个可用

✅ GB 5085.3-2007 - 危险废物鉴别标准 [可用]
✅ GB/T 15555.2-1995 - 固体废物 铜、锌、镉、铅、铬的测定 [可用]
❌ GB 5085.3-1996 - 危险废物鉴别标准 [有更新方法可用] [资料性附录]
```

## 部署和测试

### 1. 后端部署
- 更新 `standard_methods_manager.py` 中的推荐逻辑
- 添加新的API接口
- 确保数据库查询性能

### 2. 前端部署
- 更新推荐结果显示逻辑
- 添加悬浮窗功能
- 优化CSS样式

### 3. 测试验证
- 测试资料性附录方法的可用性检查
- 验证推荐方案的排序逻辑
- 测试悬浮窗的交互功能

## 后续优化建议

### 1. 性能优化
- 实现推荐结果缓存
- 优化数据库查询
- 添加异步加载

### 2. 功能增强
- 支持推荐方案的收藏和分享
- 添加推荐历史记录
- 实现推荐结果的导出功能

### 3. 用户体验
- 添加推荐理由的详细说明
- 实现推荐方案的对比功能
- 提供推荐结果的反馈机制

## 总结

通过这次优化，智能推荐系统在以下方面得到了显著改善：

1. **准确性**: 通过资料性附录方法可用性检查，确保推荐的方法都是可用的
2. **效率性**: 优先推荐能检测多个项目的方法，提高检测效率
3. **易用性**: 以检测方案的形式展示结果，便于用户理解和选择
4. **信息性**: 通过悬浮窗提供丰富的方法信息，帮助用户做出更好的决策

这些优化使得智能推荐系统更加智能、实用和用户友好，能够为用户提供更高质量的推荐服务。
