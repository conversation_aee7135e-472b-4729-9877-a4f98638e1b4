# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 数据库临时文件
*.db-wal
*.db-shm

# Flask会话文件
flask_session/

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 备份文件
*_backup/
backup_*/
*.backup
*.bak

# 调试文件
debug/
*.debug

# 测试文件
test_*.py
*_test.py
check_*.py
verify_*.py

# 报告文件
static/reports/*.png
static/reports/*.txt
static/captcha.jpg

# 清理脚本
cleanup_*.py
clean_*.py
complete_data_cleaner.py

# 迁移脚本（保留schema文件）
migrate_*.py
*_migration.py

# 文档备份
*_backup.md