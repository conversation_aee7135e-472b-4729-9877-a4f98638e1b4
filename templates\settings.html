{% extends "base.html" %}

{% block title %}系统设置 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>系统设置
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="logoutSettings()">
            <i class="fas fa-sign-out-alt me-1"></i>退出设置
        </button>
    </div>
</div>

<!-- 用户会话管理 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>当前会话信息
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>会话ID:</strong><br>
                    <code>{{ current_session or '未知' }}</code>
                </div>
                <div class="mb-3">
                    <strong>用户名:</strong><br>
                    <span class="text-muted">{{ session.username or '未知' }}</span>
                </div>
                <div class="mb-3">
                    <strong>登录时间:</strong><br>
                    <span class="text-muted">{{ session.login_time or '未知' }}</span>
                </div>
                <div class="mb-3">
                    <strong>会话状态:</strong>
                    <span class="badge bg-success ms-2">活跃</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>会话管理
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <button type="button" class="btn btn-warning w-100 mb-2" onclick="cleanupSessions()">
                        <i class="fas fa-broom me-2"></i>清理过期会话
                    </button>
                    <small class="text-muted">清理所有过期的用户会话，释放系统资源</small>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <button type="button" class="btn btn-outline-secondary w-100 mb-2" onclick="refreshSessionInfo()">
                        <i class="fas fa-sync-alt me-2"></i>刷新会话信息
                    </button>
                    <small class="text-muted">重新加载当前会话和系统状态信息</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 活跃会话列表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>活跃会话列表
                </h5>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshSessionList()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="sessions-list-content">
                    {% if all_sessions and all_sessions|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-hover" style="font-size: 0.95rem;">
                                <thead>
                                    <tr>
                                        <th>会话ID</th>
                                        <th>用户名</th>
                                        <th>登录时间</th>
                                        <th>最后活动</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session_info in all_sessions %}
                                    <tr class="{{ 'table-primary' if session_info.session_key == current_session else '' }}">
                                        <td>
                                            <code>{{ session_info.session_key[:16] }}...</code>
                                            {% if session_info.session_key == current_session %}
                                                <span class="badge bg-primary ms-2">当前</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ session_info.username or '-' }}</td>
                                        <td>
                                            <small class="text-muted">{{ session_info.login_time or '-' }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ session_info.last_activity or '-' }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">活跃</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-2x mb-3"></i>
                            <p>暂无活跃会话信息</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据管理 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>数据管理
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>警告:</strong> 以下操作将永久删除数据，请谨慎操作！建议在清空数据前先进行数据备份。
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">清空缓存数据</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">清理统计缓存和汇总表，不影响业务数据</p>
                                <button type="button" class="btn btn-outline-info btn-sm w-100"
                                        onclick="confirmClearData('cache_only', '缓存数据')">
                                    <i class="fas fa-broom me-1"></i>清空缓存
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">清空价格历史</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">删除所有价格历史记录，保留项目和方法信息</p>
                                <button type="button" class="btn btn-outline-warning btn-sm w-100"
                                        onclick="confirmClearData('price_history', '价格历史记录')">
                                    <i class="fas fa-trash-alt me-1"></i>清空价格历史
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">清空抓取记录</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">删除所有数据抓取日志记录</p>
                                <button type="button" class="btn btn-outline-warning btn-sm w-100"
                                        onclick="confirmClearData('fetch_logs', '抓取记录')">
                                    <i class="fas fa-trash-alt me-1"></i>清空抓取记录
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">清空所有数据</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">删除所有数据，包括项目、方法、价格历史等</p>
                                <button type="button" class="btn btn-danger btn-sm w-100"
                                        onclick="confirmClearData('all_data', '所有数据')">
                                    <i class="fas fa-exclamation-triangle me-1"></i>清空所有数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-2"></i>数据统计</h6>
                        <div id="data-statistics">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span class="ms-2">加载统计数据...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-download me-2"></i>数据备份</h6>
                        <p class="text-muted small">在清空数据前，建议先备份重要数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统配置 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>数据库状态
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>数据库类型:</strong><br>
                    <span class="text-muted">SQLite</span>
                </div>
                <div class="mb-3">
                    <strong>数据库文件:</strong><br>
                    <small class="text-muted font-monospace">kangda_prices.db</small>
                </div>
                <div class="mb-3">
                    <strong>连接状态:</strong>
                    <span class="badge bg-success ms-2">正常</span>
                </div>
                <div class="mb-3">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="checkDatabaseStatus()">
                        <i class="fas fa-check-circle me-1"></i>检查数据库状态
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>系统版本:</strong><br>
                    <span class="text-muted">康达价格管理系统 v1.0</span>
                </div>
                <div class="mb-3">
                    <strong>运行时间:</strong><br>
                    <span class="text-muted" id="system-uptime">计算中...</span>
                </div>
                <div class="mb-3">
                    <strong>最后更新:</strong><br>
                    <span class="text-muted">2025-07-12</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 状态消息区域 -->
<div id="status-message-area" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="alert" id="status-alert" role="alert">
                <span id="status-message"></span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 清理过期会话
function cleanupSessions() {
    showStatusMessage('正在清理过期会话...', 'info');
    
    apiRequest('/api/cleanup_sessions', {
        method: 'POST',
        body: JSON.stringify({})
    })
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            // 刷新会话列表
            setTimeout(() => {
                refreshSessionList();
            }, 1000);
        } else {
            showStatusMessage('清理会话失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showStatusMessage('清理会话时发生网络错误', 'danger');
    });
}

// 刷新会话信息
function refreshSessionInfo() {
    showStatusMessage('正在刷新会话信息...', 'info');
    
    // 重新加载页面
    setTimeout(() => {
        location.reload();
    }, 500);
}

// 刷新会话列表
function refreshSessionList() {
    showLoading('sessions-list-content', '加载会话列表...');
    
    // 这里应该调用获取会话列表的API
    // 目前直接重新加载页面
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 检查数据库状态
function checkDatabaseStatus() {
    showStatusMessage('正在检查数据库状态...', 'info');
    
    // 这里应该调用检查数据库状态的API
    // 目前模拟检查结果
    setTimeout(() => {
        showStatusMessage('数据库状态正常，连接正常', 'success');
    }, 1000);
}

// 确认清空数据
function confirmClearData(dataType, dataName) {
    // 创建确认对话框
    const confirmMessage = `确定要清空${dataName}吗？\n\n此操作无法撤销！建议在清空前先进行数据备份。`;
    
    if (confirm(confirmMessage)) {
        // 再次确认
        const finalConfirm = `最后确认：即将永久删除${dataName}，此操作无法恢复！\n\n确定继续吗？`;
        
        if (confirm(finalConfirm)) {
            clearData(dataType, dataName);
        }
    }
}

// 清空数据
function clearData(dataType, dataName) {
    showStatusMessage(`正在清空${dataName}...`, 'warning');
    
    apiRequest('/api/clear_data', {
        method: 'POST',
        body: JSON.stringify({
            data_type: dataType
        })
    })
    .then(data => {
        if (data.success) {
            showStatusMessage(`${dataName}清空成功！${data.message}`, 'success');
            // 刷新数据统计
            loadDataStatistics();
        } else {
            showStatusMessage(`清空${dataName}失败: ${data.message}`, 'danger');
        }
    })
    .catch(error => {
        showStatusMessage(`清空${dataName}时发生网络错误`, 'danger');
    });
}

// 加载数据统计
function loadDataStatistics() {
    showLoading('data-statistics', '加载统计数据...');
    
    apiRequest('/api/get_data_statistics')
        .then(data => {
            if (data.success) {
                displayDataStatistics(data.statistics);
            } else {
                document.getElementById('data-statistics').innerHTML = 
                    '<p class="text-muted">无法加载统计数据</p>';
            }
        })
        .catch(error => {
            document.getElementById('data-statistics').innerHTML = 
                '<p class="text-danger">加载统计数据失败</p>';
        });
}

// 显示数据统计
function displayDataStatistics(stats) {
    const container = document.getElementById('data-statistics');
    
    container.innerHTML = `
        <div class="row">
            <div class="col-6">
                <small class="text-muted">检测项目:</small><br>
                <strong>${stats.service_items || 0}</strong> 个
            </div>
            <div class="col-6">
                <small class="text-muted">检测方法:</small><br>
                <strong>${stats.check_methods || 0}</strong> 个
            </div>
            <div class="col-6 mt-2">
                <small class="text-muted">价格记录:</small><br>
                <strong>${stats.price_history || 0}</strong> 条
            </div>
            <div class="col-6 mt-2">
                <small class="text-muted">抓取记录:</small><br>
                <strong>${stats.fetch_logs || 0}</strong> 条
            </div>
        </div>
        <div class="mt-2">
            <small class="text-muted">数据库大小: <strong>${stats.db_size || '未知'}</strong></small>
        </div>
    `;
}

// 显示状态消息
function showStatusMessage(message, type) {
    const messageArea = document.getElementById('status-message-area');
    const alert = document.getElementById('status-alert');
    const messageSpan = document.getElementById('status-message');
    
    // 移除所有可能的alert类
    alert.className = 'alert';
    alert.classList.add(`alert-${type}`);
    
    messageSpan.textContent = message;
    messageArea.style.display = 'block';
    
    // 滚动到消息区域
    messageArea.scrollIntoView({ behavior: 'smooth' });
    
    // 5秒后自动隐藏成功和信息消息
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            messageArea.style.display = 'none';
        }, 5000);
    }
}

// 页面加载时计算系统运行时间
document.addEventListener('DOMContentLoaded', function() {
    // 加载数据统计
    loadDataStatistics();
    
    // 模拟系统启动时间（实际应该从服务器获取）
    const startTime = new Date(Date.now() - Math.random() * 86400000); // 随机1天内的时间
    
    function updateUptime() {
        const now = new Date();
        const uptime = now - startTime;
        
        const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
        const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        
        let uptimeStr = '';
        if (days > 0) uptimeStr += `${days}天 `;
        if (hours > 0) uptimeStr += `${hours}小时 `;
        uptimeStr += `${minutes}分钟`;
        
        document.getElementById('system-uptime').textContent = uptimeStr;
    }
    
    // 立即更新一次，然后每分钟更新
    updateUptime();
    setInterval(updateUptime, 60000);
});

// 退出设置验证
function logoutSettings() {
    if (confirm('确定要退出设置验证吗？退出后需要重新输入密码才能访问设置页面。')) {
        $.ajax({
            url: '/api/logout_settings',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    alert('已退出设置验证');
                    window.location.href = '/dashboard';
                } else {
                    alert('退出失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('退出设置验证失败:', error);
                alert('网络错误，请重试');
            }
        });
    }
}
</script>
{% endblock %}