#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时配置 - 用于exe环境的特殊配置
"""

import os
import sys


def setup_runtime_environment():
    """设置运行时环境"""
    
    # 获取可执行文件所在目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        application_path = os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        application_path = os.path.dirname(os.path.abspath(__file__))
    
    # 设置工作目录
    os.chdir(application_path)
    
    # 确保必要的目录存在
    required_dirs = [
        'static/reports',
        'static/exports', 
        'debug',
        'flask_session'
    ]
    
    for dir_path in required_dirs:
        full_path = os.path.join(application_path, dir_path)
        os.makedirs(full_path, exist_ok=True)
    
    return application_path


def get_resource_path(relative_path):
    """获取资源文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，资源文件在临时目录中
        base_path = sys._MEIPASS
    else:
        # 如果是开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)


def check_database_files():
    """检查数据库文件是否存在"""
    db_files = ['kangda_prices.db', 'price_management.db']
    missing_files = []
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            missing_files.append(db_file)
    
    return missing_files


if __name__ == '__main__':
    # 测试配置
    app_path = setup_runtime_environment()
    print(f"应用路径: {app_path}")
    print(f"工作目录: {os.getcwd()}")
    
    missing_dbs = check_database_files()
    if missing_dbs:
        print(f"缺少数据库文件: {missing_dbs}")
    else:
        print("数据库文件检查通过")
