{% extends "base.html" %}

{% block title %}控制台 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>系统控制台
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cogs"></i> 统计管理
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="incrementalUpdateStatistics()">
                    <i class="fas fa-plus-circle me-2"></i>增量更新统计
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="checkStatisticsConsistency()">
                    <i class="fas fa-check-circle me-2"></i>检查数据一致性
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="rebuildStatisticsSummary()">
                    <i class="fas fa-database me-2"></i>重建统计汇总表
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="clearStatisticsCache()">
                    <i class="fas fa-trash me-2"></i>清理统计缓存
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-6 col-md-6 mb-3">
        <div class="card stat-card-compact">
            <div class="card-body text-center py-3">
                <div class="stat-number-compact" id="total-items">{{ stats.total_items or 0 }}</div>
                <div class="stat-label-compact">
                    <i class="fas fa-list-alt me-1"></i>检测项目总数
                </div>

                <!-- 进度条 -->
                <div class="stat-progress-container mt-2">
                    <div class="progress stat-progress">
                        <div class="progress-bar bg-success" role="progressbar"
                             id="items-available-bar" style="width: 0%"></div>
                        <div class="progress-bar bg-danger" role="progressbar"
                             id="items-locked-bar" style="width: 0%"></div>
                    </div>
                    <div class="stat-progress-labels mt-1">
                        <small class="text-white-75">
                            <span class="stat-label-item">
                                <i class="fas fa-unlock me-1"></i>
                                <span id="items-available-count">{{ (stats.total_items or 0) - (stats.locked_items or 0) }}</span> 有效
                            </span>
                            <span class="stat-label-separator">|</span>
                            <span class="stat-label-item">
                                <i class="fas fa-lock me-1"></i>
                                <span id="items-locked-count">{{ stats.locked_items or 0 }}</span> 锁定
                            </span>
                        </small>
                    </div>
                    <div class="stat-percentage mt-1">
                        <small class="text-white-50" id="items-locked-percentage">
                            锁定占比: 0%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 col-md-6 mb-3">
        <div class="card stat-card-compact" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="card-body text-center py-3">
                <div class="stat-number-compact" id="total-methods">{{ stats.total_methods or 0 }}</div>
                <div class="stat-label-compact">
                    <i class="fas fa-flask me-1"></i>检测方法总数
                </div>

                <!-- 进度条 -->
                <div class="stat-progress-container mt-2">
                    <div class="progress stat-progress">
                        <div class="progress-bar bg-success" role="progressbar"
                             id="methods-available-bar" style="width: 0%"></div>
                        <div class="progress-bar bg-danger" role="progressbar"
                             id="methods-locked-bar" style="width: 0%"></div>
                    </div>
                    <div class="stat-progress-labels mt-1">
                        <small class="text-white-75">
                            <span class="stat-label-item">
                                <i class="fas fa-unlock me-1"></i>
                                <span id="methods-available-count">{{ (stats.total_methods or 0) - (stats.locked_methods or 0) }}</span> 有效
                            </span>
                            <span class="stat-label-separator">|</span>
                            <span class="stat-label-item">
                                <i class="fas fa-lock me-1"></i>
                                <span id="methods-locked-count">{{ stats.locked_methods or 0 }}</span> 锁定
                            </span>
                        </small>
                    </div>
                    <div class="stat-percentage mt-1">
                        <small class="text-white-50" id="methods-locked-percentage">
                            锁定占比: 0%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row">
    <!-- 分类统计 -->
    <div class="col-lg-12 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>主要检测分类
                    </h5>
                    <div id="category-sort-buttons" class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active" data-sort="item_count">按项目数</button>
                        <button type="button" class="btn btn-outline-primary" data-sort="method_count">按方法数</button>
                        <button type="button" class="btn btn-outline-primary" data-sort="qualification_count">按资质数</button>
                        <button type="button" class="btn btn-outline-primary" data-sort="name">按字母序</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="categories-list" class="row">
                    <!-- Categories will be loaded here by JavaScript -->
                </div>
                <div id="categories-pagination-container" class="d-flex justify-content-between align-items-center mt-3" style="display: none;">
                    <div class="pagination-info">
                        <small class="text-muted" id="categories-page-info"></small>
                    </div>
                    <nav aria-label="Categories pagination">
                        <ul class="pagination pagination-sm mb-0" id="categories-pagination">
                            <!-- Pagination will be loaded here by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类数据可视化 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>分类方法统计图表
                    </h5>
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>支持水平滚动查看所有分类
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <!-- 图表容器 - 支持水平滚动和Y轴固定 -->
                        <div id="categories-chart-container" style="position: relative; height: 500px;">
                            <!-- Y轴固定区域 -->
                            <div id="chart-y-axis" style="position: absolute; left: 0; top: 0; width: 80px; height: 100%; background: white; z-index: 10; border-right: 1px solid #dee2e6;">
                                <canvas id="yAxisChart" style="width: 100%; height: 100%;"></canvas>
                            </div>

                            <!-- 可滚动的图表内容区域 -->
                            <div id="chart-content-area" style="position: absolute; left: 80px; top: 0; right: 0; height: 100%; overflow-x: auto;">
                                <div id="chart-scrollable-content" style="min-width: 800px; height: 100%;">
                                    <canvas id="categoriesChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 图表说明 -->
                        <div class="mt-3">
                            <div class="text-muted small">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>图表说明：</strong>
                                        <ul class="mb-0 ps-3">
                                            <li><span class="badge bg-primary me-1">蓝色柱状图</span>总方法数量：各分类下的所有检测方法总数</li>
                                            <li><span class="badge bg-success me-1">绿色曲线图</span>可用方法数量：项目和方法都未锁定的方法数量</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>交互提示：</strong>
                                        <ul class="mb-0 ps-3">
                                            <li>鼠标悬停查看具体数值</li>
                                            <li>图表支持水平滚动查看所有分类</li>
                                            <li>可用率 = 可用方法数量 / 总方法数量</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 方法变动统计 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>变动数据分析
                </h5>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <label for="timeRangeSelect" class="form-label mb-0 me-2">统计维度:</label>
                        <select id="timeRangeSelect" class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="refreshAllStatistics()">
                            <option value="month">按天统计</option>
                            <option value="quarter">按周统计</option>
                            <option value="year">按月统计</option>
                        </select>
                    </div>
                    <div class="me-3">
                        <label for="monthsSelect" class="form-label mb-0 me-2">时间范围:</label>
                        <select id="monthsSelect" class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="refreshAllStatistics()">
                            <option value="1">1个月</option>
                            <option value="2">2个月</option>
                            <option value="3" selected>3个月</option>
                            <option value="6">6个月</option>
                            <option value="12">12个月</option>
                            <option value="18">18个月</option>
                            <option value="24">24个月</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshAllStatistics()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 图表类型切换按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="btn-group" role="group" aria-label="图表类型切换">
                        <button type="button" class="btn btn-light active" id="methodChangeBtn" onclick="switchChart('methodChange')" style="border: 1px solid #dee2e6; color: #495057;">
                            <i class="fas fa-chart-line me-1"></i>方法变动
                        </button>
                        <button type="button" class="btn btn-light" id="newMethodsBtn" onclick="switchChart('newMethods')" style="border: 1px solid #dee2e6; color: #6c757d;">
                            <i class="fas fa-plus me-1"></i>新增方法
                        </button>
                        <button type="button" class="btn btn-light" id="qualificationBtn" onclick="switchChart('qualification')" style="border: 1px solid #dee2e6; color: #6c757d;">
                            <i class="fas fa-certificate me-1"></i>资质变更
                        </button>
                        <button type="button" class="btn btn-light" id="priceChangesBtn" onclick="switchChart('priceChanges')" style="border: 1px solid #dee2e6; color: #6c757d;">
                            <i class="fas fa-dollar-sign me-1"></i>价格变更
                        </button>
                    </div>
                    <small class="text-muted" id="chartDescription">
                        <i class="fas fa-info-circle me-1"></i>
                        统计指定时间范围内有价格变动的方法数量，点击数据点查看详细信息
                    </small>
                </div>
                <!-- 统一图表容器 -->
                <div class="row">
                    <div class="col-12">
                        <!-- 方法变动统计 -->
                        <div id="methodChangeChartContainer" class="chart-container" style="display: block;">
                            <div class="chart-loading">
                                <div class="skeleton skeleton-chart"></div>
                                <div class="loading-progress">
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在加载方法变动统计数据...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 新增方法统计 -->
                        <div id="newMethodsChartContainer" class="chart-container" style="display: none;">
                            <div class="chart-loading">
                                <div class="skeleton skeleton-chart"></div>
                                <div class="loading-progress">
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在加载新增方法统计数据...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 资质变更统计 -->
                        <div id="qualificationChangesChartContainer" class="chart-container" style="display: none;">
                            <div class="chart-loading">
                                <div class="skeleton skeleton-chart"></div>
                                <div class="loading-progress">
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在加载资质变更统计数据...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 价格变更统计 -->
                        <div id="priceChangesChartContainer" class="chart-container" style="display: none;">
                            <div class="chart-loading">
                                <div class="skeleton skeleton-chart"></div>
                                <div class="loading-progress">
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在加载价格变更统计数据...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 价格变动分析 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>价格变动分析
                </h5>
                <div>
                    <button type="button" class="btn btn-outline-info btn-sm me-2" onclick="showPriceChangeHelp()">
                        <i class="fas fa-question-circle"></i> 帮助
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadComprehensivePriceChanges()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 标签页导航 -->
                <ul class="nav nav-tabs" id="priceChangeTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent-changes" type="button" role="tab">
                            <i class="fas fa-clock me-1"></i>近期变动
                            <span class="badge bg-primary ms-1" id="recent-count">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="baseline-tab" data-bs-toggle="tab" data-bs-target="#baseline-changes" type="button" role="tab">
                            <i class="fas fa-chart-line me-1"></i>基准变动
                            <span class="badge bg-success ms-1" id="baseline-count">0</span>
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content mt-3" id="priceChangeTabContent">
                    <div class="tab-pane fade show active" id="recent-changes" role="tabpanel">
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                显示最新价格与历史上第一个不同价格的对比，反映最近一次价格变化（需至少2次抓取记录）
                            </small>
                        </div>
                        <div id="recent-changes-content">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                <p class="mt-2">加载近期价格变动数据...</p>
                            </div>
                        </div>
                        <!-- 分页控件 -->
                        <div id="recent-pagination" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    显示第 <span id="recent-start-item">1</span> - <span id="recent-end-item">20</span> 条，
                                    共 <span id="recent-total-items">0</span> 条记录
                                </small>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="recent-pagination-nav">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="baseline-changes" role="tabpanel">
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                显示当前价格与基准价格的对比，反映长期价格趋势（需至少2次抓取记录）
                            </small>
                        </div>
                        <!-- 基准价格管理工具栏 -->
                        <div class="mb-3" id="baseline-toolbar" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="setSelectedAsBaseline()">
                                        <i class="fas fa-anchor me-1"></i>设为基准价格
                                    </button>
                                    <span class="ms-3 text-muted" id="selected-count">已选择 0 个方法</span>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="showManualBaselineManagement()">
                                        <i class="fas fa-cog me-1"></i>手动基准价格管理
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="showBaselineHelp()">
                                        <i class="fas fa-question-circle me-1"></i>基准价格说明
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="baseline-changes-content">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                <p class="mt-2">加载基准价格变动数据...</p>
                            </div>
                        </div>
                        <!-- 分页控件 -->
                        <div id="baseline-pagination" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    显示第 <span id="baseline-start-item">1</span> - <span id="baseline-end-item">20</span> 条，
                                    共 <span id="baseline-total-items">0</span> 条记录
                                </small>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="baseline-pagination-nav">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 价格趋势弹窗 -->
<div class="modal fade" id="priceTrendModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="priceTrendModalTitle">
                    <i class="fas fa-chart-line me-2"></i>价格趋势图
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="priceTrendContent">
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载价格趋势数据中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 手动基准价格管理 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>手动基准价格管理
                    <span class="badge bg-info ms-2" id="manual-baseline-count">0</span>
                </h5>
                <div>
                    <button type="button" class="btn btn-outline-warning btn-sm me-2" onclick="batchRestoreBaseline()">
                        <i class="fas fa-undo me-1"></i>批量恢复默认
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshManualBaselines()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        显示所有被人为设定过基准价格的方法，可以查看当前价格与手动基准价格、系统默认基准价格的对比
                    </small>
                </div>
                <div id="manual-baseline-content">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p class="mt-2">加载手动基准价格数据...</p>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div id="manual-baseline-pagination" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                    <div class="pagination-info">
                        <small class="text-muted">
                            显示第 <span id="manual-baseline-start-item">1</span> - <span id="manual-baseline-end-item">20</span> 条，
                            共 <span id="manual-baseline-total-items">0</span> 条记录
                        </small>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="manual-baseline-pagination-nav">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 手动基准价格管理弹窗 -->
<div class="modal fade" id="manualBaselineModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6">
                    <i class="fas fa-cog me-2"></i>手动基准价格管理
                    <span class="badge bg-info ms-2" id="modal-manual-baseline-count">0</span>
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-3">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="button" class="btn btn-warning btn-sm" onclick="batchRestoreBaselineInModal()">
                                <i class="fas fa-undo me-1"></i>批量恢复默认
                            </button>
                            <span class="ms-3 text-muted" id="modal-selected-count">已选择 0 个方法</span>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshManualBaselinesInModal()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div id="modal-manual-baseline-content">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p class="mt-2">加载手动基准价格数据...</p>
                    </div>
                </div>
                <!-- 分页控件 -->
                <div id="modal-manual-baseline-pagination" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                    <div class="pagination-info">
                        <small class="text-muted">
                            显示第 <span id="modal-manual-baseline-start-item">1</span> - <span id="modal-manual-baseline-end-item">20</span> 条，
                            共 <span id="modal-manual-baseline-total-items">0</span> 条记录
                        </small>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="modal-manual-baseline-pagination-nav">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 基准价格设置帮助弹窗 -->
<div class="modal fade" id="baselineHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6">
                    <i class="fas fa-anchor me-2"></i>手动基准价格设置说明
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-3">
                <div class="help-content">
                    <div class="mb-4">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-info-circle me-2"></i>功能说明
                        </h6>
                        <p class="mb-2">手动基准价格设置功能允许您为特定方法设定自定义的基准价格，用于更精确的价格变动分析。</p>
                        <ul class="mb-0">
                            <li><strong>设置基准：</strong>选择方法后点击"设为基准价格"，将当前价格设为基准</li>
                            <li><strong>自动移除：</strong>设置基准价格后，该方法立即从基准变动列表中移除</li>
                            <li><strong>重新显示：</strong>只有当价格相对于新基准再次变化时，才重新显示</li>
                            <li><strong>优先级：</strong>手动设置的基准价格优先于历史第一次价格</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-success mb-2">
                            <i class="fas fa-cogs me-2"></i>使用步骤
                        </h6>
                        <ol class="mb-0">
                            <li>在基准变动列表中勾选要设置基准价格的方法</li>
                            <li>点击"设为基准价格"按钮</li>
                            <li>系统将当前价格设为这些方法的基准价格</li>
                            <li>设置完成后，这些方法从当前列表中移除</li>
                            <li>当价格再次变化时，将基于新的基准价格计算变动</li>
                        </ol>
                    </div>

                    <div class="alert alert-info mb-0">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-lightbulb me-2"></i>使用建议
                        </h6>
                        <ul class="mb-0 small">
                            <li><strong>适用场景：</strong>当您认为某个价格点应作为新的参考基准时使用</li>
                            <li><strong>批量操作：</strong>可以同时为多个方法设置基准价格</li>
                            <li><strong>清除基准：</strong>如需恢复使用历史第一次价格，可清除手动基准</li>
                            <li><strong>数据保护：</strong>手动基准价格会记录设置时间和操作用户</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 价格变动分析帮助弹窗 -->
<div class="modal fade" id="priceChangeHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6">
                    <i class="fas fa-question-circle me-2"></i>价格变动分析帮助
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-3">
                <div class="help-content">
                    <!-- 数据范围说明 -->
                    <div class="mb-4">
                        <h6 class="text-info mb-2">
                            <i class="fas fa-database me-2"></i>数据分析范围
                        </h6>
                        <p class="mb-2">系统分析<strong>最近7天内被抓取过数据的方法</strong>，然后检测这些方法是否存在价格变动。</p>
                        <ul class="mb-0">
                            <li><strong>时间基准：</strong>"7天"指的是数据抓取时间（fetch_time），不是价格变动发生时间</li>
                            <li><strong>筛选条件：</strong>只分析至少被抓取过2次的方法，确保有足够历史数据判断变动</li>
                            <li><strong>显示限制：</strong>每种变动类型最多显示前10个方法</li>
                            <li><strong>新建方法：</strong>只有1条价格记录的方法不会显示在任何价格变动分析中</li>
                        </ul>
                    </div>

                    <!-- 近期变动说明 -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-clock me-2"></i>近期变动
                        </h6>
                        <p class="mb-2">比较方法的<strong>最新价格</strong>与<strong>历史上第一个不同的价格</strong>，显示最近一次价格变化。</p>
                        <ul class="mb-0">
                            <li><strong>检测逻辑：</strong>获取最新价格，然后向前查找第一个与之不同的历史价格</li>
                            <li><strong>智能跳过：</strong>自动跳过相同价格的记录，找到真正的价格变化点</li>
                            <li><strong>变动要求：</strong>只有至少2次抓取记录的方法才会显示近期变动</li>
                            <li><strong>适用场景：</strong>监控最新的价格调整方向和幅度</li>
                        </ul>
                    </div>

                    <!-- 基准变动说明 -->
                    <div class="mb-4">
                        <h6 class="text-success mb-2">
                            <i class="fas fa-chart-line me-2"></i>基准变动
                        </h6>
                        <p class="mb-2">比较方法的<strong>当前价格</strong>与<strong>历史第一次记录的价格</strong>，显示累计价格变化。</p>
                        <ul class="mb-0">
                            <li><strong>基准定义：</strong>使用历史上第一次抓取的价格作为基准价格</li>
                            <li><strong>数据要求：</strong>需要至少2条价格记录才能进行基准变动分析</li>
                            <li><strong>变动条件：</strong>只有当前价格与基准价格不同时才显示变动</li>
                            <li><strong>适用场景：</strong>分析价格的历史走势和整体变化幅度</li>
                        </ul>
                    </div>

                    <!-- 价格变动指示器说明 -->
                    <div class="mb-4">
                        <h6 class="text-warning mb-2">
                            <i class="fas fa-signal me-2"></i>价格变动指示器
                        </h6>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-arrow-up text-danger me-2"></i>
                                    <span>价格上涨（红色上箭头）</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-arrow-down text-success me-2"></i>
                                    <span>价格下降（绿色下箭头）</span>
                                </div>
                            </div>
                            <div class="col-md-12 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-2">无变动</span>
                                    <span>（灰色文字显示）</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 采样价格处理说明 -->
                    <div class="mb-3">
                        <h6 class="text-info mb-2">
                            <i class="fas fa-vial me-2"></i>采样价格处理逻辑
                        </h6>
                        <ul class="mb-0">
                            <li><strong>全历史覆盖：</strong>分析所有历史上出现过的采样类型，不仅限于当前存在的</li>
                            <li><strong>变动检测：</strong>采样价格变动遵循与主价格相同的逻辑：
                                <ul class="mt-1">
                                    <li><strong>近期变动：</strong>比较最新价格与历史上第一个不同的价格</li>
                                    <li><strong>基准变动：</strong>比较当前价格与历史第一次记录的价格</li>
                                </ul>
                            </li>
                            <li><strong>状态识别：</strong>自动识别采样类型的三种状态：
                                <ul class="mt-1">
                                    <li><strong>正常变动：</strong>价格发生变化的现有采样类型</li>
                                    <li><strong>新增：</strong>历史上没有，最新抓取中出现的采样类型</li>
                                    <li><strong>删除：</strong>历史上存在，最新抓取中消失的采样类型（标记为"已删除"）</li>
                                </ul>
                            </li>
                            <li><strong>删除处理：</strong>删除的采样类型显示最后一次的价格，变动幅度为-100%</li>
                        </ul>
                    </div>

                    <!-- 重要提醒 -->
                    <div class="alert alert-warning mb-0">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>重要说明
                        </h6>
                        <ul class="mb-0 small">
                            <li><strong>时间范围：</strong>"最近7天"指的是系统数据抓取时间，不是价格变动发生时间</li>
                            <li><strong>变动检测：</strong>只有实际价格发生变化的方法才会显示在变动列表中</li>
                            <li><strong>数据要求：</strong>所有价格变动分析都需要至少2次抓取记录</li>
                            <li><strong>新建方法：</strong>只有1条价格记录的方法不会显示在任何价格变动分析中</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 分类容器样式优化 */
.category-item {
    transition: all 0.3s ease;
    min-height: 180px; /* 确保最小高度，避免布局跳动 */
}

/* 分类列表容器过渡效果 */
#categories-list {
    transition: opacity 0.3s ease-in-out;
    position: relative;
}

/* 加载遮罩层 */
.categories-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    backdrop-filter: blur(1px);
}

.categories-loading-overlay.show {
    opacity: 1;
}

/* 加载指示器样式 */
.categories-loading-spinner {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 淡入动画 */
.categories-fade-in {
    animation: categoriesFadeIn 0.4s ease-out;
}

@keyframes categoriesFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 优化骨架屏动画效果（备用） */
.placeholder-glow .placeholder {
    animation: placeholder-glow 2s ease-in-out infinite alternate;
}

@keyframes placeholder-glow {
    0% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 确保骨架屏占位符有合适的圆角 */
.placeholder {
    border-radius: 0.25rem;
}

/* 分类图表优化样式 */
#categories-chart-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

#chart-y-axis {
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

#chart-content-area {
    background: #fafafa;
}

#chart-content-area::-webkit-scrollbar {
    height: 8px;
}

#chart-content-area::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#chart-content-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#chart-content-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 页面加载时初始化进度条
    initializeProgressBars();

    // 页面加载时获取综合价格变动数据
    loadComprehensivePriceChanges();

    // 页面加载时获取方法变动统计数据
    loadMethodChangeStatistics();

    // 页面加载时获取详细变动统计数据
    loadDetailedChangeStatistics();

    // 默认显示方法变动图表
    switchChart('methodChange');

    // 添加点击事件委托处理方法名称点击
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('method-name-clickable')) {
            const methodId = e.target.getAttribute('data-method-id');
            const methodName = e.target.getAttribute('data-method-name');
            const projectName = e.target.getAttribute('data-project-name');
            showPriceTrend(methodId, methodName, projectName);
        }
    });
    // 添加分类排序和分页的逻辑
    document.getElementById('category-sort-buttons').addEventListener('click', function(e) {
        if (e.target.tagName === 'BUTTON') {
            document.querySelectorAll('#category-sort-buttons button').forEach(btn => btn.classList.remove('active'));
            e.target.classList.add('active');
            loadCategories();
        }
    });

    loadCategories();

    // 初始化分类可视化图表
    initializeCategoriesChart();
});

let currentCategoriesPage = 1;
let currentCategoriesSort = 'item_count';

// 生成分类骨架屏
function generateCategoriesSkeleton(count = 6) {
    let html = '';
    for (let i = 0; i < count; i++) {
        html += `
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="category-item p-3 border rounded h-100">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="placeholder-glow">
                            <span class="placeholder col-8" style="height: 1.25rem;"></span>
                        </div>
                    </div>
                    <div class="row">
                        <!-- 项目统计骨架 -->
                        <div class="col-4 border-end">
                            <div class="text-center">
                                <div class="placeholder-glow">
                                    <small class="placeholder col-6" style="height: 0.875rem;"></small>
                                    <div class="placeholder col-4 mx-auto mt-1" style="height: 1.5rem;"></div>
                                    <div class="placeholder col-10 mx-auto mt-1" style="height: 6px;"></div>
                                    <div class="d-flex justify-content-around mt-1 px-1">
                                        <small class="placeholder col-3" style="height: 0.7rem;"></small>
                                        <small class="placeholder col-3" style="height: 0.7rem;"></small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 方法统计骨架 -->
                        <div class="col-4 border-end">
                            <div class="text-center">
                                <div class="placeholder-glow">
                                    <small class="placeholder col-6" style="height: 0.875rem;"></small>
                                    <div class="placeholder col-4 mx-auto mt-1" style="height: 1.5rem;"></div>
                                    <div class="placeholder col-10 mx-auto mt-1" style="height: 6px;"></div>
                                    <div class="d-flex justify-content-around mt-1 px-1">
                                        <small class="placeholder col-3" style="height: 0.7rem;"></small>
                                        <small class="placeholder col-3" style="height: 0.7rem;"></small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 资质项目骨架 -->
                        <div class="col-4">
                            <div class="text-center">
                                <div class="placeholder-glow">
                                    <small class="placeholder col-6" style="height: 0.875rem;"></small>
                                    <div class="row mt-1">
                                        <div class="col-4">
                                            <div class="placeholder col-8 mx-auto" style="height: 0.6rem;"></div>
                                            <div class="placeholder col-6 mx-auto mt-1" style="height: 0.8rem;"></div>
                                        </div>
                                        <div class="col-4">
                                            <div class="placeholder col-8 mx-auto" style="height: 0.6rem;"></div>
                                            <div class="placeholder col-6 mx-auto mt-1" style="height: 0.8rem;"></div>
                                        </div>
                                        <div class="col-4">
                                            <div class="placeholder col-8 mx-auto" style="height: 0.6rem;"></div>
                                            <div class="placeholder col-6 mx-auto mt-1" style="height: 0.8rem;"></div>
                                        </div>
                                    </div>
                                    <div class="placeholder col-8 mx-auto mt-1" style="height: 0.65rem;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    return html;
}

function loadCategories(page = 1) {
    currentCategoriesPage = page;

    // 安全获取当前排序方式，如果找不到active按钮则使用默认值
    const activeButton = document.querySelector('#category-sort-buttons .active');
    if (activeButton && activeButton.dataset && activeButton.dataset.sort) {
        currentCategoriesSort = activeButton.dataset.sort;
    } else {
        // 如果找不到active按钮，使用默认排序方式并设置第一个按钮为active
        currentCategoriesSort = 'item_count';
        const firstButton = document.querySelector('#category-sort-buttons button[data-sort="item_count"]');
        if (firstButton) {
            document.querySelectorAll('#category-sort-buttons button').forEach(btn => btn.classList.remove('active'));
            firstButton.classList.add('active');
        }
    }

    const container = document.getElementById('categories-list');

    // 显示加载遮罩层，而不是替换整个内容
    showCategoriesLoading();

    apiRequest(`/api/categories?sort_by=${currentCategoriesSort}&page=${currentCategoriesPage}&per_page=6`)
        .then(data => {
            if (data.success) {
                // 隐藏加载遮罩并渲染新内容
                hideCategoriesLoading();
                renderCategories(data.data.categories);
                renderCategoriesPagination(data.data);
            } else {
                hideCategoriesLoading();
                container.innerHTML = `<div class="col-12"><p class="text-danger">Error: ${data.message}</p></div>`;
            }
        })
        .catch(error => {
            console.error('加载分类数据失败:', error);
            hideCategoriesLoading();
            container.innerHTML = `<div class="col-12"><p class="text-danger">加载分类数据失败: ${error.message}</p></div>`;
        });
}

// 显示分类加载状态
function showCategoriesLoading() {
    const container = document.getElementById('categories-list');

    // 如果是首次加载（容器为空），使用骨架屏
    if (!container.children.length || container.innerHTML.trim() === '<!-- Categories will be loaded here by JavaScript -->') {
        container.innerHTML = generateCategoriesSkeleton(6);
        return;
    }

    // 如果已有内容，显示遮罩层
    let overlay = container.querySelector('.categories-loading-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'categories-loading-overlay';
        overlay.innerHTML = `
            <div class="categories-loading-spinner">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <span>加载中...</span>
            </div>
        `;
        container.appendChild(overlay);
    }

    // 显示遮罩层
    setTimeout(() => overlay.classList.add('show'), 10);
}

// 隐藏分类加载状态
function hideCategoriesLoading() {
    const container = document.getElementById('categories-list');
    const overlay = container.querySelector('.categories-loading-overlay');

    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 200);
    }
}

function renderCategories(categories) {
    const container = document.getElementById('categories-list');
    if (!categories || categories.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted">暂无分类数据</p></div>';
        container.classList.add('categories-fade-in');
        return;
    }

    let html = '';
    categories.forEach(category => {
        const unlocked_items = category.item_count - category.locked_item_count;
        const unlocked_item_percentage = category.unlocked_item_percentage;
        const locked_item_percentage = category.locked_item_percentage;
        const normal_method_percentage = category.normal_method_percentage;
        const locked_method_percentage = category.locked_method_percentage;

        html += `
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="category-item p-3 border rounded h-100">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0" title="${escapeHtml(category.service_category_name)}">${escapeHtml(category.service_category_name.length > 20 ? category.service_category_name.substring(0, 20) + '...' : category.service_category_name)}</h6>
                    </div>
                    <div class="row">
                        <!-- 项目统计 -->
                        <div class="col-4 border-end">
                            <div class="text-center">
                                <small class="text-muted">项目统计</small>
                                <h5 class="mb-1">${category.item_count}</h5>
                                <div class="progress mx-auto" style="height: 6px; max-width: 80px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: ${unlocked_item_percentage}%" title="未锁定项目: ${unlocked_items}"></div>
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: ${locked_item_percentage}%" title="锁定项目: ${category.locked_item_count}"></div>
                                </div>
                                <div class="d-flex justify-content-around mt-1 px-1">
                                    <small style="font-size: 0.7rem;"><i class="fas fa-circle text-success"></i> ${unlocked_items}</small>
                                    <small style="font-size: 0.7rem;"><i class="fas fa-circle text-danger"></i> ${category.locked_item_count}</small>
                                </div>
                            </div>
                        </div>

                        <!-- 方法统计 -->
                        <div class="col-4 border-end">
                            <div class="text-center">
                                <small class="text-muted">方法统计</small>
                                <h5 class="mb-1">${category.method_count}</h5>
                                <div class="progress mx-auto" style="height: 6px; max-width: 80px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: ${normal_method_percentage}%" title="未锁定方法: ${category.normal_method_count}"></div>
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: ${locked_method_percentage}%" title="锁定方法: ${category.locked_method_count}"></div>
                                </div>
                                <div class="d-flex justify-content-around mt-1 px-1">
                                    <small style="font-size: 0.7rem;"><i class="fas fa-circle text-success"></i> ${category.normal_method_count}</small>
                                    <small style="font-size: 0.7rem;"><i class="fas fa-circle text-danger"></i> ${category.locked_method_count}</small>
                                </div>
                            </div>
                        </div>

                        <!-- CMA/CNAS/NHC统计 -->
                        <div class="col-4">
                            <div class="text-center">
                                <small class="text-muted">资质项目</small>
                                <div class="row">
                                    <div class="col-4">
                                        <div class="text-center">
                                            <span class="badge bg-info-subtle text-info border border-info" style="font-size: 0.6rem;">CMA</span>
                                            <div class="fw-bold text-info" style="font-size: 0.8rem;">${category.cma_count || 0}</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-center">
                                            <span class="badge bg-warning-subtle text-warning border border-warning" style="font-size: 0.6rem;">CNAS</span>
                                            <div class="fw-bold text-warning" style="font-size: 0.8rem;">${category.cnas_count || 0}</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-center">
                                            <span class="badge bg-success-subtle text-success border border-success" style="font-size: 0.6rem;">NHC</span>
                                            <div class="fw-bold text-success" style="font-size: 0.8rem;">${category.nhc_count || 0}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted" style="font-size: 0.65rem;">
                                        <i class="fas fa-certificate me-1"></i>认证项目数量
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    // 更新内容并添加淡入动画
    container.innerHTML = html;
    container.classList.remove('categories-fade-in');
    // 强制重排以确保类被移除
    container.offsetHeight;
    container.classList.add('categories-fade-in');

    // 清理动画类
    setTimeout(() => {
        container.classList.remove('categories-fade-in');
    }, 400);
}

function renderCategoriesPagination(data) {
    const paginationContainer = document.getElementById('categories-pagination-container');
    const paginationUl = document.getElementById('categories-pagination');
    const pageInfo = document.getElementById('categories-page-info');

    if (data.total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }
    
    paginationContainer.style.display = 'flex';
    pageInfo.textContent = `第 ${data.current_page} / ${data.total_pages} 页 (共 ${data.total_categories} 个分类)`;

    let paginationHtml = '';
    // Previous button
    paginationHtml += `<li class="page-item ${data.current_page === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="loadCategories(${data.current_page - 1}); return false;">&laquo;</a></li>`;

    // Page numbers
    for (let i = 1; i <= data.total_pages; i++) {
        paginationHtml += `<li class="page-item ${i === data.current_page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadCategories(${i}); return false;">${i}</a></li>`;
    }

    // Next button
    paginationHtml += `<li class="page-item ${data.current_page === data.total_pages ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="loadCategories(${data.current_page + 1}); return false;">&raquo;</a></li>`;

    paginationUl.innerHTML = paginationHtml;
}

// 分类可视化图表相关变量和函数
let categoriesChart = null;
let yAxisChart = null;
let chartData = [];
let currentVisibleRange = { start: 0, end: 0 };
let scrollTimeout = null;
let isAnimating = false;
let currentMaxY = 0;

// 初始化分类可视化图表
function initializeCategoriesChart() {
    loadCategoriesChartData();
}

// 加载分类图表数据
function loadCategoriesChartData() {
    apiRequest('/api/categories-chart')
        .then(data => {
            if (data.success) {
                chartData = data.categories;
                renderOptimizedChart(chartData);
            } else {
                console.error('加载分类图表数据失败:', data.message);
                showChartError('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载分类图表数据失败:', error);
            showChartError('加载数据失败: ' + error.message);
        });
}

// 显示图表错误信息
function showChartError(message) {
    const container = document.getElementById('categories-chart-container');
    container.innerHTML = `<div class="text-center text-danger p-4"><i class="fas fa-exclamation-triangle me-2"></i>${message}</div>`;
}

// 渲染优化的图表系统（固定Y轴 + 可滚动内容）
function renderOptimizedChart(categories) {
    // 销毁现有图表
    if (categoriesChart) {
        categoriesChart.destroy();
    }
    if (yAxisChart) {
        yAxisChart.destroy();
    }

    // 准备数据
    const labels = categories.map(cat => {
        return cat.service_category_name.length > 8 ?
            cat.service_category_name.substring(0, 8) + '...' :
            cat.service_category_name;
    });

    const totalMethods = categories.map(cat => cat.total_methods);
    const availableMethods = categories.map(cat => cat.available_methods);

    // 计算初始可视范围和Y轴最大值
    const itemsPerView = calculateItemsPerView();
    currentVisibleRange = { start: 0, end: Math.min(itemsPerView, categories.length) };
    const initialMaxY = calculateVisibleMaxY(categories, currentVisibleRange);

    // 动态计算图表宽度
    const minWidth = Math.max(800, categories.length * 60);
    const chartContainer = document.getElementById('chart-scrollable-content');
    chartContainer.style.minWidth = minWidth + 'px';

    // 创建Y轴图表（固定显示）
    createYAxisChart(initialMaxY);

    // 创建主图表（可滚动内容）
    createMainChart(categories, labels, totalMethods, availableMethods, initialMaxY);

    // 添加滚动监听器
    setupScrollListener();
}

// 计算每个视图可显示的项目数量
function calculateItemsPerView() {
    const containerWidth = document.getElementById('chart-content-area').clientWidth;
    const itemWidth = 60; // 每个分类的大致宽度
    return Math.floor(containerWidth / itemWidth);
}

// 计算可视区域内的Y轴最大值
function calculateVisibleMaxY(categories, range) {
    let maxValue = 0;
    for (let i = range.start; i < range.end && i < categories.length; i++) {
        maxValue = Math.max(maxValue, categories[i].total_methods, categories[i].available_methods);
    }
    // 添加10%的缓冲空间
    return Math.ceil(maxValue * 1.1);
}

// 创建固定的Y轴图表
function createYAxisChart(maxY) {
    const ctx = document.getElementById('yAxisChart').getContext('2d');

    currentMaxY = maxY;

    yAxisChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [''],
            datasets: [{
                data: [0],
                borderColor: 'transparent',
                backgroundColor: 'transparent'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 400,
                easing: 'easeInOutQuart',
                onComplete: function() {
                    isAnimating = false;
                }
            },
            plugins: {
                legend: { display: false },
                title: { display: false },
                tooltip: { enabled: false }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '方法数量',
                        font: { size: 12 }
                    },
                    beginAtZero: true,
                    max: maxY,
                    grid: {
                        drawOnChartArea: false,
                        drawTicks: true
                    },
                    ticks: {
                        font: { size: 10 }
                    }
                }
            },
            elements: {
                point: { radius: 0 }
            }
        }
    });
}

// 创建主图表（可滚动内容）
function createMainChart(categories, labels, totalMethods, availableMethods, maxY) {
    const ctx = document.getElementById('categoriesChart').getContext('2d');

    categoriesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    type: 'bar',
                    label: '总方法数量',
                    data: totalMethods,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1,
                    yAxisID: 'y'
                },
                {
                    type: 'line',
                    label: '可用方法数量',
                    data: availableMethods,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.1,
                    pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    yAxisID: 'y'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 400,
                easing: 'easeInOutQuart',
                onComplete: function() {
                    isAnimating = false;
                }
            },
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                title: {
                    display: true,
                    text: '各分类方法数量统计'
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const index = context[0].dataIndex;
                            return categories[index].service_category_name;
                        },
                        afterBody: function(context) {
                            const index = context[0].dataIndex;
                            const total = categories[index].total_methods;
                            const available = categories[index].available_methods;
                            const rate = total > 0 ? ((available / total) * 100).toFixed(1) : '0.0';
                            return `可用率: ${rate}%`;
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '检测分类'
                    }
                },
                y: {
                    type: 'linear',
                    display: false, // 隐藏主图表的Y轴，使用固定的Y轴
                    position: 'left',
                    beginAtZero: true,
                    max: maxY
                }
            }
        }
    });
}

// 设置滚动监听器
function setupScrollListener() {
    const scrollContainer = document.getElementById('chart-content-area');

    scrollContainer.addEventListener('scroll', function() {
        // 如果正在动画中，不触发新的更新
        if (isAnimating) {
            return;
        }

        // 使用防抖来避免频繁更新，调整为更短的时间以提高响应性
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        scrollTimeout = setTimeout(() => {
            updateVisibleRange();
        }, 150);
    });
}

// 更新可视范围和Y轴刻度
function updateVisibleRange() {
    if (isAnimating) {
        return; // 如果正在动画中，跳过更新
    }

    const scrollContainer = document.getElementById('chart-content-area');
    const scrollLeft = scrollContainer.scrollLeft;
    const containerWidth = scrollContainer.clientWidth;
    const totalWidth = document.getElementById('chart-scrollable-content').scrollWidth;

    // 计算当前可视的分类索引范围
    const itemWidth = totalWidth / chartData.length;
    const startIndex = Math.floor(scrollLeft / itemWidth);
    const endIndex = Math.min(
        Math.ceil((scrollLeft + containerWidth) / itemWidth),
        chartData.length
    );

    // 更新可视范围
    const newRange = { start: startIndex, end: endIndex };

    // 如果范围发生变化，计算新的Y轴最大值
    if (newRange.start !== currentVisibleRange.start || newRange.end !== currentVisibleRange.end) {
        currentVisibleRange = newRange;
        const newMaxY = calculateVisibleMaxY(chartData, currentVisibleRange);

        // 只有当Y轴最大值变化超过阈值时才触发动画更新
        const changeThreshold = Math.max(currentMaxY * 0.1, 5); // 10%的变化或至少5个单位
        if (Math.abs(newMaxY - currentMaxY) > changeThreshold) {
            updateYAxisScale(newMaxY);
        }
    }
}

// 更新Y轴刻度（带平滑动画）
function updateYAxisScale(newMaxY) {
    if (!yAxisChart || !categoriesChart || isAnimating) {
        return;
    }

    // 设置动画状态
    isAnimating = true;
    currentMaxY = newMaxY;

    // 创建平滑的动画配置
    const animationConfig = {
        duration: 400,
        easing: 'easeInOutQuart'
    };

    // 更新固定Y轴图表（带动画）
    yAxisChart.options.scales.y.max = newMaxY;
    yAxisChart.update(animationConfig);

    // 更新主图表（带动画）
    categoriesChart.options.scales.y.max = newMaxY;
    categoriesChart.update(animationConfig);

    // 添加额外的动画完成回调
    setTimeout(() => {
        isAnimating = false;
    }, animationConfig.duration + 50); // 添加小缓冲时间
}

// 优化的可视范围计算（添加缓冲区）
function calculateOptimizedVisibleRange(scrollLeft, containerWidth, totalWidth, dataLength) {
    const itemWidth = totalWidth / dataLength;

    // 添加缓冲区，提前加载邻近的数据
    const bufferItems = 2;
    const startIndex = Math.max(0, Math.floor(scrollLeft / itemWidth) - bufferItems);
    const endIndex = Math.min(
        dataLength,
        Math.ceil((scrollLeft + containerWidth) / itemWidth) + bufferItems
    );

    return { start: startIndex, end: endIndex };
}

// 平滑的Y轴值计算（避免频繁的小幅变化）
function calculateSmoothedMaxY(categories, range) {
    let maxValue = 0;
    for (let i = range.start; i < range.end && i < categories.length; i++) {
        maxValue = Math.max(maxValue, categories[i].total_methods, categories[i].available_methods);
    }

    // 使用更智能的缓冲计算
    const buffer = Math.max(maxValue * 0.15, 10); // 15%缓冲或至少10个单位
    const smoothedMax = Math.ceil((maxValue + buffer) / 10) * 10; // 向上取整到10的倍数

    return smoothedMax;
}

// 检查是否需要更新Y轴（减少不必要的动画）
function shouldUpdateYAxis(currentMax, newMax) {
    const significantChangeThreshold = Math.max(currentMax * 0.2, 20); // 20%变化或至少20个单位
    return Math.abs(newMax - currentMax) > significantChangeThreshold;
}

// 刷新统计数据
function refreshStats() {
    showLoading('total-items', '...');
    showLoading('total-methods', '...');
    showLoading('total-records', '...');
    showLoading('recent-updates', '...');
    
    apiRequest('/api/get_statistics')
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                document.getElementById('total-items').textContent = stats.total_items || 0;
                document.getElementById('total-methods').textContent = stats.total_methods || 0;
                document.getElementById('total-records').textContent = stats.total_price_records || 0;

                // 更新项目统计进度条
                updateStatProgressBar('items', stats.total_items || 0, stats.locked_items || 0);

                // 更新方法统计进度条
                updateStatProgressBar('methods', stats.total_methods || 0, stats.locked_methods || 0);

                // 更新分类列表
                updateCategoriesList(stats.categories || []);
            } else {
                showError('price-changes-content', '获取统计数据失败: ' + data.message);
            }
        });
}

// 更新统计进度条
function updateStatProgressBar(type, total, locked) {
    const available = total - locked;
    const lockedPercentage = total > 0 ? (locked / total) * 100 : 0;
    const availablePercentage = total > 0 ? (available / total) * 100 : 100;

    // 更新进度条
    const availableBar = document.getElementById(`${type}-available-bar`);
    const lockedBar = document.getElementById(`${type}-locked-bar`);

    if (availableBar && lockedBar) {
        availableBar.style.width = availablePercentage + '%';
        lockedBar.style.width = lockedPercentage + '%';
    }

    // 更新数值显示
    const availableCount = document.getElementById(`${type}-available-count`);
    const lockedCount = document.getElementById(`${type}-locked-count`);
    const lockedPercentageElement = document.getElementById(`${type}-locked-percentage`);

    if (availableCount) {
        availableCount.textContent = available;
    }

    if (lockedCount) {
        lockedCount.textContent = locked;
    }

    if (lockedPercentageElement) {
        lockedPercentageElement.textContent = `锁定占比: ${lockedPercentage.toFixed(1)}%`;
    }
}

// 页面加载时初始化进度条
function initializeProgressBars() {
    // 从现有的统计数据初始化进度条
    const totalItems = parseInt(document.getElementById('total-items').textContent) || 0;
    const totalMethods = parseInt(document.getElementById('total-methods').textContent) || 0;

    // 从后端获取锁定数据
    apiRequest('/api/get_statistics')
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                updateStatProgressBar('items', stats.total_items || 0, stats.locked_items || 0);
                updateStatProgressBar('methods', stats.total_methods || 0, stats.locked_methods || 0);
            }
        })
        .catch(error => {
            console.error('初始化进度条失败:', error);
        });
}

// 当前显示的图表类型
let currentChartType = 'methodChange';

// 图表切换功能
function switchChart(chartType) {
    // 重置所有按钮样式
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.style.backgroundColor = '';
        btn.style.color = '#6c757d';
        btn.style.borderColor = '#dee2e6';
    });

    // 设置选中按钮样式
    const activeBtn = document.getElementById(chartType + 'Btn');
    if (activeBtn) {
        activeBtn.classList.add('active');
        activeBtn.style.backgroundColor = '#007bff';
        activeBtn.style.color = '#ffffff';
        activeBtn.style.borderColor = '#007bff';
    }

    // 更新描述文本
    const descriptions = {
        'methodChange': '统计指定时间范围内有价格变动的方法数量，点击数据点查看详细信息',
        'newMethods': '统计指定时间范围内新增的检测方法数量，基于抓取历史对比',
        'qualification': '统计指定时间范围内CMA/CNAS/NHC资质变更的方法数量',
        'priceChanges': '统计指定时间范围内分析价格、最低价格、采样价格发生变化的方法数量'
    };

    document.getElementById('chartDescription').innerHTML =
        `<i class="fas fa-info-circle me-1"></i>${descriptions[chartType]}`;

    currentChartType = chartType;

    // 隐藏所有图表容器
    document.getElementById('methodChangeChartContainer').style.display = 'none';
    document.getElementById('newMethodsChartContainer').style.display = 'none';
    document.getElementById('qualificationChangesChartContainer').style.display = 'none';
    document.getElementById('priceChangesChartContainer').style.display = 'none';

    // 显示选中的图表容器
    const containerMap = {
        'methodChange': 'methodChangeChartContainer',
        'newMethods': 'newMethodsChartContainer',
        'qualification': 'qualificationChangesChartContainer',
        'priceChanges': 'priceChangesChartContainer'
    };

    const targetContainer = document.getElementById(containerMap[chartType]);
    if (targetContainer) {
        targetContainer.style.display = 'block';

        // 智能懒加载：检查是否需要加载数据
        if (shouldLoadChartData(chartType, targetContainer)) {
            loadChartData(chartType);
        }
    }
}

// 判断是否需要加载图表数据
function shouldLoadChartData(chartType, container) {
    // 如果容器包含加载状态，说明还没有加载过
    if (container.querySelector('.chart-loading') || container.querySelector('.skeleton')) {
        return true;
    }

    // 如果是方法变动统计，总是需要加载（因为是默认显示的）
    if (chartType === 'methodChange') {
        return !container.querySelector('canvas');
    }

    // 对于其他图表，检查是否有预加载的数据
    if (window.preloadedChartData) {
        switch(chartType) {
            case 'newMethods':
                return !container.querySelector('canvas');
            case 'qualification':
                return !container.querySelector('canvas');
            case 'priceChanges':
                return !container.querySelector('canvas');
        }
    }

    return true;
}

// 加载指定类型的图表数据
function loadChartData(chartType) {
    const timeRange = document.getElementById('timeRangeSelect').value;
    const months = document.getElementById('monthsSelect').value;

    switch(chartType) {
        case 'methodChange':
            loadMethodChangeStatistics();
            break;
        case 'newMethods':
            if (window.preloadedChartData && window.preloadedChartData.newMethods) {
                // 使用预加载的数据
                displayNewMethodsChart(window.preloadedChartData.newMethods);
            } else {
                loadDetailedChangeStatistics();
            }
            break;
        case 'qualification':
            if (window.preloadedChartData && window.preloadedChartData.qualificationChanges) {
                // 使用预加载的数据
                displayQualificationChangesChart(window.preloadedChartData.qualificationChanges);
            } else {
                loadDetailedChangeStatistics();
            }
            break;
        case 'priceChanges':
            if (window.preloadedChartData && window.preloadedChartData.priceChanges) {
                // 使用预加载的数据
                displayPriceChangesChart(window.preloadedChartData.priceChanges);
            } else {
                loadDetailedChangeStatistics();
            }
            break;
    }
}

// 加载方法变动统计数据
function loadMethodChangeStatistics() {
    const timeRange = document.getElementById('timeRangeSelect').value;
    const months = document.getElementById('monthsSelect').value;
    const container = document.getElementById('methodChangeChartContainer');

    container.innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载方法变动统计数据中...</p>
        </div>
    `;

    apiRequest(`/api/get_method_change_statistics?time_range=${timeRange}&months=${months}`)
        .then(data => {
            if (data.success) {
                displayMethodChangeChart(data.data);
            } else {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        获取方法变动统计数据失败: ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    获取方法变动统计数据时出错: ${error.message}
                </div>
            `;
        });
}

// 显示方法变动统计图表
function displayMethodChangeChart(statisticsData) {
    const container = document.getElementById('methodChangeChartContainer');

    // 移除加载状态
    removeChartLoading(container);

    if (!statisticsData.statistics || statisticsData.statistics.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-chart-line fa-2x mb-3"></i>
                <p>暂无方法变动统计数据</p>
                <small>在选定的时间范围内没有发现方法变动</small>
            </div>
        `;
        return;
    }

    // 创建图表容器
    container.innerHTML = `
        <div style="height: 300px;">
            <canvas id="methodChangeChart"></canvas>
        </div>
    `;

    // 准备图表数据
    const labels = statisticsData.statistics.map(item => item.period);
    const data = statisticsData.statistics.map(item => item.method_count);

    // 创建Chart.js图表
    const ctx = document.getElementById('methodChangeChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '变动方法数量',
                data: data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return `${statisticsData.statistics[0].period_name}: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `变动方法数量: ${context.parsed.y} 个`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: statisticsData.statistics[0].period_name
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        // 根据数据点数量动态调整标签显示
                        maxTicksLimit: Math.min(15, Math.max(5, Math.ceil(labels.length / 5))),
                        maxRotation: 45,
                        minRotation: 0,
                        callback: function(value, index, values) {
                            const label = this.getLabelForValue(value);
                            // 对于按天统计，如果数据点太多，只显示部分标签
                            if (statisticsData.time_range === 'month' && labels.length > 30) {
                                // 每5天显示一个标签
                                return index % 5 === 0 ? label : '';
                            }
                            return label;
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '方法数量'
                    },
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const elementIndex = elements[0].index;
                    const clickedData = statisticsData.statistics[elementIndex];
                    // 跳转到分析方法界面，并应用时间范围筛选
                    jumpToAnalysisMethodsWithDateFilter(clickedData.period);
                }
            }
        }
    });
}



// 当前分页状态
let currentPage = 1;
let currentTab = 'recent'; // 'recent' 或 'baseline'

// 加载综合价格变动数据
function loadComprehensivePriceChanges(page = 1) {
    currentPage = page;

    showLoading('recent-changes-content', '加载近期价格变动数据...');
    showLoading('baseline-changes-content', '加载基准价格变动数据...');

    apiRequest(`/api/get_comprehensive_price_changes?days=7&page=${page}&per_page=20`)
        .then(data => {
            if (data.success) {
                displayRecentPriceChanges(data.data.recent_changes, data.data);
                displayBaselinePriceChanges(data.data.baseline_changes, data.data);

                // 更新计数徽章
                document.getElementById('recent-count').textContent = data.data.recent_total || 0;
                document.getElementById('baseline-count').textContent = data.data.baseline_total || 0;

                // 更新分页控件
                updatePagination('recent', data.data);
                updatePagination('baseline', data.data);
            } else {
                showError('recent-changes-content', '获取近期价格变动数据失败: ' + data.message);
                showError('baseline-changes-content', '获取基准价格变动数据失败: ' + data.message);
            }
        });
}

// 保持向后兼容的函数
function loadPriceChanges() {
    loadComprehensivePriceChanges();
}

// 显示近期价格变动数据
function displayRecentPriceChanges(changes, paginationData) {
    const container = document.getElementById('recent-changes-content');

    if (!changes || changes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <p>近7天没有发现近期价格变动</p>
                <small>近期变动：比较最近两次抓取的价格差异</small>
            </div>
        `;
        // 隐藏分页控件
        document.getElementById('recent-pagination').style.display = 'none';
        return;
    }

    displayPriceChangesTable(changes, container, 'recent');

    // 显示分页控件
    if (paginationData && paginationData.recent_total_pages > 1) {
        document.getElementById('recent-pagination').style.display = 'flex';
    } else {
        document.getElementById('recent-pagination').style.display = 'none';
    }
}

// 显示基准价格变动数据
function displayBaselinePriceChanges(changes, paginationData) {
    const container = document.getElementById('baseline-changes-content');

    if (!changes || changes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-chart-line fa-2x mb-3"></i>
                <p>没有发现基准价格变动</p>
                <small>基准变动：比较当前价格与基准价格，需至少2次抓取记录</small>
            </div>
        `;
        // 隐藏工具栏和分页控件
        document.getElementById('baseline-toolbar').style.display = 'none';
        document.getElementById('baseline-pagination').style.display = 'none';
        return;
    }

    displayPriceChangesTable(changes, container, 'baseline');

    // 显示工具栏
    document.getElementById('baseline-toolbar').style.display = 'block';

    // 显示分页控件
    if (paginationData && paginationData.baseline_total_pages > 1) {
        document.getElementById('baseline-pagination').style.display = 'flex';
    } else {
        document.getElementById('baseline-pagination').style.display = 'none';
    }
}

// 通用价格变动表格显示函数
function displayPriceChangesTable(changes, container, changeType) {
    let html = '<div class="table-responsive"><table class="table table-hover table-sm table-bordered" style="font-size: 0.95rem;">';

    // 为基准变动表格添加多选列
    if (changeType === 'baseline') {
        html += `
            <thead class="table-light">
                <tr>
                    <th rowspan="2" width="3%" class="align-middle text-center">
                        <input type="checkbox" id="select-all-baseline" onchange="toggleSelectAll(this)">
                    </th>
                    <th rowspan="2" width="5%" class="align-middle text-center">项目编号</th>
                    <th rowspan="2" width="4%" class="align-middle text-center">方法ID</th>
                    <th rowspan="2" width="12%" class="align-middle text-center">检测项目</th>
                    <th rowspan="2" width="13%" class="align-middle text-center">检测方法</th>
                    <th colspan="3" width="18%" class="text-center border-end">分析价格</th>
                    <th colspan="3" width="18%" class="text-center border-end">最低价格</th>
                    <th colspan="4" width="24%" class="text-center">采样价格</th>
                </tr>
                <tr>
                    <th width="6%" class="text-center">当前</th>
                    <th width="6%" class="text-center">基准</th>
                    <th width="6%" class="text-center border-end">变动</th>
                    <th width="6%" class="text-center">当前</th>
                    <th width="6%" class="text-center">基准</th>
                    <th width="6%" class="text-center border-end">变动</th>
                    <th width="6%" class="text-center">类型</th>
                    <th width="6%" class="text-center">当前</th>
                    <th width="6%" class="text-center">基准</th>
                    <th width="6%" class="text-center">变动</th>
                </tr>
            </thead>
        `;
    } else {
        html += `
            <thead class="table-light">
                <tr>
                    <th rowspan="2" width="5%" class="align-middle text-center">项目编号</th>
                    <th rowspan="2" width="4%" class="align-middle text-center">方法ID</th>
                    <th rowspan="2" width="12%" class="align-middle text-center">检测项目</th>
                    <th rowspan="2" width="15%" class="align-middle text-center">检测方法</th>
                    <th colspan="3" width="18%" class="text-center border-end">分析价格</th>
                    <th colspan="3" width="18%" class="text-center border-end">最低价格</th>
                    <th colspan="4" width="28%" class="text-center">采样价格</th>
                </tr>
                <tr>
                    <th width="6%" class="text-center">当前</th>
                    <th width="6%" class="text-center">历史</th>
                    <th width="6%" class="text-center border-end">变动</th>
                    <th width="6%" class="text-center">当前</th>
                    <th width="6%" class="text-center">历史</th>
                    <th width="6%" class="text-center border-end">变动</th>
                    <th width="7%" class="text-center">类型</th>
                    <th width="7%" class="text-center">当前</th>
                    <th width="7%" class="text-center">历史</th>
                    <th width="7%" class="text-center">变动</th>
                </tr>
            </thead>
        `;
    }

    html += '<tbody>';

    changes.forEach(change => {
        // 跳过数据不完整的记录
        if (!change.service_item_name || !change.check_method_name ||
            change.service_item_name === 'null' || change.check_method_name === 'null') {
            return;
        }

        // 准备分析价格数据
        let analysisCurrent = '', analysisHistorical = '', analysisChange = '';
        if (change.analysis_price_change) {
            const ac = change.analysis_price_change;
            const direction = ac.change > 0 ? 'up' : 'down';
            const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
            const changeText = ac.change > 0 ? `+${ac.change.toFixed(2)}` : ac.change.toFixed(2);
            const percentText = ac.change_percent > 0 ? `+${ac.change_percent.toFixed(1)}%` : `${ac.change_percent.toFixed(1)}%`;

            analysisCurrent = `${ac.current.toFixed(2)}元`;
            // 为基准变动添加手动基准标识
            if (changeType === 'baseline' && ac.is_manual_baseline) {
                analysisHistorical = `${ac.previous.toFixed(2)}元<br><small class="text-info"><i class="fas fa-anchor"></i>手动基准</small>`;
            } else {
                analysisHistorical = `${ac.previous.toFixed(2)}元`;
            }
            analysisChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
        } else {
            analysisCurrent = `${(change.current_analysis_price || 0).toFixed(2)}元`;
            // 显示实际的历史价格，优先使用后端提供的历史价格信息
            const previousAnalysisPrice = change.previous_analysis_price !== undefined ?
                change.previous_analysis_price : (change.current_analysis_price || 0);
            analysisHistorical = `${previousAnalysisPrice.toFixed(2)}元`;
            analysisChange = '<span class="text-muted">无变动</span>';
        }

        // 准备最低价格数据
        let currentLowestPrice = change.current_lowest_price || 0;
        let currentAnalysisPrice = change.current_analysis_price || 0;

        if (currentLowestPrice === 0 && currentAnalysisPrice > 0) {
            currentLowestPrice = currentAnalysisPrice;
        }

        let lowestCurrent = '', lowestHistorical = '', lowestChange = '';
        if (change.lowest_price_change && change.lowest_price_change.change !== 0) {
            const lc = change.lowest_price_change;
            const direction = lc.change > 0 ? 'up' : 'down';
            const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
            const changeText = lc.change > 0 ? `+${lc.change.toFixed(2)}` : lc.change.toFixed(2);
            const percentText = lc.change_percent > 0 ? `+${lc.change_percent.toFixed(1)}%` : `${lc.change_percent.toFixed(1)}%`;

            lowestCurrent = `${lc.current.toFixed(2)}元`;
            lowestHistorical = `${lc.previous.toFixed(2)}元`;
            lowestChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
        } else {
            lowestCurrent = `${currentLowestPrice.toFixed(2)}元`;
            // 显示实际的历史价格，优先使用后端提供的历史价格信息
            const previousLowestPrice = change.previous_lowest_price !== undefined ?
                change.previous_lowest_price : currentLowestPrice;
            lowestHistorical = `${previousLowestPrice.toFixed(2)}元`;
            lowestChange = '<span class="text-muted">无变动</span>';
        }

        // 处理采样价格 - 每个采样类型一行
        const sampleChanges = change.sample_price_changes || [];
        const deletedSamples = change.deleted_sample_types || [];

        // 合并采样类型，避免重复（优先使用sample_price_changes中的数据）
        const sampleTypeMap = new Map();

        // 先添加正常的采样价格变动
        sampleChanges.forEach(sample => {
            sampleTypeMap.set(sample.type, sample);
        });

        // 再添加删除的采样类型（如果不在变动列表中）
        deletedSamples.forEach(sample => {
            if (!sampleTypeMap.has(sample.type)) {
                sampleTypeMap.set(sample.type, sample);
            }
        });

        const allSamples = Array.from(sampleTypeMap.values());
        const totalRows = Math.max(1, allSamples.length);

        for (let i = 0; i < totalRows; i++) {
            const isFirstRow = i === 0;
            const sample = allSamples[i];

            let sampleType = '', sampleCurrent = '', sampleHistorical = '', sampleChange = '';
            if (sample) {
                if (sample.status === 'deleted') {
                    // 删除的采样类型
                    sampleType = `<span class="badge bg-danger">${sample.type}</span>`;
                    sampleCurrent = '<span class="text-danger">已删除</span>';
                    sampleHistorical = `${(sample.previous || sample.last_price || 0).toFixed(2)}元`;
                    sampleChange = '<i class="fas fa-times text-danger"></i> 删除';
                } else if (sample.price_change) {
                    // 有价格变动的采样类型
                    const sc = sample.price_change;
                    const direction = sc.change > 0 ? 'up' : 'down';
                    const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
                    const changeText = sc.change > 0 ? `+${sc.change.toFixed(2)}` : sc.change.toFixed(2);
                    const percentText = sc.change_percent > 0 ? `+${sc.change_percent.toFixed(1)}%` : `${sc.change_percent.toFixed(1)}%`;

                    sampleType = `<span class="badge bg-primary-subtle text-primary">${sample.type}</span>`;
                    sampleCurrent = `${sc.current.toFixed(2)}元`;
                    sampleHistorical = `${sc.previous.toFixed(2)}元`;
                    sampleChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
                } else {
                    // 无变动的采样类型
                    sampleType = `<span class="badge bg-secondary-subtle text-secondary">${sample.type}</span>`;
                    sampleCurrent = `${sample.current_price.toFixed(2)}元`;
                    // 对于无变动的采样类型，历史价格等于当前价格
                    sampleHistorical = `${sample.current_price.toFixed(2)}元`;
                    sampleChange = '<span class="text-muted">无变动</span>';
                }
            } else if (isFirstRow) {
                sampleType = '<span class="text-muted">无采样价格</span>';
                sampleCurrent = '-';
                sampleHistorical = '-';
                sampleChange = '-';
            }

            html += `
                <tr>
                    ${isFirstRow ? `
                        ${changeType === 'baseline' ? `
                            <td rowspan="${totalRows}" class="text-center align-middle">
                                <input type="checkbox" class="baseline-method-checkbox"
                                       value="${change.check_method_id}"
                                       onchange="updateSelectedCount()">
                            </td>
                        ` : ''}
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <code class="small">${change.service_item_number || 'N/A'}</code>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <code class="small">${formatMethodId(change.method_no)}</code>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <div class="fw-bold">${change.service_item_name}</div>
                            <small class="text-muted d-block">${change.service_category_name || ''}</small>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <span class="small method-name-clickable"
                                  title="${change.check_method_name}"
                                  data-method-id="${change.check_method_id}"
                                  data-method-name="${change.check_method_name}"
                                  data-project-name="${change.service_item_name}"
                                  style="cursor: pointer; color: #0d6efd; text-decoration: underline;">
                                ${change.check_method_name.length > 25 ?
                                  change.check_method_name.substring(0, 25) + '...' :
                                  change.check_method_name}
                            </span>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${analysisCurrent}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${analysisHistorical}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle border-end">${analysisChange}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${lowestCurrent}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${lowestHistorical}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle border-end">${lowestChange}</td>
                    ` : ''}
                    <td class="text-center">${sampleType}</td>
                    <td class="text-center">${sampleCurrent}</td>
                    <td class="text-center">${sampleHistorical}</td>
                    <td class="text-center">${sampleChange}</td>
                </tr>
            `;
        }
    });

    html += '</tbody></table></div>';

    if (changes.length > 10) {
        const changeTypeText = changeType === 'recent' ? '近期变动' : '基准变动';
        html += `
            <div class="text-center mt-3">
                <small class="text-muted">显示前10个${changeTypeText}，共${changes.length}个变动</small>
            </div>
        `;
    }

    container.innerHTML = html;
}

// 显示详细价格变动数据
function displayDetailedPriceChanges(changes) {
    const container = document.getElementById('price-changes-content');

    if (!changes || changes.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p>近7天没有发现价格变动</p>
            </div>
        `;
        return;
    }

    // 只显示前10个方法
    const topChanges = changes.slice(0, 10);

    let html = '<div class="table-responsive"><table class="table table-hover table-sm table-bordered" style="font-size: 0.95rem;">';
    html += `
        <thead class="table-light">
            <tr>
                <th rowspan="2" width="5%" class="align-middle text-center">项目编号</th>
                <th rowspan="2" width="4%" class="align-middle text-center">方法ID</th>
                <th rowspan="2" width="13%" class="align-middle text-center">检测项目</th>
                <th rowspan="2" width="16%" class="align-middle text-center">检测方法</th>
                <th colspan="3" width="18%" class="text-center border-end">分析价格</th>
                <th colspan="3" width="18%" class="text-center border-end">最低价格</th>
                <th colspan="3" width="21%" class="text-center">采样价格</th>
            </tr>
            <tr>
                <th width="6%" class="text-center">当前</th>
                <th width="6%" class="text-center">历史</th>
                <th width="6%" class="text-center border-end">变动</th>
                <th width="6%" class="text-center">当前</th>
                <th width="6%" class="text-center">历史</th>
                <th width="6%" class="text-center border-end">变动</th>
                <th width="7%" class="text-center">类型</th>
                <th width="7%" class="text-center">当前</th>
                <th width="7%" class="text-center">变动</th>
            </tr>
        </thead>
        <tbody>
    `;

    topChanges.forEach(change => {
        // 跳过数据不完整的记录
        if (!change.service_item_name || !change.check_method_name ||
            change.service_item_name === 'null' || change.check_method_name === 'null') {
            return;
        }

        // 准备分析价格数据
        let analysisCurrent = '', analysisHistorical = '', analysisChange = '';
        if (change.analysis_price_change) {
            const ac = change.analysis_price_change;
            const direction = ac.change > 0 ? 'up' : 'down';
            const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
            const changeText = ac.change > 0 ? `+${ac.change.toFixed(2)}` : ac.change.toFixed(2);
            const percentText = ac.change_percent > 0 ? `+${ac.change_percent.toFixed(1)}%` : `${ac.change_percent.toFixed(1)}%`;

            analysisCurrent = `${ac.current.toFixed(2)}元`;
            analysisHistorical = `${ac.previous.toFixed(2)}元`;
            analysisChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
        } else {
            analysisCurrent = `${(change.current_analysis_price || 0).toFixed(2)}元`;
            analysisHistorical = '-';
            analysisChange = '<span class="text-muted">无变动</span>';
        }

        // 准备最低价格数据
        let currentLowestPrice = change.current_lowest_price || 0;
        let currentAnalysisPrice = change.current_analysis_price || 0;

        if (currentLowestPrice === 0 && currentAnalysisPrice > 0) {
            currentLowestPrice = currentAnalysisPrice;
        }

        let lowestCurrent = '', lowestHistorical = '', lowestChange = '';
        if (change.lowest_price_change && change.lowest_price_change.change !== 0) {
            const lc = change.lowest_price_change;
            const direction = lc.change > 0 ? 'up' : 'down';
            const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
            const changeText = lc.change > 0 ? `+${lc.change.toFixed(2)}` : lc.change.toFixed(2);
            const percentText = lc.change_percent > 0 ? `+${lc.change_percent.toFixed(1)}%` : `${lc.change_percent.toFixed(1)}%`;

            lowestCurrent = `${lc.current.toFixed(2)}元`;
            lowestHistorical = `${lc.previous.toFixed(2)}元`;
            lowestChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
        } else {
            lowestCurrent = `${currentLowestPrice.toFixed(2)}元`;
            lowestHistorical = '-';
            lowestChange = '<span class="text-muted">无变动</span>';
        }

        // 处理采样价格 - 每个采样类型一行（包括删除的）
        const sampleChanges = change.sample_price_changes || [];
        const deletedSamples = change.deleted_sample_types || [];

        // 合并采样类型，避免重复（优先使用sample_price_changes中的数据）
        const sampleTypeMap = new Map();

        // 先添加正常的采样价格变动
        sampleChanges.forEach(sample => {
            sampleTypeMap.set(sample.type, sample);
        });

        // 再添加删除的采样类型（如果不在变动列表中）
        deletedSamples.forEach(sample => {
            if (!sampleTypeMap.has(sample.type)) {
                sampleTypeMap.set(sample.type, sample);
            }
        });

        const allSamples = Array.from(sampleTypeMap.values());
        const totalRows = Math.max(1, allSamples.length);

        for (let i = 0; i < totalRows; i++) {
            const isFirstRow = i === 0;
            const sample = allSamples[i];

            let sampleType = '', sampleCurrent = '', sampleChange = '';
            if (sample) {
                if (sample.status === 'deleted') {
                    // 处理已删除的采样类型
                    sampleType = `<span class="badge bg-danger">${sample.type} (已删除)</span>`;
                    sampleCurrent = '<span class="text-danger">0元</span>';
                    sampleChange = `<i class="fas fa-times text-danger"></i> -${sample.previous.toFixed(2)}元<br><small>(-100%)</small>`;
                } else if (sample.price_change) {
                    // 处理价格变动的采样类型
                    const sc = sample.price_change;
                    const direction = sc.change > 0 ? 'up' : 'down';
                    const iconClass = direction === 'up' ? 'fas fa-arrow-up text-danger' : 'fas fa-arrow-down text-success';
                    const changeText = sc.change > 0 ? `+${sc.change.toFixed(2)}` : sc.change.toFixed(2);
                    const percentText = sc.change_percent > 0 ? `+${sc.change_percent.toFixed(1)}%` : `${sc.change_percent.toFixed(1)}%`;

                    sampleType = `<span class="badge bg-primary-subtle text-primary">${sample.type}</span>`;
                    sampleCurrent = `${sc.current.toFixed(2)}元`;
                    sampleChange = `<i class="${iconClass}"></i> ${changeText}元<br><small>(${percentText})</small>`;
                }
            } else if (isFirstRow) {
                sampleType = '<span class="text-muted">无变动</span>';
                sampleCurrent = '-';
                sampleChange = '-';
            }

            html += `
                <tr>
                    ${isFirstRow ? `
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <code class="small">${change.service_item_number || 'N/A'}</code>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <code class="small">${formatMethodId(change.method_no)}</code>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <div class="fw-bold">${change.service_item_name}</div>
                            <small class="text-muted d-block">${change.service_category_name || ''}</small>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">
                            <span class="small" title="${change.check_method_name}">
                                ${change.check_method_name.length > 25 ?
                                  change.check_method_name.substring(0, 25) + '...' :
                                  change.check_method_name}
                            </span>
                        </td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${analysisCurrent}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${analysisHistorical}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle border-end">${analysisChange}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${lowestCurrent}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle">${lowestHistorical}</td>
                        <td rowspan="${totalRows}" class="text-center align-middle border-end">${lowestChange}</td>
                    ` : ''}
                    <td class="text-center">${sampleType}</td>
                    <td class="text-center">${sampleCurrent}</td>
                    <td class="text-center">${sampleChange}</td>
                </tr>
            `;
        }
    });

    html += '</tbody></table></div>';

    if (changes.length > 10) {
        html += `
            <div class="text-center mt-3">
                <small class="text-muted">显示前10个变动，共${changes.length}个变动</small>
                <br>
                <a href="${url_for('data_search')}" class="btn btn-outline-primary btn-sm mt-2">
                    查看全部变动
                </a>
            </div>
        `;
    }

    container.innerHTML = html;
}

// 显示价格变动数据（保留原有函数以防其他地方使用）
function displayPriceChanges(changes) {
    // 调用新的详细显示函数
    displayDetailedPriceChanges(changes);
}

// 格式化方法ID，只显示后两位
function formatMethodId(methodNo) {
    if (!methodNo) return '-';
    const methodStr = methodNo.toString();
    return methodStr.length >= 2 ? methodStr.slice(-2) : methodStr;
}

// 更新分类列表
function updateCategoriesList(categories) {
    const container = document.getElementById('categories-list');
    
    if (!categories || categories.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无分类数据</p>';
        return;
    }
    
    let html = '';
    categories.slice(0, 5).forEach(category => {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${category.service_category_name}</span>
                <div>
                    <span class="badge bg-primary me-2">${category.count} 个项目</span>
                    ${category.locked_count > 0 ? `<span class="badge bg-danger">${category.locked_count} 个锁定</span>` : ''}
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 显示价格趋势图
function showPriceTrend(methodId, methodName, projectName) {
    // 设置弹窗标题
    document.getElementById('priceTrendModalTitle').innerHTML =
        `<i class="fas fa-chart-line me-2"></i>${escapeHtml(methodName)} - 价格趋势图`;

    // 显示加载状态
    document.getElementById('priceTrendContent').innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载价格趋势数据中...</p>
        </div>
    `;

    // 显示弹窗
    new bootstrap.Modal(document.getElementById('priceTrendModal')).show();

    // 获取价格趋势数据
    apiRequest(`/api/method_price_trend?method_id=${methodId}`)
        .then(data => {
            if (data.success) {
                displayPriceTrend(data.data, methodName, projectName);
            } else {
                document.getElementById('priceTrendContent').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || '获取价格趋势数据失败'}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('priceTrendContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    获取价格趋势数据时出错: ${error.message}
                </div>
            `;
        });
}

// 显示价格趋势图表
function displayPriceTrend(trendData, methodName, projectName) {
    const container = document.getElementById('priceTrendContent');

    // 保存当前趋势数据到全局变量，供表格函数使用
    window.currentTrendData = trendData;

    if (!trendData.price_history || trendData.price_history.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                该方法暂无价格历史记录
            </div>
        `;
        return;
    }

    // 获取所有采样类型
    let allSampleTypes = [];
    if (trendData.all_sample_types) {
        allSampleTypes = trendData.all_sample_types;
    } else {
        const sampleTypesSet = new Set();
        trendData.price_history.forEach(record => {
            if (record.sample_prices) {
                Object.keys(record.sample_prices).forEach(type => sampleTypesSet.add(type));
            }
        });
        allSampleTypes = Array.from(sampleTypesSet);
    }

    // 创建图表容器
    container.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>价格趋势图
                            <small class="text-muted ms-2">${escapeHtml(projectName)} - ${escapeHtml(methodName)}</small>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div style="height: 400px;">
                            <canvas id="priceTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-table me-2"></i>价格历史记录
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped column-divider" id="priceHistoryTable" style="font-size: 0.95rem;">
                                <thead class="table-primary">
                                    <tr>
                                        <th class="text-center fw-bold">抓取时间</th>
                                        <th class="text-center fw-bold">分析价格</th>
                                        <th class="text-center fw-bold">最低价格</th>
                                        ${allSampleTypes.map(type => `<th class="text-center fw-bold">采样价格<br><small>(${escapeHtml(type)})</small></th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody id="priceHistoryTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 准备图表数据
    const labels = trendData.price_history.map(record => new Date(record.fetch_time).toLocaleString('zh-CN'));
    const datasets = [];
    const colors = ['#007bff', '#dc3545', '#28a745', '#ffc107', '#17a2b8', '#6f42c1'];

    // 1. 分析价格
    datasets.push({
        label: '分析价格',
        data: trendData.price_history.map(record => record.check_method_price),
        borderColor: colors[0],
        backgroundColor: colors[0] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true
    });

    // 2. 最低价格（基于当前方法状态决定逻辑）
    // 检查当前方法的最低价格状态
    const currentMethodInfo = trendData.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    datasets.push({
        label: '最低价格',
        data: trendData.price_history.map(record => {
            if (shouldUseAnalysisPrice) {
                // 当前方法的最低价格已被删除，整个历史都使用分析价格
                if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                    return record.check_method_price;
                } else {
                    return null;
                }
            } else {
                // 当前方法有最低价格，使用历史记录中的实际最低价格，null时回退到分析价格
                if (record.lowest_bid === null || record.lowest_bid === undefined || record.lowest_bid === 0) {
                    if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                        return record.check_method_price;
                    } else {
                        return null;
                    }
                }
                return record.lowest_bid;
            }
        }),
        borderColor: colors[1],
        backgroundColor: colors[1] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true // 连接空值点
    });

    // 3. 采样价格
    let colorIndex = 2;
    allSampleTypes.forEach(type => {
        datasets.push({
            label: `采样价格 - ${type}`,
            data: trendData.price_history.map(record => (record.sample_prices ? record.sample_prices[type] : null)),
            borderColor: colors[colorIndex % colors.length],
            backgroundColor: colors[colorIndex % colors.length] + '33',
            fill: false,
            tension: 0.1,
            spanGaps: true
        });
        colorIndex++;
    });

    // 绘制图表
    const ctx = document.getElementById('priceTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '抓取时间'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '价格 (元)'
                    },
                    beginAtZero: false
                }
            },
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // 填充价格历史表格
    fillPriceHistoryTable(trendData.price_history, allSampleTypes);
}

// 填充价格历史表格
function fillPriceHistoryTable(priceHistory, sampleTypes) {
    const tbody = document.getElementById('priceHistoryTableBody');

    // 获取当前方法的最低价格状态（从全局变量或数据中获取）
    const currentMethodInfo = window.currentTrendData?.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    let bodyHtml = '';
    [...priceHistory].reverse().forEach(record => {
        let rowHtml = `<tr><td class="text-center">${new Date(record.fetch_time).toLocaleString('zh-CN')}</td>`;

        const analysisPrice = record.check_method_price !== null && record.check_method_price !== undefined ? parseFloat(record.check_method_price).toFixed(2) + '元' : '-';
        rowHtml += `<td class="text-center fw-bold text-primary">${analysisPrice}</td>`;

        // 基于当前方法状态决定最低价格显示逻辑
        let lowestPriceDisplay;
        if (shouldUseAnalysisPrice) {
            // 当前方法的最低价格已被删除，整个历史都使用分析价格
            if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                lowestPriceDisplay = parseFloat(record.check_method_price).toFixed(2) + '元';
            } else {
                lowestPriceDisplay = '-';
            }
        } else {
            // 当前方法有最低价格，使用历史记录中的实际最低价格，null时回退到分析价格
            if (record.lowest_bid === null || record.lowest_bid === undefined || record.lowest_bid === 0) {
                if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                    lowestPriceDisplay = parseFloat(record.check_method_price).toFixed(2) + '元';
                } else {
                    lowestPriceDisplay = '-';
                }
            } else {
                lowestPriceDisplay = parseFloat(record.lowest_bid).toFixed(2) + '元';
            }
        }
        rowHtml += `<td class="text-center text-danger">${lowestPriceDisplay}</td>`;

        sampleTypes.forEach(type => {
            const samplePrice = (record.sample_prices && record.sample_prices[type] !== null && record.sample_prices[type] !== undefined) ? parseFloat(record.sample_prices[type]).toFixed(2) + '元' : '-';
            if (record.sample_prices && record.sample_prices[type] === 0) {
                rowHtml += `<td class="text-center text-danger"><del>0元</del></td>`;
            } else {
                rowHtml += `<td class="text-center">${samplePrice}</td>`;
            }
        });

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    tbody.innerHTML = bodyHtml;
}

// HTML转义函数
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// API请求函数
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 刷新所有统计数据
function refreshAllStatistics() {
    // 根据统计维度自动调整默认月数
    updateDefaultMonths();

    // 重新加载所有图表数据
    loadMethodChangeStatistics();
    loadDetailedChangeStatistics();
}

// 根据统计维度更新默认月数
function updateDefaultMonths() {
    const timeRange = document.getElementById('timeRangeSelect').value;
    const monthsSelect = document.getElementById('monthsSelect');

    // 如果用户没有手动选择过，则根据统计维度设置默认值
    if (!monthsSelect.hasAttribute('data-user-selected')) {
        let defaultMonths;
        switch(timeRange) {
            case 'month':  // 按天统计
                defaultMonths = '3';
                break;
            case 'quarter': // 按周统计
                defaultMonths = '6';
                break;
            case 'year':   // 按月统计
                defaultMonths = '12';
                break;
            default:
                defaultMonths = '3';
        }
        monthsSelect.value = defaultMonths;
    }
}

// 监听月数选择器的变化，标记为用户手动选择
document.addEventListener('DOMContentLoaded', function() {
    const monthsSelect = document.getElementById('monthsSelect');
    monthsSelect.addEventListener('change', function() {
        this.setAttribute('data-user-selected', 'true');
    });

    // 启动渐进式图表加载
    startProgressiveChartLoading();
});

// 渐进式图表加载
function startProgressiveChartLoading() {
    // 延迟加载，避免阻塞页面渲染
    setTimeout(() => {
        // 第一优先级：方法变动统计（默认显示的图表）
        loadMethodChangeStatistics();

        // 第二优先级：详细变动统计（延迟加载）
        setTimeout(() => {
            loadDetailedChangeStatisticsBackground();
        }, 1000);
    }, 100);
}

// 后台加载详细变动统计数据（不显示加载状态）
function loadDetailedChangeStatisticsBackground() {
    const timeRange = document.getElementById('timeRangeSelect').value;
    const months = document.getElementById('monthsSelect').value;

    apiRequest(`/api/get_detailed_change_statistics?time_range=${timeRange}&months=${months}`)
        .then(data => {
            if (data.success) {
                // 保存统计数据到全局变量
                window.currentStatisticsData = data.data;

                // 预渲染图表数据（但不显示）
                window.preloadedChartData = {
                    newMethods: data.data.new_methods,
                    qualificationChanges: data.data.qualification_changes,
                    priceChanges: data.data.price_changes
                };

                console.log('详细统计数据预加载完成');
            } else {
                console.warn('预加载详细统计数据失败:', data.message);
            }
        })
        .catch(error => {
            console.warn('预加载详细统计数据时出错:', error);
        });
}

// 加载详细变动统计数据
function loadDetailedChangeStatistics() {
    const timeRange = document.getElementById('timeRangeSelect').value;
    const months = document.getElementById('monthsSelect').value;

    // 显示加载状态
    showChartLoading('newMethodsChartContainer', '加载新增方法统计数据中...');
    showChartLoading('qualificationChangesChartContainer', '加载资质变更统计数据中...');
    showChartLoading('priceChangesChartContainer', '加载价格变更统计数据中...');

    apiRequest(`/api/get_detailed_change_statistics?time_range=${timeRange}&months=${months}`)
        .then(data => {
            if (data.success) {
                // 保存统计数据到全局变量，供跳转时使用
                window.currentStatisticsData = data.data;

                displayNewMethodsChart(data.data.new_methods);
                displayQualificationChangesChart(data.data.qualification_changes);
                displayPriceChangesChart(data.data.price_changes);
            } else {
                showChartError('newMethodsChartContainer', '获取新增方法统计数据失败: ' + data.message);
                showChartError('qualificationChangesChartContainer', '获取资质变更统计数据失败: ' + data.message);
                showChartError('priceChangesChartContainer', '获取价格变更统计数据失败: ' + data.message);
            }
        })
        .catch(error => {
            showChartError('newMethodsChartContainer', '获取新增方法统计数据时出错: ' + error.message);
            showChartError('qualificationChangesChartContainer', '获取资质变更统计数据时出错: ' + error.message);
            showChartError('priceChangesChartContainer', '获取价格变更统计数据时出错: ' + error.message);
        });
}

// 显示图表加载状态
function showChartLoading(containerId, message) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="chart-loading">
                <div class="skeleton skeleton-chart"></div>
                <div class="loading-progress">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">${message}</div>
                </div>
            </div>
        `;
    }
}

// 移除图表加载状态
function removeChartLoading(container) {
    const loadingElement = container.querySelector('.chart-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

// 显示图表错误状态
function showChartError(containerId, message) {
    const container = document.getElementById(containerId);
    if (container) {
        // 先移除加载状态
        removeChartLoading(container);

        container.innerHTML = `
            <div class="text-center text-danger p-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>${message}</p>
                <button class="btn btn-outline-primary btn-sm mt-2" onclick="retryLoadChart('${containerId}')">
                    <i class="fas fa-redo me-1"></i>重试
                </button>
            </div>
        `;
    }
}

// 重试加载图表
function retryLoadChart(containerId) {
    // 根据容器ID确定图表类型
    const chartTypeMap = {
        'methodChangeChartContainer': 'methodChange',
        'newMethodsChartContainer': 'newMethods',
        'qualificationChangesChartContainer': 'qualification',
        'priceChangesChartContainer': 'priceChanges'
    };

    const chartType = chartTypeMap[containerId];
    if (chartType) {
        // 清除预加载数据，强制重新加载
        if (window.preloadedChartData) {
            delete window.preloadedChartData;
        }

        // 显示加载状态并重新加载
        showChartLoading(containerId, '正在重新加载数据...');
        loadChartData(chartType);
    }
}

// 显示新增方法统计图表
function displayNewMethodsChart(newMethodsData) {
    const container = document.getElementById('newMethodsChartContainer');

    // 移除加载状态
    removeChartLoading(container);

    if (!newMethodsData || newMethodsData.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-plus fa-2x mb-3"></i>
                <p>暂无新增方法统计数据</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `<div style="height: 300px;"><canvas id="newMethodsChart"></canvas></div>`;

    const labels = newMethodsData.map(item => item.period);
    const data = newMethodsData.map(item => item.count);

    const ctx = document.getElementById('newMethodsChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '新增方法数量',
                data: data,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#28a745',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: true, position: 'top' },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return `${newMethodsData[0].period_name}: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `新增方法数量: ${context.parsed.y} 个`;
                        }
                    }
                }
            },
            scales: {
                x: { title: { display: true, text: newMethodsData[0]?.period_name || '时间' } },
                y: { title: { display: true, text: '方法数量' }, beginAtZero: true, ticks: { stepSize: 1 } }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const elementIndex = elements[0].index;
                    const clickedData = newMethodsData[elementIndex];
                    jumpToAnalysisMethodsWithChangeFilter(clickedData.period, 'new_methods');
                }
            }
        }
    });
}

// 显示资质变更统计图表
function displayQualificationChangesChart(qualificationData) {
    const container = document.getElementById('qualificationChangesChartContainer');

    // 移除加载状态
    removeChartLoading(container);

    const hasData = qualificationData.cma.length > 0 ||
                   qualificationData.cnas.length > 0 ||
                   qualificationData.nhc.length > 0;

    if (!hasData) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-certificate fa-2x mb-3"></i>
                <p>暂无资质变更统计数据</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `<div style="height: 300px;"><canvas id="qualificationChangesChart"></canvas></div>`;

    // 合并所有时间点
    const allPeriods = new Set();
    qualificationData.cma.forEach(item => allPeriods.add(item.period));
    qualificationData.cnas.forEach(item => allPeriods.add(item.period));
    qualificationData.nhc.forEach(item => allPeriods.add(item.period));
    const labels = Array.from(allPeriods).sort();

    // 构建数据集
    function buildDataset(data, label, color) {
        const dataMap = {};
        data.forEach(item => dataMap[item.period] = item.count);
        return {
            label: label,
            data: labels.map(period => dataMap[period] || 0),
            borderColor: color,
            backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
            borderWidth: 3,
            pointBackgroundColor: color,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 5,
            fill: false,
            tension: 0.4
        };
    }

    const datasets = [];
    if (qualificationData.cma.length > 0) {
        datasets.push(buildDataset(qualificationData.cma, 'CMA变更', 'rgb(255, 99, 132)'));
    }
    if (qualificationData.cnas.length > 0) {
        datasets.push(buildDataset(qualificationData.cnas, 'CNAS变更', 'rgb(54, 162, 235)'));
    }
    if (qualificationData.nhc.length > 0) {
        datasets.push(buildDataset(qualificationData.nhc, 'NHC变更', 'rgb(255, 205, 86)'));
    }

    const ctx = document.getElementById('qualificationChangesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: { labels: labels, datasets: datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: true, position: 'top' },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const periodName = qualificationData.cma[0]?.period_name ||
                                             qualificationData.cnas[0]?.period_name ||
                                             qualificationData.nhc[0]?.period_name || '时间';
                            return `${periodName}: ${context[0].label}`;
                        }
                    }
                }
            },
            scales: {
                x: { title: { display: true, text: '时间' } },
                y: { title: { display: true, text: '变更数量' }, beginAtZero: true, ticks: { stepSize: 1 } }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const elementIndex = elements[0].index;
                    const period = labels[elementIndex];
                    jumpToAnalysisMethodsWithChangeFilter(period, 'qualification_changes');
                }
            }
        }
    });
}

// 显示价格变更统计图表
function displayPriceChangesChart(priceChangesData) {
    const container = document.getElementById('priceChangesChartContainer');

    // 移除加载状态
    removeChartLoading(container);

    if (!priceChangesData || priceChangesData.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-dollar-sign fa-2x mb-3"></i>
                <p>暂无价格变更统计数据</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `<div style="height: 300px;"><canvas id="priceChangesChart"></canvas></div>`;

    const labels = priceChangesData.map(item => item.period);
    const data = priceChangesData.map(item => item.count);

    const ctx = document.getElementById('priceChangesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '价格变更数量',
                data: data,
                borderColor: '#fd7e14',
                backgroundColor: 'rgba(253, 126, 20, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: '#fd7e14',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: true, position: 'top' },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return `${priceChangesData[0].period_name}: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `价格变更数量: ${context.parsed.y} 个`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: { display: true, text: priceChangesData[0]?.period_name || '时间' },
                    ticks: {
                        maxTicksLimit: Math.min(15, Math.max(5, Math.ceil(labels.length / 5))),
                        maxRotation: 45,
                        minRotation: 0
                    }
                },
                y: { title: { display: true, text: '方法数量' }, beginAtZero: true, ticks: { stepSize: 1 } }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const elementIndex = elements[0].index;
                    const clickedData = priceChangesData[elementIndex];
                    jumpToAnalysisMethodsWithChangeFilter(clickedData.period, 'price_changes');
                }
            }
        }
    });
}



// 跳转到分析方法界面并应用日期筛选（原有功能保留）
function jumpToAnalysisMethodsWithDateFilter(targetDate) {
    // 构建跳转URL，包含日期筛选参数
    const url = `/analysis_methods?date_filter=${encodeURIComponent(targetDate)}`;

    // 在新标签页中打开分析方法界面
    window.open(url, '_blank');
}

// 跳转到专门的方法详情窗口，显示精确的变动方法
function jumpToAnalysisMethodsWithChangeFilter(targetDate, changeType) {
    // 从统计数据中获取对应的方法ID列表
    const methodIds = getMethodIdsByDateAndType(targetDate, changeType);

    if (methodIds && methodIds.length > 0) {
        // 构建分析方法页面URL，使用method_ids参数
        const url = `/analysis_methods?method_ids=${methodIds.join(',')}&change_type=${encodeURIComponent(changeType)}&change_date=${encodeURIComponent(targetDate)}`;

        // 在新标签页中打开分析方法页面
        window.open(url, '_blank');
    } else {
        alert('该时间段没有找到相关的方法变动记录');
    }
}

// 从统计数据中获取指定日期和类型的方法ID列表
function getMethodIdsByDateAndType(targetDate, changeType) {
    // 从全局统计数据中查找对应的方法ID
    if (!window.currentStatisticsData || !window.currentStatisticsData.method_details) {
        console.warn('统计数据不完整，无法获取方法ID列表');
        return [];
    }

    const methodDetails = window.currentStatisticsData.method_details;
    let methodIds = [];

    switch(changeType) {
        case 'new_methods':
            if (methodDetails.new_methods && methodDetails.new_methods[targetDate]) {
                methodIds = methodDetails.new_methods[targetDate];
            }
            break;
        case 'qualification_changes':
            // 合并CMA、CNAS、NHC的变更方法ID
            if (methodDetails.qualification_changes) {
                const cmaIds = methodDetails.qualification_changes.cma[targetDate] || [];
                const cnasIds = methodDetails.qualification_changes.cnas[targetDate] || [];
                const nhcIds = methodDetails.qualification_changes.nhc[targetDate] || [];
                methodIds = [...new Set([...cmaIds, ...cnasIds, ...nhcIds])]; // 去重
            }
            break;
        case 'price_changes':
            if (methodDetails.price_changes && methodDetails.price_changes[targetDate]) {
                methodIds = methodDetails.price_changes[targetDate];
            }
            break;
    }

    return methodIds;
}



// 显示价格变动分析帮助
function showPriceChangeHelp() {
    const modal = new bootstrap.Modal(document.getElementById('priceChangeHelpModal'));
    modal.show();
}

// 显示基准价格设置帮助
function showBaselineHelp() {
    const modal = new bootstrap.Modal(document.getElementById('baselineHelpModal'));
    modal.show();
}

// 更新分页控件
function updatePagination(type, paginationData) {
    const totalPages = type === 'recent' ? paginationData.recent_total_pages : paginationData.baseline_total_pages;
    const total = type === 'recent' ? paginationData.recent_total : paginationData.baseline_total;
    const count = type === 'recent' ? paginationData.recent_count : paginationData.baseline_count;

    if (totalPages <= 1) {
        document.getElementById(`${type}-pagination`).style.display = 'none';
        return;
    }

    // 更新分页信息
    const startItem = (currentPage - 1) * paginationData.per_page + 1;
    const endItem = Math.min(currentPage * paginationData.per_page, total);

    document.getElementById(`${type}-start-item`).textContent = startItem;
    document.getElementById(`${type}-end-item`).textContent = endItem;
    document.getElementById(`${type}-total-items`).textContent = total;

    // 生成分页按钮
    const paginationNav = document.getElementById(`${type}-pagination-nav`);
    let paginationHtml = '';

    // 上一页按钮
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadComprehensivePriceChanges(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadComprehensivePriceChanges(1); return false;">1</a>
            </li>
        `;
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadComprehensivePriceChanges(${i}); return false;">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadComprehensivePriceChanges(${totalPages}); return false;">${totalPages}</a>
            </li>
        `;
    }

    // 下一页按钮
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadComprehensivePriceChanges(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationNav.innerHTML = paginationHtml;
    document.getElementById(`${type}-pagination`).style.display = 'flex';
}

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.baseline-method-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedCount();
}

// 更新选中数量显示
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.baseline-method-checkbox:checked');
    const count = checkboxes.length;
    document.getElementById('selected-count').textContent = `已选择 ${count} 个方法`;

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('select-all-baseline');
    const allCheckboxes = document.querySelectorAll('.baseline-method-checkbox');

    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
}

// 设置选中方法为基准价格
function setSelectedAsBaseline() {
    const checkboxes = document.querySelectorAll('.baseline-method-checkbox:checked');
    const methodIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    if (methodIds.length === 0) {
        showAlert('请先选择要设置基准价格的方法', 'warning');
        return;
    }

    if (!confirm(`确定要将选中的 ${methodIds.length} 个方法的当前价格设为基准价格吗？\n\n设置后，这些方法将从当前基准变动列表中移除，只有当价格相对于新基准再次变化时才重新显示。`)) {
        return;
    }

    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>设置中...';
    button.disabled = true;

    apiRequest('/api/set_manual_baseline_prices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            method_ids: methodIds
        })
    })
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // 刷新价格变动数据
            loadComprehensivePriceChanges(currentPage);
        } else {
            showAlert('设置基准价格失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('设置基准价格时出错: ' + error.message, 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
    });
}



// 统计管理功能
function incrementalUpdateStatistics() {
    if (!confirm('确定要执行增量更新统计数据吗？这个操作可能需要几分钟时间。')) {
        return;
    }

    const button = event.target.closest('a');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在增量更新...';
    button.classList.add('disabled');

    apiRequest('/api/update_statistics_incrementally', {
        method: 'POST'
    })
    .then(data => {
        if (data.success) {
            alert('增量更新统计数据成功！');
            // 刷新当前图表
            if (window.currentChartType) {
                loadChartData(window.currentChartType);
            }
        } else {
            alert('增量更新统计数据失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('增量更新统计数据时出错: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    });
}

function checkStatisticsConsistency() {
    const button = event.target.closest('a');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在检查...';
    button.classList.add('disabled');

    apiRequest('/api/check_statistics_consistency')
    .then(data => {
        if (data.success) {
            const report = data.data;
            let message = `数据一致性检查完成\n\n`;
            message += `检查结果: ${report.consistent ? '✅ 通过' : '❌ 发现问题'}\n`;
            message += `检查时间: ${new Date(report.checked_at).toLocaleString()}\n`;

            if (report.issues && report.issues.length > 0) {
                message += `\n发现的问题:\n`;
                report.issues.forEach(issue => {
                    message += `• ${issue}\n`;
                });
            }

            if (report.summary_count !== undefined) {
                message += `\n汇总表记录数: ${report.summary_count}`;
                message += `\n实时计算记录数: ${report.realtime_count}`;
            }

            alert(message);
        } else {
            alert('检查数据一致性失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('检查数据一致性时出错: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    });
}

function rebuildStatisticsSummary() {
    if (!confirm('确定要重建统计汇总表吗？这个操作会重新计算所有统计数据，可能需要较长时间。')) {
        return;
    }

    const button = event.target.closest('a');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在重建...';
    button.classList.add('disabled');

    apiRequest('/api/rebuild_statistics_summary', {
        method: 'POST'
    })
    .then(data => {
        if (data.success) {
            alert('统计汇总表重建成功！');
            // 刷新当前图表
            if (window.currentChartType) {
                loadChartData(window.currentChartType);
            }
        } else {
            alert('统计汇总表重建失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('重建统计汇总表时出错: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    });
}

function clearStatisticsCache() {
    if (!confirm('确定要清理统计缓存吗？清理后下次访问图表时会重新加载数据。')) {
        return;
    }

    const button = event.target.closest('a');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在清理...';
    button.classList.add('disabled');

    apiRequest('/api/clear_statistics_cache', {
        method: 'POST'
    })
    .then(data => {
        if (data.success) {
            alert('统计缓存清理成功！');
            // 清除前端预加载数据
            if (window.preloadedChartData) {
                delete window.preloadedChartData;
            }
        } else {
            alert('统计缓存清理失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('清理统计缓存时出错: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    });
}

// 手动基准价格管理相关函数
let currentManualBaselinePage = 1;

// 加载手动基准价格数据
function loadManualBaselines(page = 1) {
    currentManualBaselinePage = page;

    showLoading('manual-baseline-content', '加载手动基准价格数据...');

    apiRequest(`/api/get_manual_baseline_details?page=${page}&per_page=20`)
        .then(data => {
            if (data.success) {
                displayManualBaselines(data.baselines, data);

                // 更新计数徽章
                document.getElementById('manual-baseline-count').textContent = data.total_count || 0;

                // 更新分页控件
                updateManualBaselinePagination(data);
            } else {
                showError('manual-baseline-content', '获取手动基准价格数据失败: ' + data.message);
            }
        });
}

// 显示手动基准价格数据
function displayManualBaselines(baselines, paginationData) {
    const container = document.getElementById('manual-baseline-content');

    if (!baselines || baselines.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-anchor fa-2x mb-3"></i>
                <p>暂无手动设置的基准价格</p>
                <small>在基准变动页面选择方法并点击"设为基准价格"来设置手动基准</small>
            </div>
        `;
        // 隐藏分页控件
        document.getElementById('manual-baseline-pagination').style.display = 'none';
        return;
    }

    displayManualBaselinesTable(baselines, container);

    // 显示分页控件
    if (paginationData && paginationData.total_pages > 1) {
        document.getElementById('manual-baseline-pagination').style.display = 'flex';
    } else {
        document.getElementById('manual-baseline-pagination').style.display = 'none';
    }
}

// 显示手动基准价格表格
function displayManualBaselinesTable(baselines, container) {
    let html = '<div class="table-responsive"><table class="table table-hover table-sm table-bordered" style="font-size: 0.95rem;">';

    html += `
        <thead class="table-light">
            <tr>
                <th width="3%" class="text-center">
                    <input type="checkbox" id="select-all-manual-baseline" onchange="toggleSelectAllManualBaseline(this)">
                </th>
                <th width="5%" class="text-center">项目编号</th>
                <th width="4%" class="text-center">方法ID</th>
                <th width="12%" class="text-center">检测项目</th>
                <th width="13%" class="text-center">检测方法</th>
                <th width="8%" class="text-center">当前分析价格</th>
                <th width="8%" class="text-center">手动基准价格</th>
                <th width="8%" class="text-center">默认基准价格</th>
                <th width="8%" class="text-center">当前最低价格</th>
                <th width="8%" class="text-center">手动基准最低价格</th>
                <th width="8%" class="text-center">默认基准最低价格</th>
                <th width="10%" class="text-center">设置信息</th>
                <th width="8%" class="text-center">操作</th>
            </tr>
        </thead>
        <tbody>
    `;

    baselines.forEach(baseline => {
        html += `
            <tr>
                <td class="text-center">
                    <input type="checkbox" class="manual-baseline-checkbox"
                           value="${baseline.check_method_id}"
                           onchange="updateManualBaselineSelectedCount()">
                </td>
                <td class="text-center">
                    <code class="small">${baseline.service_item_number || 'N/A'}</code>
                </td>
                <td class="text-center">
                    <code class="small">${formatMethodId(baseline.method_no)}</code>
                </td>
                <td class="text-truncate" title="${baseline.service_item_name}">
                    <div class="fw-bold">${baseline.service_item_name}</div>
                    <small class="text-muted d-block">${baseline.service_category_name || ''}</small>
                </td>
                <td class="text-truncate" title="${baseline.method_name}">
                    ${baseline.method_name.length > 25 ? baseline.method_name.substring(0, 25) + '...' : baseline.method_name}
                </td>
                <td class="text-center">
                    ${baseline.current_analysis_price ? baseline.current_analysis_price.toFixed(2) + '元' : '-'}
                </td>
                <td class="text-center">
                    <span class="text-info fw-bold">
                        ${baseline.manual_baseline_analysis_price ? baseline.manual_baseline_analysis_price.toFixed(2) + '元' : '-'}
                    </span>
                </td>
                <td class="text-center">
                    ${baseline.default_baseline_analysis_price ? baseline.default_baseline_analysis_price.toFixed(2) + '元' : '-'}
                </td>
                <td class="text-center">
                    ${baseline.current_lowest_price ? baseline.current_lowest_price.toFixed(2) + '元' : '-'}
                </td>
                <td class="text-center">
                    <span class="text-info fw-bold">
                        ${baseline.manual_baseline_lowest_price ? baseline.manual_baseline_lowest_price.toFixed(2) + '元' : '-'}
                    </span>
                </td>
                <td class="text-center">
                    ${baseline.default_baseline_lowest_price ? baseline.default_baseline_lowest_price.toFixed(2) + '元' : '-'}
                </td>
                <td class="text-center">
                    <small>
                        <div class="text-muted">${new Date(baseline.set_time).toLocaleDateString()}</div>
                        <div class="text-primary">${baseline.set_by_user || '未知用户'}</div>
                    </small>
                </td>
                <td class="text-center">
                    <button type="button" class="btn btn-outline-warning btn-sm"
                            onclick="restoreDefaultBaseline(${baseline.check_method_id})"
                            title="恢复默认基准价格">
                        <i class="fas fa-undo"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 更新手动基准价格分页控件
function updateManualBaselinePagination(paginationData) {
    const totalPages = paginationData.total_pages;
    const total = paginationData.total_count;

    if (totalPages <= 1) {
        document.getElementById('manual-baseline-pagination').style.display = 'none';
        return;
    }

    // 更新分页信息
    const startItem = (currentManualBaselinePage - 1) * paginationData.per_page + 1;
    const endItem = Math.min(currentManualBaselinePage * paginationData.per_page, total);

    document.getElementById('manual-baseline-start-item').textContent = startItem;
    document.getElementById('manual-baseline-end-item').textContent = endItem;
    document.getElementById('manual-baseline-total-items').textContent = total;

    // 生成分页按钮
    const paginationNav = document.getElementById('manual-baseline-pagination-nav');
    let paginationHtml = '';

    // 上一页按钮
    paginationHtml += `
        <li class="page-item ${currentManualBaselinePage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadManualBaselines(${currentManualBaselinePage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentManualBaselinePage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadManualBaselines(1); return false;">1</a>
            </li>
        `;
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentManualBaselinePage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadManualBaselines(${i}); return false;">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadManualBaselines(${totalPages}); return false;">${totalPages}</a>
            </li>
        `;
    }

    // 下一页按钮
    paginationHtml += `
        <li class="page-item ${currentManualBaselinePage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadManualBaselines(${currentManualBaselinePage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationNav.innerHTML = paginationHtml;
    document.getElementById('manual-baseline-pagination').style.display = 'flex';
}

// 刷新手动基准价格数据
function refreshManualBaselines() {
    loadManualBaselines(currentManualBaselinePage);
}

// 显示手动基准价格管理弹窗
function showManualBaselineManagement() {
    const modal = new bootstrap.Modal(document.getElementById('manualBaselineModal'));
    modal.show();

    // 加载弹窗中的数据
    loadManualBaselinesInModal();
}

// 在弹窗中加载手动基准价格数据
function loadManualBaselinesInModal(page = 1) {
    showLoading('modal-manual-baseline-content', '加载手动基准价格数据...');

    apiRequest(`/api/get_manual_baseline_details?page=${page}&per_page=20`)
        .then(data => {
            if (data.success) {
                displayManualBaselinesInModal(data.baselines, data);

                // 更新计数徽章
                document.getElementById('modal-manual-baseline-count').textContent = data.total_count || 0;
            } else {
                showError('modal-manual-baseline-content', '获取手动基准价格数据失败: ' + data.message);
            }
        });
}

// 在弹窗中显示手动基准价格数据
function displayManualBaselinesInModal(baselines, paginationData) {
    const container = document.getElementById('modal-manual-baseline-content');

    if (!baselines || baselines.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-anchor fa-2x mb-3"></i>
                <p>暂无手动设置的基准价格</p>
                <small>在基准变动页面选择方法并点击"设为基准价格"来设置手动基准</small>
            </div>
        `;
        return;
    }

    // 使用相同的表格显示函数
    displayManualBaselinesTable(baselines, container);
}

// 刷新弹窗中的手动基准价格数据
function refreshManualBaselinesInModal() {
    loadManualBaselinesInModal();
}

// 全选/取消全选手动基准价格
function toggleSelectAllManualBaseline(checkbox) {
    const checkboxes = document.querySelectorAll('.manual-baseline-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateManualBaselineSelectedCount();
}

// 更新手动基准价格选中数量显示
function updateManualBaselineSelectedCount() {
    const checkboxes = document.querySelectorAll('.manual-baseline-checkbox:checked');
    const count = checkboxes.length;

    // 更新主页面的计数
    const selectedCountElement = document.getElementById('selected-count');
    if (selectedCountElement) {
        selectedCountElement.textContent = `已选择 ${count} 个方法`;
    }

    // 更新弹窗中的计数
    const modalSelectedCountElement = document.getElementById('modal-selected-count');
    if (modalSelectedCountElement) {
        modalSelectedCountElement.textContent = `已选择 ${count} 个方法`;
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('select-all-manual-baseline');
    const allCheckboxes = document.querySelectorAll('.manual-baseline-checkbox');

    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
}

// 恢复单个方法的默认基准价格
function restoreDefaultBaseline(methodId) {
    if (!confirm('确定要恢复该方法的默认基准价格吗？\n\n恢复后将使用历史第一次价格作为基准。')) {
        return;
    }

    restoreBaselines([methodId]);
}

// 批量恢复默认基准价格
function batchRestoreBaseline() {
    const checkboxes = document.querySelectorAll('.manual-baseline-checkbox:checked');
    const methodIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    if (methodIds.length === 0) {
        showAlert('请先选择要恢复默认基准价格的方法', 'warning');
        return;
    }

    if (!confirm(`确定要恢复选中的 ${methodIds.length} 个方法的默认基准价格吗？\n\n恢复后将使用历史第一次价格作为基准。`)) {
        return;
    }

    restoreBaselines(methodIds);
}

// 在弹窗中批量恢复默认基准价格
function batchRestoreBaselineInModal() {
    batchRestoreBaseline();
}

// 恢复基准价格的通用函数
function restoreBaselines(methodIds) {
    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>恢复中...';
    button.disabled = true;

    apiRequest('/api/remove_manual_baseline_prices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            method_ids: methodIds
        })
    })
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // 刷新所有相关数据
            loadManualBaselines(currentManualBaselinePage);
            loadComprehensivePriceChanges(currentPage);

            // 如果弹窗打开，也刷新弹窗数据
            const modal = document.getElementById('manualBaselineModal');
            if (modal && modal.classList.contains('show')) {
                loadManualBaselinesInModal();
            }
        } else {
            showAlert('恢复默认基准价格失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('恢复默认基准价格时出错: ' + error.message, 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 页面加载完成后初始化手动基准价格管理
document.addEventListener('DOMContentLoaded', function() {
    // 加载手动基准价格管理数据
    loadManualBaselines();
});
</script>
{% endblock %}