{% extends "base.html" %}

{% block title %}方法价格全览 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center pt-2 pb-2 mb-3 border-bottom">
    <h1 class="h4">
        <i class="fas fa-chart-line me-2"></i>方法价格全览
    </h1>
</div>

<!-- 方法分析功能 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white py-2">
                <h6 class="mb-0">
                    <i class="fas fa-search me-2"></i>方法价格分析
                </h6>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-lg-4">
                        <input type="text" class="form-control" id="method-analysis-input"
                               placeholder="输入方法名称进行分析（支持直接复制完整方法名，自动处理空格差异）">
                    </div>
                    <div class="col-lg-4">
                        <input type="text" class="form-control" id="project-name-input"
                               placeholder="输入项目名称进行搜索（支持项目和子项目匹配）">
                    </div>

                    <div class="col-lg-1">
                        <button type="button" class="btn btn-info w-100" onclick="analyzeMethod()">
                            <i class="fas fa-search me-1"></i>分析
                        </button>
                    </div>
                    <div class="col-lg-1">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="clearMethodAnalysis()">
                            <i class="fas fa-times"></i>清空
                        </button>
                    </div>
                </div>
                
                <!-- 分析结果摘要 -->
                <div id="method-analysis-summary" class="mt-4" style="display: none;">
                </div>
                
                <!-- 分析结果详情 -->
                <div id="method-analysis-details" class="mt-4" style="display: none;">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作提示 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-light border">
            <h6 class="alert-heading mb-2">
                <i class="fas fa-lightbulb"></i> 使用提示
            </h6>
            <ul class="mb-0 small">
                <li>输入完整的方法名称或关键词进行搜索分析</li>
                <li>输入项目名称可搜索包含该项目的方法（支持子项目匹配）</li>
                <li><strong>同时输入方法名称和项目名称时，只显示两个条件都匹配的方法</strong></li>
                <li>支持模糊匹配，系统会查找相似的方法名称</li>
                <li>分析结果按检测分类分组显示，便于对比</li>
                <li>点击项目名称可查看相关子项目信息</li>
                <li>CMA/CNAS/NHC认证状态帮助判断方法权威性</li>
            </ul>
        </div>
    </div>
</div>

<!-- 最近分析方法历史 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light py-2">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>最近分析方法
                </h6>
            </div>
            <div class="card-body p-3">
                <div id="recent-analysis-list">
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p class="mb-0">暂无分析方法记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 子项目弹窗 -->
<div class="modal fade" id="childItemsModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="childItemsModalTitle">
                    <i class="fas fa-list me-2"></i>子项目列表
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="childItemsContent">
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 价格趋势弹窗 -->
<div class="modal fade" id="priceTrendModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6" id="priceTrendModalTitle">
                    <i class="fas fa-chart-line me-2"></i>价格趋势图
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="priceTrendContent">
                    <div class="text-center p-4">
                        <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                        <p>加载价格趋势数据中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 最近分析方法历史
let recentAnalysis = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从本地存储加载历史记录
    loadRecentAnalysis();
    
    // 添加回车键事件监听
    document.getElementById('method-analysis-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            analyzeMethod();
        }
    });
    
    document.getElementById('project-name-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            analyzeMethod();
        }
    });
    
    // 添加点击事件委托处理子项目显示
    document.body.addEventListener('click', function(e) {
        if (e.target.classList.contains('project-name-clickable')) {
            const projectName = e.target.getAttribute('data-project-name');
            const childItems = e.target.getAttribute('data-child-items');
            showChildItems(projectName, childItems);
        }
    });
});

// 格式化函数（与分析方法页面保持一致）
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatMethodId(methodNo) {
    if (!methodNo) return '-';
    const str = methodNo.toString();
    return str.length >= 2 ? str.slice(-2) : str;
}

function formatCategory(category1, category2) {
    const cat1 = category1 || '';
    const cat2 = category2 || '';
    if (cat1 && cat2) {
        return `${cat1}-${cat2}`;
    } else if (cat1) {
        return cat1;
    } else if (cat2) {
        return cat2;
    }
    return '-';
}

function formatPrice(analysisPrice, lowestPrice) {
    let analysis = analysisPrice || 0;
    let lowest = lowestPrice || analysis;

    // 如果分析价格为"未设置"或空，设为0
    if (analysis === '未设置' || !analysis) analysis = 0;
    if (lowest === '未设置' || !lowest) lowest = analysis;

    return `${analysis}/${lowest}`;
}

// 新增：带价格变动箭头的价格格式化函数
function formatPriceWithChanges(method) {
    let analysis = method.analysis_price_display || method.analysis_price || 0;
    let lowest = method.lowest_price_display || method.lowest_price || analysis;

    // 如果分析价格为"未设置"或空，设为0
    if (analysis === '未设置' || !analysis) analysis = 0;
    if (lowest === '未设置' || !lowest) lowest = analysis;

    // 获取价格变动箭头
    let analysisArrow = '';
    let lowestArrow = '';

    if (method.analysis_price_change && method.analysis_price_change.change !== undefined) {
        analysisArrow = getPriceChangeArrow(method.analysis_price_change.change);
    }

    if (method.lowest_price_change && method.lowest_price_change.change !== undefined) {
        lowestArrow = getPriceChangeArrow(method.lowest_price_change.change);
    }

    return `${analysis}${analysisArrow}/${lowest}${lowestArrow}`;
}

// 新增：获取价格变动箭头指示器
function getPriceChangeArrow(change) {
    if (change > 0) {
        return '<span class="text-danger ms-1">↑</span>';
    } else if (change < 0) {
        return '<span class="text-success ms-1">↓</span>';
    }
    return '';
}

function formatProjectName(projectName, hasChildItem) {
    if (!projectName) return '-';

    if (hasChildItem && hasChildItem !== '无') {
        // 使用data属性存储子项目数据，避免在onclick中传递大量字符串
        return `<span class="text-primary fw-bold project-name-clickable" style="cursor: pointer;"
                      data-project-name="${escapeHtml(projectName)}"
                      data-child-items="${escapeHtml(hasChildItem)}">${escapeHtml(projectName)}</span>`;
    } else {
        return `<span class="fw-bold">${escapeHtml(projectName)}</span>`;
    }
}

function formatChildItemCount(hasChildItem) {
    if (!hasChildItem || hasChildItem === '无' || hasChildItem.trim() === '') {
        return '<span class="text-muted">1</span>';
    }

    // 使用中文顿号分割子项目，计算数量
    const childItems = hasChildItem.split('、').filter(item => item.trim() !== '');
    const count = childItems.length;

    return `<span style="color: black;">${count}</span>`;
}

function formatStatus(itemStatus, methodStatus) {
    const itemLocked = itemStatus == 1;
    const methodLocked = methodStatus == 1;

    let itemIcon = itemLocked ? '<i class="fas fa-lock text-danger" title="项目锁定"></i>' : '<i class="fas fa-unlock text-success" title="项目正常"></i>';
    let methodIcon = methodLocked ? '<i class="fas fa-lock text-danger" title="方法锁定"></i>' : '<i class="fas fa-unlock text-success" title="方法正常"></i>';

    return `<span style="font-size: 0.75rem;">项目${itemIcon} 方法${methodIcon}</span>`;
}

function formatAvailability(itemStatus, methodStatus) {
    const itemLocked = itemStatus == 1;
    const methodLocked = methodStatus == 1;
    const isAvailable = !itemLocked && !methodLocked;

    return isAvailable
        ? '<span class="badge bg-success-subtle text-success border border-success" style="font-size: 0.7rem;">可用</span>'
        : '<span class="badge bg-danger-subtle text-danger border border-danger" style="font-size: 0.7rem;">不可用</span>';
}

function formatCertification(cma, cnas, nhc) {
    const hasCMA = cma == 1;
    const hasCNAS = cnas == 1;
    const hasNHC = nhc == 1;

    const certifications = [];
    if (hasCMA) certifications.push('CMA');
    if (hasCNAS) certifications.push('CNAS');
    if (hasNHC) certifications.push('NHC');

    if (certifications.length === 0) {
        return '<span class="text-muted" style="font-size:0.7rem;">无</span>';
    } else if (certifications.length === 1) {
        const cert = certifications[0];
        const colorClass = cert === 'CMA' ? 'bg-success' :
                          cert === 'CNAS' ? 'bg-info' : 'bg-warning';
        return `<span class="badge ${colorClass}" style="font-size:0.7rem;">${cert}</span>`;
    } else {
        return `<span class="badge bg-primary" style="font-size:0.7rem;">${certifications.join('/')}</span>`;
    }
}

// API请求函数
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 方法分析功能
function analyzeMethod() {
    const methodName = document.getElementById('method-analysis-input').value.trim();
    const projectName = document.getElementById('project-name-input').value.trim();

    if (!methodName && !projectName) {
        alert('请输入要分析的方法名称或项目名称');
        return;
    }

    const summaryContainer = document.getElementById('method-analysis-summary');
    const detailsContainer = document.getElementById('method-analysis-details');

    // 显示加载状态
    summaryContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在分析方法...</div>';
    summaryContainer.style.display = 'block';
    detailsContainer.innerHTML = '';
    detailsContainer.style.display = 'none';

    const params = new URLSearchParams();
    if (methodName) {
        params.append('method_name', methodName);
    }
    if (projectName) {
        params.append('project_name', projectName);
    }

    apiRequest(`/api/analyze_method?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayMethodAnalysisResults(data.data);
                // 添加到历史记录，优先使用方法名称，如果没有则使用项目名称
                const searchTerm = methodName || projectName;
                addToRecentAnalysis(searchTerm);
            } else {
                summaryContainer.innerHTML = `<div class="alert alert-warning small mb-0">${data.message}</div>`;
                detailsContainer.innerHTML = '';
            }
        })
        .catch(error => {
            summaryContainer.innerHTML = `<div class="alert alert-danger small mb-0">分析失败: ${error.message}</div>`;
            detailsContainer.innerHTML = '';
        });
}

function displayMethodAnalysisResults(data) {
    const summaryContainer = document.getElementById('method-analysis-summary');
    const detailsContainer = document.getElementById('method-analysis-details');

    // 显示统计摘要
    summaryContainer.innerHTML = `
        <div class="alert alert-info mb-0">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary mb-1">${data.total_methods}</h4>
                        <small class="text-muted">匹配方法数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary mb-1">${data.total_projects}</h4>
                        <small class="text-muted">涉及项目数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary mb-1">${data.categories_count}</h4>
                        <small class="text-muted">检测分类数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary mb-1">${Object.keys(data.category_groups).length}</h4>
                        <small class="text-muted">主分类数</small>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 按一级-二级分类分组显示详细结果
    let detailsHtml = '';

    // 重新组织数据按一级-二级分类分组
    const categoryGroups = {};
    for (const [categoryName, categoryData] of Object.entries(data.category_groups)) {
        categoryData.methods.forEach(method => {
            // 只显示没有废止的方法
            if (method.method_status == 1) return; // 跳过废止的方法

            const primaryCategory = method.service_category_name || '未分类';
            const secondaryCategory = method.second_category_name || '其他';
            const fullCategoryKey = `${primaryCategory}-${secondaryCategory}`;

            if (!categoryGroups[fullCategoryKey]) {
                categoryGroups[fullCategoryKey] = {
                    primary_category: primaryCategory,
                    secondary_category: secondaryCategory,
                    methods: []
                };
            }
            categoryGroups[fullCategoryKey].methods.push(method);
        });
    }



    // 对每个分类的方法进行排序
    for (const [categoryKey, categoryData] of Object.entries(categoryGroups)) {
        // 优先按last_date排序，如果last_date为null则按updated_at排序
        categoryData.methods.sort((a, b) => {
            // 如果有last_date就用last_date，否则用updated_at
            const dateA = a.last_date ? new Date(a.last_date) : new Date(a.method_updated_at || a.updated_at || '2020-01-01');
            const dateB = b.last_date ? new Date(b.last_date) : new Date(b.method_updated_at || b.updated_at || '2020-01-01');
            return dateB - dateA;
        });
    }

    for (const [categoryKey, categoryData] of Object.entries(categoryGroups)) {
        detailsHtml += `
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                    <h6 class="text-primary mb-0">
                        <i class="fas fa-folder me-2"></i>${categoryData.primary_category} - ${categoryData.secondary_category}
                    </h6>
                    <span class="badge bg-primary">${categoryData.methods.length} 个方法</span>
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0 column-divider" style="font-size: 0.95rem;">
                        <thead class="table-light">
                            <tr>
                                <th width="6%" class="text-center">分类</th>
                                <th width="6%" class="text-center">项目编号</th>
                                <th width="4%" class="text-center">方法ID</th>
                                <th width="7%" class="text-center">项目名称</th>
                                <th width="7%" class="text-center">子项目数量</th>
                                <th width="32%" class="text-center">方法名称</th>
                                <th width="10%" class="text-center">价格(分析/最低)</th>
                                <th width="10%" class="text-center">采样价格</th>
                                <th width="8%" class="text-center">资质</th>
                                <th width="6%" class="text-center">状态</th>
                                <th width="4%" class="text-center">可用</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        categoryData.methods.forEach(method => {
            // 处理采样价格（包括已删除的和变动状态）
            let samplePricesHtml = '';
            const currentSamples = method.sample_types || [];
            const deletedSamples = method.deleted_sample_types || [];
            const sampleChanges = method.sample_price_changes || [];

            if (currentSamples.length > 0 || deletedSamples.length > 0 || sampleChanges.length > 0) {
                const samplePrices = [];

                // 创建采样类型变动状态映射
                const sampleChangeMap = {};
                sampleChanges.forEach(change => {
                    sampleChangeMap[change.type] = change;
                });

                // 当前有效的采样价格
                currentSamples.forEach(sample => {
                    const change = sampleChangeMap[sample.type];
                    let priceText = `${sample.type}: ${sample.price || '未设置'}`;

                    if (change && change.price_change) {
                        const arrow = getPriceChangeArrow(change.price_change.change);
                        priceText += arrow;
                    }

                    samplePrices.push(priceText);
                });

                // 已删除的采样价格
                deletedSamples.forEach(sample => {
                    samplePrices.push(`<span class="text-danger"><del>${sample.type}: ${sample.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
                });

                // 处理变动中标记为删除的采样类型
                sampleChanges.forEach(change => {
                    if (change.status === 'deleted' && !deletedSamples.find(s => s.type === change.type)) {
                        samplePrices.push(`<span class="text-danger"><del>${change.type}: ${change.last_price || 0}</del> <span class="text-muted small">删除</span></span>`);
                    } else if (change.status === 'new') {
                        // 新增的采样类型（如果不在当前列表中）
                        if (!currentSamples.find(s => s.type === change.type)) {
                            samplePrices.push(`<span class="text-success">${change.type}: ${change.current_price || 0} <span class="text-muted small">新增</span></span>`);
                        }
                    }
                });

                samplePricesHtml = samplePrices.join('<br>');
            } else {
                samplePricesHtml = '<span class="text-muted">无采样价格</span>';
            }

            detailsHtml += `
                <tr>
                    <td class="text-center">${formatCategory(method.service_category_name, method.second_category_name)}</td>
                    <td class="text-center"><code class="small">${method.service_item_number || '-'}</code></td>
                    <td class="text-center"><code class="small">${formatMethodId(method.method_no)}</code></td>
                    <td class="text-center">${formatProjectName(method.service_item_name, method.child_item)}</td>
                    <td class="text-center">${formatChildItemCount(method.child_item)}</td>
                    <td>
                        <span class="text-primary" style="cursor: pointer; text-decoration: underline;"
                              onclick="showPriceTrend('${method.method_id}', '${escapeHtml(method.method_name)}', '${escapeHtml(method.service_item_name)}')"
                              title="点击查看价格趋势图">
                            ${escapeHtml(method.method_name)}
                        </span>
                    </td>
                    <td class="text-center">${formatPriceWithChanges(method)}</td>
                    <td class="text-center small">${samplePricesHtml}</td>
                    <td class="text-center">${formatCertification(method.cma, method.cnas, method.gov_agree)}</td>
                    <td class="text-center">${formatStatus(method.item_status, method.method_status)}</td>
                    <td class="text-center">${formatAvailability(method.item_status, method.method_status)}</td>
                </tr>
            `;
        });

        detailsHtml += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    detailsContainer.innerHTML = detailsHtml;
    detailsContainer.style.display = 'block';
}

function clearMethodAnalysis() {
    document.getElementById('method-analysis-input').value = '';
    document.getElementById('project-name-input').value = '';
    document.getElementById('method-analysis-summary').style.display = 'none';
    document.getElementById('method-analysis-details').style.display = 'none';
    document.getElementById('method-analysis-summary').innerHTML = '';
    document.getElementById('method-analysis-details').innerHTML = '';
}

// 最近分析方法历史管理
function loadRecentAnalysis() {
    const saved = localStorage.getItem('recentMethodAnalysis');
    if (saved) {
        recentAnalysis = JSON.parse(saved);
        displayRecentAnalysis();
    }
}

function saveRecentAnalysis() {
    localStorage.setItem('recentMethodAnalysis', JSON.stringify(recentAnalysis));
    displayRecentAnalysis();
}

function addToRecentAnalysis(methodName) {
    // 移除已存在的相同方法
    recentAnalysis = recentAnalysis.filter(item => item !== methodName);
    
    // 添加到开头
    recentAnalysis.unshift(methodName);
    
    // 限制历史记录数量
    if (recentAnalysis.length > 10) {
        recentAnalysis = recentAnalysis.slice(0, 10);
    }
    
    saveRecentAnalysis();
}

function displayRecentAnalysis() {
    const container = document.getElementById('recent-analysis-list');
    
    if (recentAnalysis.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p class="mb-0">暂无分析方法记录</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    recentAnalysis.forEach((methodName, index) => {
        html += `
            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-2">
                <div class="d-flex align-items-center">
                    <span class="badge bg-secondary me-3">${index + 1}</span>
                    <span class="fw-medium">${escapeHtml(methodName)}</span>
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="analyzeRecentMethod('${escapeHtml(methodName)}')">
                        <i class="fas fa-search me-1"></i>重新分析
                    </button>
                    <button class="btn btn-sm btn-outline-danger ms-1" onclick="removeFromRecent('${escapeHtml(methodName)}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function analyzeRecentMethod(methodName) {
    document.getElementById('method-analysis-input').value = methodName;
    analyzeMethod();
}

function removeFromRecent(methodName) {
    recentAnalysis = recentAnalysis.filter(item => item !== methodName);
    saveRecentAnalysis();
}


// 获取价格变动状态（更新为使用新的价格变动数据）
function getPriceChangeStatus(method) {
    // 使用新的价格变动数据
    let hasIncrease = false;
    let hasDecrease = false;

    // 检查分析价格变动
    if (method.analysis_price_change && method.analysis_price_change.change !== undefined) {
        if (method.analysis_price_change.change > 0) {
            hasIncrease = true;
        } else if (method.analysis_price_change.change < 0) {
            hasDecrease = true;
        }
    }

    // 检查最低价格变动
    if (method.lowest_price_change && method.lowest_price_change.change !== undefined) {
        if (method.lowest_price_change.change > 0) {
            hasIncrease = true;
        } else if (method.lowest_price_change.change < 0) {
            hasDecrease = true;
        }
    }

    // 检查采样价格变动
    if (method.sample_price_changes && method.sample_price_changes.length > 0) {
        method.sample_price_changes.forEach(change => {
            if (change.price_change && change.price_change.change !== undefined) {
                if (change.price_change.change > 0) {
                    hasIncrease = true;
                } else if (change.price_change.change < 0) {
                    hasDecrease = true;
                }
            }
        });
    }

    // 确定整体变动状态
    if (hasIncrease && hasDecrease) {
        return '<span class="badge bg-warning-subtle text-warning border border-warning" style="font-size: 0.7rem;">价格混合变动</span>';
    } else if (hasIncrease) {
        return '<span class="badge bg-danger-subtle text-danger border border-danger" style="font-size: 0.7rem;">价格上涨</span>';
    } else if (hasDecrease) {
        return '<span class="badge bg-success-subtle text-success border border-success" style="font-size: 0.7rem;">价格下降</span>';
    } else {
        return '<span class="badge bg-secondary-subtle text-secondary border border-secondary" style="font-size: 0.7rem;">价格稳定</span>';
    }
}

// 检查单个价格的变动状态
function checkPriceChange(currentPrice, initialPrice) {
    // 处理未设置或空值的情况
    if (currentPrice === '未设置' || currentPrice === null || currentPrice === undefined) {
        currentPrice = 0;
    }
    if (initialPrice === '未设置' || initialPrice === null || initialPrice === undefined) {
        initialPrice = 0;
    }

    const current = parseFloat(currentPrice) || 0;
    const initial = parseFloat(initialPrice) || 0;

    // 如果初始价格为0，且当前价格也为0，则认为无变动
    // 如果初始价格为0，但当前价格不为0，则认为是增长
    if (initial === 0 && current === 0) {
        return 'none';
    } else if (initial === 0 && current > 0) {
        return 'increase';
    } else if (current > initial) {
        return 'increase';
    } else if (current < initial) {
        return 'decrease';
    } else {
        return 'none';
    }
}



// 子项目功能
function showChildItems(projectName, childItemData) {
    document.getElementById('childItemsModalTitle').innerHTML = `<i class="fas fa-list me-2"></i>${projectName} - 子项目列表`;

    // 显示加载状态
    document.getElementById('childItemsContent').innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载子项目数据中...</p>
        </div>
    `;

    // 显示弹窗
    new bootstrap.Modal(document.getElementById('childItemsModal')).show();

    // 直接显示原始子项目数据，不进行分割处理
    try {
        if (!childItemData || childItemData === '无' || childItemData === '') {
            document.getElementById('childItemsContent').innerHTML = `
                <div class="text-center p-4 text-muted">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>该项目暂无子项目信息</p>
                </div>
            `;
            return;
        }

        // 计算子项目数量（只按顿号分割）
        const childItems = childItemData.split('、').filter(item => item.trim());
        const itemCount = childItems.length;

        let html = `
            <div class="alert alert-info mb-3">
                <div class="row">
                    <div class="col-md-8">
                        <strong><i class="fas fa-list me-2"></i>子项目列表</strong>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge bg-primary">共 ${itemCount} 个子项目</span>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <div class="input-group input-group-sm">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="childItemSearch" placeholder="搜索子项目...">
                </div>
            </div>
            <div class="card">
                <div class="card-body p-2" style="max-height: 400px; overflow-y: auto;">
                    <div class="row" id="childItemsList">
        `;

        childItems.forEach((item, index) => {
            const trimmedItem = item.trim();
            if (trimmedItem) {
                html += `
                    <div class="col-md-6 mb-1 child-item-entry">
                        <small class="text-muted">${index + 1}.</small>
                        <span class="child-item-text">${escapeHtml(trimmedItem)}</span>
                    </div>
                `;
            }
        });

        html += `
                    </div>
                </div>
            </div>
        `;

        document.getElementById('childItemsContent').innerHTML = html;

        // 添加搜索功能
        const searchInput = document.getElementById('childItemSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                const childItemEntries = document.querySelectorAll('.child-item-entry');

                childItemEntries.forEach(entry => {
                    const text = entry.querySelector('.child-item-text').textContent.toLowerCase();
                    if (searchTerm === '' || text.includes(searchTerm)) {
                        entry.style.display = '';
                    } else {
                        entry.style.display = 'none';
                    }
                });
            });
        }

    } catch (error) {
        console.error('解析子项目数据失败:', error);
        document.getElementById('childItemsContent').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                解析子项目数据失败
                <hr>
                <small><strong>原始数据:</strong> ${escapeHtml(String(childItemData || ''))}</small>
                <br>
                <small><strong>错误信息:</strong> ${error.message}</small>
            </div>
        `;
    }
}

// 显示价格趋势图
function showPriceTrend(methodId, methodName, projectName) {
    // 设置弹窗标题
    document.getElementById('priceTrendModalTitle').innerHTML =
        `<i class="fas fa-chart-line me-2"></i>${escapeHtml(methodName)} - 价格趋势图`;

    // 显示加载状态
    document.getElementById('priceTrendContent').innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>加载价格趋势数据中...</p>
        </div>
    `;

    // 显示弹窗
    new bootstrap.Modal(document.getElementById('priceTrendModal')).show();

    // 获取价格趋势数据
    apiRequest(`/api/method_price_trend?method_id=${methodId}`)
        .then(data => {
            if (data.success) {
                displayPriceTrend(data.data, methodName, projectName);
            } else {
                document.getElementById('priceTrendContent').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || '获取价格趋势数据失败'}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('priceTrendContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    获取价格趋势数据时出错: ${error.message}
                </div>
            `;
        });
}

// 显示价格趋势图表
function displayPriceTrend(trendData, methodName, projectName) {
    const container = document.getElementById('priceTrendContent');

    // 保存当前趋势数据到全局变量，供表格函数使用
    window.currentTrendData = trendData;

    if (!trendData.price_history || trendData.price_history.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                该方法暂无价格历史记录
            </div>
        `;
        return;
    }

    // 获取所有采样类型（从趋势数据或价格历史中提取）
    let allSampleTypes = [];
    if (trendData.all_sample_types) {
        allSampleTypes = trendData.all_sample_types;
    } else {
        // 从价格历史中提取所有采样类型
        const sampleTypesSet = new Set();
        trendData.price_history.forEach(record => {
            if (record.sample_prices) {
                Object.keys(record.sample_prices).forEach(type => sampleTypesSet.add(type));
            }
        });
        allSampleTypes = Array.from(sampleTypesSet);
    }

    // 创建图表和表格容器
    container.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light py-2">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            ${escapeHtml(projectName)} - ${escapeHtml(methodName)}
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="priceTrendChart" style="width:100%; height:350px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light py-2 d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            价格历史记录 (仅显示变动点)
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover mb-0 column-divider" style="font-size: 0.95rem;">
                                <thead class="table-light">
                                    <tr id="priceHistoryTableHeader">
                                        <!-- 表头将动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="priceHistoryTableBody">
                                    <!-- 数据将动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 准备图表数据
    const labels = trendData.price_history.map(record => new Date(record.fetch_time).toLocaleString('zh-CN'));
    const datasets = [];
    const colors = ['#007bff', '#dc3545', '#28a745', '#ffc107', '#17a2b8', '#6f42c1'];

    // 1. 分析价格
    datasets.push({
        label: '分析价格',
        data: trendData.price_history.map(record => record.check_method_price),
        borderColor: colors[0],
        backgroundColor: colors[0] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true // 连接空值点
    });

    // 2. 最低价格（基于当前方法状态决定逻辑）
    // 检查当前方法的最低价格状态
    const currentMethodInfo = trendData.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    datasets.push({
        label: '最低价格',
        data: trendData.price_history.map(record => {
            if (shouldUseAnalysisPrice) {
                // 当前方法的最低价格已被删除，整个历史都使用分析价格
                if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                    return record.check_method_price;
                } else {
                    return null;
                }
            } else {
                // 当前方法有最低价格，使用历史记录中的实际最低价格，null时回退到分析价格
                if (record.lowest_bid === null || record.lowest_bid === undefined || record.lowest_bid === 0) {
                    if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                        return record.check_method_price;
                    } else {
                        return null;
                    }
                }
                return record.lowest_bid;
            }
        }),
        borderColor: colors[1],
        backgroundColor: colors[1] + '33',
        fill: false,
        tension: 0.1,
        spanGaps: true // 连接空值点
    });

    // 3. 采样价格 - 处理所有历史出现过的采样类型
    let colorIndex = 2; // 从第三个颜色开始
    allSampleTypes.forEach(type => {
        datasets.push({
            label: `采样价格 - ${type}`,
            data: trendData.price_history.map(record => (record.sample_prices ? record.sample_prices[type] : null)),
            borderColor: colors[colorIndex % colors.length],
            backgroundColor: colors[colorIndex % colors.length] + '33',
            fill: false,
            tension: 0.1,
            spanGaps: true // 连接空值点
        });
        colorIndex++;
    });

    // 绘制图表
    const ctx = document.getElementById('priceTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '抓取时间'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '价格 (元)'
                    },
                    beginAtZero: false
                }
            },
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // 填充价格历史表格
    fillPriceHistoryTable(trendData.price_history, allSampleTypes);
}

// 填充价格历史表格
function fillPriceHistoryTable(priceHistory, sampleTypes) {
    const thead = document.getElementById('priceHistoryTableHeader');
    const tbody = document.getElementById('priceHistoryTableBody');

    // 生成表头
    let headerHtml = '<th class="text-center">抓取时间</th><th class="text-center">分析价格</th><th class="text-center">最低价格</th>';
    sampleTypes.forEach(type => {
        headerHtml += `<th class="text-center">采样价格 (${escapeHtml(type)})</th>`;
    });
    thead.innerHTML = headerHtml;

    // 获取当前方法的最低价格状态（从全局变量或数据中获取）
    const currentMethodInfo = window.currentTrendData?.method_info || {};
    const currentLowestBid = currentMethodInfo.current_lowest_bid;
    const shouldUseAnalysisPrice = (currentLowestBid === null || currentLowestBid === undefined || currentLowestBid === 0);

    // 生成表格内容
    let bodyHtml = '';
    // 反转历史记录，让最新的在前面
    [...priceHistory].reverse().forEach(record => {
        let rowHtml = `<tr><td class="text-center">${new Date(record.fetch_time).toLocaleString('zh-CN')}</td>`;

        const analysisPrice = record.check_method_price !== null && record.check_method_price !== undefined ? parseFloat(record.check_method_price).toFixed(2) + '元' : '-';
        rowHtml += `<td class="text-center fw-bold text-primary">${analysisPrice}</td>`;

        // 基于当前方法状态决定最低价格显示逻辑
        let lowestPriceDisplay;
        if (shouldUseAnalysisPrice) {
            // 当前方法的最低价格已被删除，整个历史都使用分析价格
            if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                lowestPriceDisplay = parseFloat(record.check_method_price).toFixed(2) + '元';
            } else {
                lowestPriceDisplay = '-';
            }
        } else {
            // 当前方法有最低价格，使用历史记录中的实际最低价格，null时回退到分析价格
            if (record.lowest_bid === null || record.lowest_bid === undefined || record.lowest_bid === 0) {
                if (record.check_method_price !== null && record.check_method_price !== undefined && record.check_method_price !== 0) {
                    lowestPriceDisplay = parseFloat(record.check_method_price).toFixed(2) + '元';
                } else {
                    lowestPriceDisplay = '-';
                }
            } else {
                lowestPriceDisplay = parseFloat(record.lowest_bid).toFixed(2) + '元';
            }
        }
        rowHtml += `<td class="text-center text-danger">${lowestPriceDisplay}</td>`;

        sampleTypes.forEach(type => {
            const samplePrice = (record.sample_prices && record.sample_prices[type] !== null && record.sample_prices[type] !== undefined) ? parseFloat(record.sample_prices[type]).toFixed(2) + '元' : '-';
            // 特殊处理价格为0的情况（表示已删除）
            if (record.sample_prices && record.sample_prices[type] === 0) {
                rowHtml += `<td class="text-center text-danger"><del>0元</del></td>`;
            } else {
                rowHtml += `<td class="text-center">${samplePrice}</td>`;
            }
        });

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    tbody.innerHTML = bodyHtml;
}

// 移除旧的、过于复杂的绘图函数
// function drawSimplePriceTrend(priceHistory) { ... }
// function fillPriceHistoryTable(priceHistory) { ... }

</script>
{% endblock %}