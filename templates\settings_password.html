{% extends "base.html" %}

{% block title %}设置密码验证 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center align-items-center" style="min-height: 80vh;">
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header text-center bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-lock me-2"></i>系统设置验证
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                        <p class="text-muted">请输入设置密码以访问系统设置</p>
                    </div>
                    
                    <form id="password-form">
                        <div class="mb-3">
                            <label for="password" class="form-label">设置密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input type="password" class="form-control" id="password" 
                                       placeholder="请输入设置密码" required>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="togglePassword()">
                                    <i class="fas fa-eye" id="password-toggle-icon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-unlock me-2"></i>验证密码
                            </button>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回控制台
                            </a>
                        </div>
                    </form>
                    
                    <!-- 错误提示 -->
                    <div id="error-alert" class="alert alert-danger mt-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-message"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动聚焦密码输入框
    $('#password').focus();
    
    // 回车键提交
    $('#password').keypress(function(e) {
        if (e.which == 13) {
            $('#password-form').submit();
        }
    });
});

// 切换密码显示/隐藏
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// 表单提交处理
$('#password-form').submit(function(e) {
    e.preventDefault();
    
    const password = $('#password').val().trim();
    
    if (!password) {
        showError('请输入设置密码');
        return;
    }
    
    // 显示加载状态
    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.html();
    submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>验证中...').prop('disabled', true);
    
    // 发送验证请求
    $.ajax({
        url: '/api/verify_settings_password',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            password: password
        }),
        success: function(response) {
            if (response.success) {
                // 验证成功，跳转到设置页面
                window.location.href = '/settings';
            } else {
                showError(response.message || '密码验证失败');
                $('#password').val('').focus();
            }
        },
        error: function(xhr, status, error) {
            console.error('验证请求失败:', error);
            showError('网络错误，请重试');
        },
        complete: function() {
            // 恢复按钮状态
            submitBtn.html(originalText).prop('disabled', false);
        }
    });
});

// 显示错误信息
function showError(message) {
    $('#error-message').text(message);
    $('#error-alert').slideDown();
    
    // 3秒后自动隐藏
    setTimeout(function() {
        $('#error-alert').slideUp();
    }, 3000);
}
</script>
{% endblock %}
