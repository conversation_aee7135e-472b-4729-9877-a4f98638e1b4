{% extends "base.html" %}

{% block title %}评价管理 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-star me-2"></i>评价管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="showCreateEvaluationModal()">
                <i class="fas fa-plus me-1"></i>创建评价方法
            </button>
            <button type="button" class="btn btn-success" onclick="showRecommendationModal()">
                <i class="fas fa-magic me-1"></i>智能推荐
            </button>
        </div>
    </div>
</div>

<!-- 评价方法列表 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>评价方法列表
        </h5>
    </div>
    <div class="card-body">
        <div id="evaluationMethodsTable">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="color: black;">评价方法名称</th>
                            <th style="color: black;">适用范围</th>
                            <th style="color: black;">推荐配置数量</th>
                            <th style="color: black;">创建者</th>
                            <th style="color: black;">创建时间</th>
                            <th style="color: black;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="evaluationMethodsTableBody">
                    </tbody>
                </table>
            </div>
        </div>
        
        <div id="noEvaluationData" class="text-center py-4" style="display: none;">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <p class="text-muted">暂无评价方法</p>
        </div>
    </div>
</div>

<!-- 智能推荐区域 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-magic me-2"></i>智能推荐系统
        </h5>
        <small class="text-muted">基于评价方法的推荐配置进行智能推荐</small>
    </div>
    <div class="card-body">
        <form id="recommendationForm" class="row g-3">
            <div class="col-md-3">
                <label for="evaluationMethodSelect" class="form-label">评价方法 *</label>
                <select class="form-select" id="evaluationMethodSelect" required>
                    <option value="">请选择评价方法</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="applicableScopeSelect" class="form-label">适用范围 *</label>
                <select class="form-select" id="applicableScopeSelect" required disabled>
                    <option value="">请先选择评价方法</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="detectionItemsInput" class="form-label">检测项目 *</label>
                <textarea class="form-control" id="detectionItemsInput" rows="2"
                          placeholder="请输入检测项目，多个项目用顿号（、）或逗号（,）分隔&#10;例如：镉、铅、汞、铬、砷" required></textarea>
                <div class="form-text">支持别名匹配，如：Cd、Pb、Hg等</div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-success d-block w-100">
                    <i class="fas fa-search me-1"></i>获取推荐
                </button>
                <button type="button" class="btn btn-outline-secondary d-block w-100 mt-2" onclick="clearRecommendationForm()">
                    <i class="fas fa-eraser me-1"></i>清空
                </button>
            </div>
        </form>
        
        <div id="recommendationResults" style="display: none;">
            <hr>
            <h6>推荐结果：</h6>
            <div id="recommendationContent"></div>
        </div>
    </div>
</div>

<!-- 创建评价方法模态框 -->
<div class="modal fade" id="evaluationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="evaluationModalTitle">创建评价方法</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="evaluationForm">
                    <input type="hidden" id="evaluationId" value="">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="evaluationName" class="form-label">评价方法名称 *</label>
                            <input type="text" class="form-control" id="evaluationName" required
                                   placeholder="如：危险废物鉴别5085.3">
                        </div>
                        <div class="col-12">
                            <label for="evaluationDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="evaluationDescription" rows="3"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="evaluationScope" class="form-label">适用范围</label>
                            <select class="form-select" id="evaluationScope">
                                <option value="">请选择适用范围</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="evaluationCreatedBy" class="form-label">创建者</label>
                            <input type="text" class="form-control" id="evaluationCreatedBy" 
                                   value="{{ session.user_info.staffName or session.username }}" readonly>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>推荐配置</h6>
                    <div id="recommendationConfigs">
                        <div class="recommendation-config row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">检测项目</label>
                                <select class="form-select detection-item-select">
                                    <option value="">请选择检测项目</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">推荐标准方法</label>
                                <select class="form-select standard-method-select">
                                    <option value="">请选择标准方法</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">优先级</label>
                                <select class="form-select priority-select">
                                    <option value="1">高</option>
                                    <option value="2">中</option>
                                    <option value="3">低</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-outline-danger d-block w-100" 
                                        onclick="removeRecommendationConfig(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="addRecommendationConfig()">
                            <i class="fas fa-plus me-1"></i>添加推荐配置
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="showImportRecommendationsModal()">
                            <i class="fas fa-upload me-1"></i>Excel导入推荐配置
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveEvaluationMethod()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 智能推荐模态框 -->
<div class="modal fade" id="recommendationModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">智能推荐系统</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>输入条件</h6>
                        <form id="modalRecommendationForm">
                            <div class="mb-3">
                                <label for="modalDetectionType" class="form-label">检测类型</label>
                                <select class="form-select" id="modalDetectionType">
                                    <option value="">请选择检测类型</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="modalDetectionItems" class="form-label">检测项目</label>
                                <select class="form-select" id="modalDetectionItems" multiple size="10">
                                </select>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-magic me-1"></i>获取推荐
                            </button>
                        </form>
                    </div>
                    <div class="col-md-8">
                        <h6>推荐结果</h6>
                        <div id="modalRecommendationResults">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-lightbulb fa-3x mb-3"></i>
                                <p>请选择检测条件并点击"获取推荐"</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel导入推荐配置模态框 -->
<div class="modal fade" id="importRecommendationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Excel导入推荐配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 说明信息 -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>导入说明</h6>
                    <ul class="mb-0">
                        <li>Excel文件格式：第1列为检测项目名称，第2列为推荐标准方法</li>
                        <li>检测项目名称支持别名匹配（如：镉、Cd、铅、Pb等）</li>
                        <li>推荐标准方法格式：普通方法如"GB 5085.3-2007"，附录方法如"GB 5085.3-2007-附录A"</li>
                        <li>多个推荐方法用顿号"、"分隔</li>
                        <li>所有导入的推荐方法默认优先级为"高"（优先级：1=高，2=中，3=低）</li>
                    </ul>
                </div>

                <!-- 模板下载 -->
                <div class="mb-3">
                    <label class="form-label">Excel模板</label>
                    <div>
                        <button type="button" class="btn btn-outline-primary" onclick="downloadTemplate()">
                            <i class="fas fa-download me-1"></i>下载Excel模板
                        </button>
                        <small class="text-muted ms-2">建议先下载模板，按格式填写数据</small>
                    </div>
                </div>

                <!-- 文件选择 -->
                <div class="mb-3">
                    <label for="importFile" class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="importFile"
                           accept=".xlsx,.xls" onchange="handleFileSelect(this)">
                    <div class="form-text">支持.xlsx和.xls格式</div>
                </div>

                <!-- 导入进度 -->
                <div id="importProgress" style="display: none;">
                    <div class="mb-2">
                        <label class="form-label">导入进度</label>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- 导入结果 -->
                <div id="importResult" style="display: none;">
                    <div class="mb-2">
                        <label class="form-label">导入结果</label>
                        <div id="importResultContent"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="startImportBtn"
                        onclick="startImport()" disabled>
                    <i class="fas fa-upload me-1"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allScopes = [];
let allItems = [];
let allStandardMethods = [];

$(document).ready(function() {
    // 加载初始数据
    loadScopes();
    loadItems();
    loadStandardMethods();
    loadEvaluationMethods();

    // 绑定推荐表单
    $('#recommendationForm').on('submit', function(e) {
        e.preventDefault();
        getRecommendations();
    });

    // 绑定模态框推荐表单
    $('#modalRecommendationForm').on('submit', function(e) {
        e.preventDefault();
        getModalRecommendations();
    });

    // 绑定评价方法选择变化事件
    $('#evaluationMethodSelect').on('change', function() {
        const methodId = $(this).val();
        if (methodId) {
            loadMethodScopes(methodId);
        } else {
            $('#applicableScopeSelect').prop('disabled', true).html('<option value="">请先选择评价方法</option>');
        }
    });

    // 添加调试按钮（临时）
    setTimeout(() => {
        console.log('页面加载完成，适用范围数据:', allScopes);
        console.log('适用范围选择框选项数量:', $('#evaluationScope option').length);
    }, 2000);
});

// 加载适用范围
function loadScopes() {
    console.log('开始加载适用范围...');
    apiRequest('/api/standard_methods/scopes')
        .then(data => {
            console.log('适用范围API响应:', data);
            if (data.success) {
                allScopes = data.data;
                console.log('加载的适用范围数据:', allScopes);
                updateScopeSelects();
            } else {
                console.error('适用范围API返回错误:', data.message);
            }
        })
        .catch(error => {
            console.error('加载适用范围失败:', error);
        });
}

// 加载检测项目
function loadItems() {
    apiRequest('/api/standard_methods/items')
        .then(data => {
            if (data.success) {
                allItems = data.data;
                updateItemSelects();
            }
        });
}

// 加载标准方法
function loadStandardMethods() {
    apiRequest('/api/standard_methods?per_page=1000')
        .then(data => {
            if (data.success) {
                allStandardMethods = data.data.methods;
                updateStandardMethodSelects();
            }
        });
}

// 更新适用范围选择框
function updateScopeSelects() {
    console.log('开始更新适用范围选择框，数据:', allScopes);
    const selects = ['#detectionType', '#modalDetectionType', '#evaluationScope'];

    selects.forEach(selector => {
        const select = $(selector);
        const currentValue = select.val();
        select.empty();

        if (selector === '#evaluationScope') {
            select.append('<option value="">请选择适用范围</option>');
        } else {
            select.append('<option value="">请选择检测类型</option>');
        }

        allScopes.forEach(scope => {
            select.append(`<option value="${scope.id}">${scope.name}</option>`);
        });

        if (currentValue) {
            select.val(currentValue);
        }

        console.log(`${selector} 选择框已更新，选项数量:`, select.find('option').length);
    });
}

// 更新检测项目选择框
function updateItemSelects() {
    const selects = ['#detectionItemsList', '#modalDetectionItems', '.detection-item-select'];
    
    selects.forEach(selector => {
        $(selector).each(function() {
            const select = $(this);
            const currentValue = select.val();
            select.empty();
            
            if (!select.hasClass('detection-item-select')) {
                // 多选框不需要默认选项
            } else {
                select.append('<option value="">请选择检测项目</option>');
            }
            
            allItems.forEach(item => {
                select.append(`<option value="${item.name}">${item.display_name}</option>`);
            });
            
            if (currentValue) {
                select.val(currentValue);
            }
        });
    });
}

// 更新标准方法选择框
function updateStandardMethodSelects() {
    $('.standard-method-select').each(function() {
        const select = $(this);
        const currentValue = select.val();
        select.empty().append('<option value="">请选择标准方法</option>');

        allStandardMethods.forEach(method => {
            select.append(`<option value="${method.id}">${method.full_standard_number} - ${method.standard_name}</option>`);
        });

        if (currentValue) {
            select.val(currentValue);
        }
    });
}

// 加载评价方法列表
function loadEvaluationMethods() {
    apiRequest('/api/evaluation_methods')
        .then(data => {
            if (data.success) {
                displayEvaluationMethods(data.data);
                updateEvaluationMethodSelect(data.data.methods);
            } else {
                $('#noEvaluationData').show();
            }
        });
}

// 更新智能推荐系统的评价方法选择框
function updateEvaluationMethodSelect(methods) {
    const select = $('#evaluationMethodSelect');
    select.empty().append('<option value="">请选择评价方法</option>');

    // 按名称分组评价方法（因为同名方法可能有多个适用范围）
    const methodGroups = {};
    methods.forEach(method => {
        if (!methodGroups[method.name]) {
            methodGroups[method.name] = [];
        }
        methodGroups[method.name].push(method);
    });

    // 添加分组后的评价方法选项
    Object.keys(methodGroups).sort().forEach(methodName => {
        const methodGroup = methodGroups[methodName];
        // 使用第一个方法的ID作为代表
        const representativeMethod = methodGroup[0];
        select.append(`<option value="${representativeMethod.id}">${methodName}</option>`);
    });
}

// 加载评价方法的适用范围
function loadMethodScopes(methodId) {
    const scopeSelect = $('#applicableScopeSelect');

    // 显示加载状态
    scopeSelect.prop('disabled', true).html('<option value="">正在加载...</option>');

    apiRequest(`/api/evaluation_methods/${methodId}/scopes`)
        .then(data => {
            if (data.success) {
                scopeSelect.empty().append('<option value="">请选择适用范围</option>');

                data.data.scopes.forEach(scope => {
                    scopeSelect.append(`<option value="${scope.evaluation_method_id}" data-scope-id="${scope.scope_id}">${scope.scope_name}</option>`);
                });

                scopeSelect.prop('disabled', false);
            } else {
                scopeSelect.html('<option value="">加载失败</option>');
                alert('加载适用范围失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('加载适用范围失败:', error);
            scopeSelect.html('<option value="">加载失败</option>');
            alert('加载适用范围失败：网络错误');
        });
}

// 清空推荐表单
function clearRecommendationForm() {
    $('#evaluationMethodSelect').val('');
    $('#applicableScopeSelect').prop('disabled', true).html('<option value="">请先选择评价方法</option>');
    $('#detectionItemsInput').val('');
    $('#recommendationResults').hide();
}

// 显示评价方法列表
function displayEvaluationMethods(data) {
    const tbody = $('#evaluationMethodsTableBody');
    tbody.empty();

    if (data.methods.length === 0) {
        $('#noEvaluationData').show();
        return;
    }

    data.methods.forEach(method => {
        const row = $(`
            <tr>
                <td><strong>${method.name}</strong></td>
                <td>${method.scope_name || '-'}</td>
                <td><span class="badge bg-info">${method.recommendation_count}</span></td>
                <td>${method.created_by || '-'}</td>
                <td>${formatDateTime(method.created_at)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editEvaluationMethod(${method.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteEvaluationMethod(${method.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示创建评价方法模态框
function showCreateEvaluationModal() {
    // 重置表单
    $('#evaluationForm')[0].reset();
    $('#evaluationId').val('');
    $('#evaluationModalTitle').text('创建评价方法');

    // 清空推荐配置
    $('#recommendationConfigs').empty();
    addRecommendationConfig(); // 添加一个空的推荐配置

    // 确保适用范围选项已加载
    updateScopeSelects();
    updateItemSelects();
    updateStandardMethodSelects();
    $('#evaluationModal').modal('show');
}

// 添加推荐配置
function addRecommendationConfig(existingData = null) {
    const configId = 'config_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    const configHtml = `
        <div class="recommendation-config row g-3 mb-3" data-config-id="${configId}">
            <div class="col-md-4">
                <label class="form-label">检测项目</label>
                <div class="position-relative">
                    <input type="text" class="form-control detection-item-search"
                           placeholder="搜索检测项目..." autocomplete="off"
                           data-config-id="${configId}">
                    <input type="hidden" class="detection-item-id" value="">
                    <div class="dropdown-menu detection-item-dropdown" style="width: 100%; max-height: 200px; overflow-y: auto;">
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <label class="form-label">推荐标准方法</label>
                <div class="position-relative">
                    <input type="text" class="form-control standard-method-search"
                           placeholder="搜索标准方法..." autocomplete="off"
                           data-config-id="${configId}">
                    <input type="hidden" class="standard-method-id" value="">
                    <div class="dropdown-menu standard-method-dropdown" style="width: 100%; max-height: 200px; overflow-y: auto;">
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">优先级</label>
                <select class="form-select priority-select">
                    <option value="1">高</option>
                    <option value="2">中</option>
                    <option value="3">低</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-outline-danger d-block w-100"
                        onclick="removeRecommendationConfig(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    const $config = $(configHtml);
    $('#recommendationConfigs').append($config);

    // 初始化搜索功能
    initSearchableSelects($config);

    // 如果有现有数据，填充表单
    if (existingData) {
        const $detectionInput = $config.find('.detection-item-search');
        const $detectionId = $config.find('.detection-item-id');
        const $methodInput = $config.find('.standard-method-search');
        const $methodId = $config.find('.standard-method-id');

        // 设置检测项目
        if (existingData.detection_item_id && existingData.detection_item_name) {
            $detectionInput.val(existingData.detection_item_display_name || existingData.detection_item_name);
            $detectionId.val(existingData.detection_item_id);
        }

        // 设置标准方法
        if (existingData.recommended_standard_method_id && existingData.standard_name) {
            $methodInput.val(`${existingData.full_standard_number} - ${existingData.standard_name}`);
            $methodId.val(existingData.recommended_standard_method_id);
        }

        // 设置优先级
        $config.find('.priority-select').val(existingData.priority || 1);
    }
}

// 移除推荐配置
function removeRecommendationConfig(button) {
    $(button).closest('.recommendation-config').remove();
}

// 保存评价方法
function saveEvaluationMethod() {
    const formData = {
        name: $('#evaluationName').val(),
        description: $('#evaluationDescription').val(),
        applicable_scope_id: $('#evaluationScope').val() || null,
        created_by: $('#evaluationCreatedBy').val(),
        recommendations: []
    };

    // 收集推荐配置
    $('.recommendation-config').each(function() {
        const config = $(this);

        // 检查是否是新的搜索式选择框
        const $detectionId = config.find('.detection-item-id');
        const $methodId = config.find('.standard-method-id');

        if ($detectionId.length > 0 && $methodId.length > 0) {
            // 新的搜索式选择框
            const itemId = $detectionId.val();
            const methodId = $methodId.val();
            const priority = config.find('.priority-select').val();

            if (itemId && methodId) {
                formData.recommendations.push({
                    detection_item_id: parseInt(itemId),
                    recommended_standard_method_id: parseInt(methodId),
                    priority: parseInt(priority)
                });
            }
        } else {
            // 旧的选择框（向后兼容）
            const itemId = config.find('.detection-item-select').val();
            const methodId = config.find('.standard-method-select').val();
            const priority = config.find('.priority-select').val();

            if (itemId && methodId) {
                // 需要根据检测项目名称获取ID
                const itemName = config.find('.detection-item-select option:selected').text();
                const item = allItems.find(i => i.display_name === itemName);

                if (item) {
                    formData.recommendations.push({
                        detection_item_id: item.id,
                        recommended_standard_method_id: parseInt(methodId),
                        priority: parseInt(priority)
                    });
                }
            }
        }
    });

    // 检查是否为编辑模式
    const evaluationId = $('#evaluationId').val();
    const isEdit = evaluationId && evaluationId !== '';

    const url = isEdit ? `/api/evaluation_methods/${evaluationId}` : '/api/evaluation_methods';
    const method = isEdit ? 'PUT' : 'POST';

    apiRequest(url, {
        method: method,
        body: JSON.stringify(formData)
    })
    .then(data => {
        if (data.success) {
            $('#evaluationModal').modal('hide');
            loadEvaluationMethods();
            alert(data.message);

            // 清空表单
            $('#evaluationForm')[0].reset();
            $('#evaluationId').val('');
            $('#evaluationModalTitle').text('创建评价方法');
        } else {
            alert('保存失败：' + data.message);
        }
    });
}

// 获取推荐
function getRecommendations() {
    const evaluationMethodId = $('#evaluationMethodSelect').val();
    const applicableScopeId = $('#applicableScopeSelect').val();
    const detectionItemsText = $('#detectionItemsInput').val().trim();

    if (!evaluationMethodId) {
        alert('请选择评价方法');
        return;
    }

    if (!applicableScopeId) {
        alert('请选择适用范围');
        return;
    }

    if (!detectionItemsText) {
        alert('请输入检测项目');
        return;
    }

    // 显示加载状态
    $('#recommendationResults').show();
    $('#recommendationContent').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="mt-2 text-muted">正在基于评价方法推荐配置进行智能分析...</p>
        </div>
    `);

    apiRequest('/api/intelligent_recommendations', {
        method: 'POST',
        body: JSON.stringify({
            evaluation_method_id: parseInt(applicableScopeId),  // 使用选中的评价方法ID
            applicable_scope_id: parseInt($('#applicableScopeSelect option:selected').data('scope-id')),
            detection_items_text: detectionItemsText
        })
    })
    .then(data => {
        if (data.success) {
            displayRecommendations(data.data);
        } else {
            $('#recommendationContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    获取推荐失败：${data.message}
                </div>
            `);
        }
    })
    .catch(error => {
        console.error('获取推荐失败:', error);
        $('#recommendationContent').html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                获取推荐失败：网络错误
            </div>
        `);
    });
}

// 显示推荐结果
function displayRecommendations(data) {
    const content = $('#recommendationContent');
    content.empty();

    if (data.recommended_schemes.length === 0) {
        content.html(`
            <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i>
                <strong>未找到匹配的推荐方法</strong><br>
                该评价方法中没有针对输入检测项目的推荐配置。
            </div>
        `);
        return;
    }

    // 计算匹配率颜色
    const matchRate = data.match_rate;
    const matchRateClass = matchRate >= 0.8 ? 'success' : matchRate >= 0.5 ? 'warning' : 'danger';

    let html = `
        <div class="alert alert-info">
            <h6><i class="fas fa-chart-line"></i> 智能推荐分析结果</h6>
            <div class="row">
                <div class="col-md-6">
                    <strong>评价方法：</strong>${data.evaluation_method.name}<br>
                    <strong>适用范围：</strong>${data.evaluation_method.scope_name}<br>
                    <strong>输入项目：</strong>${data.input_items.join('、')}
                </div>
                <div class="col-md-6">
                    <strong>匹配项目：</strong><span class="text-success">${data.matched_items.join('、') || '无'}</span><br>
                    <strong>未匹配项目：</strong><span class="text-danger">${data.unmatched_items.join('、') || '无'}</span><br>
                    <strong>匹配率：</strong><span class="badge bg-${matchRateClass}">${(matchRate * 100).toFixed(1)}%</span>
                </div>
            </div>
        </div>
    `;

    // 显示推荐方案（按覆盖项目数量排序）
    html += `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-star"></i> 推荐检测方案
                    <small class="text-muted">（按覆盖项目数量排序，优先推荐一个方法检测多个项目）</small>
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group">
    `;

    data.recommended_schemes.forEach((scheme, index) => {
        const priorityText = scheme.priority === 1 ? '高' : scheme.priority === 2 ? '中' : '低';
        const priorityClass = scheme.priority === 1 ? 'success' : scheme.priority === 2 ? 'warning' : 'secondary';
        const coverageClass = scheme.covered_items_count > 1 ? 'success' : 'info';

        // 构建检测项目列表
        const itemsList = scheme.covered_items.map(item => item.display_name).join('、');

        html += `
            <div class="list-group-item ${index === 0 ? 'list-group-item-light border-primary' : ''}">
                <div class="d-flex w-100 justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            ${index === 0 ? '<i class="fas fa-crown text-warning me-1"></i>' : ''}
                            ${scheme.full_standard_number}
                            ${scheme.is_informative_appendix ? '<span class="badge bg-warning ms-2">资料性附录</span>' : ''}
                        </h6>
                        <p class="mb-2">${scheme.standard_name}</p>
                        <div class="recommendation-scheme-text">
                            <strong>${scheme.full_standard_number}</strong>，检测<strong>${itemsList}</strong>等项目
                        </div>
                        ${scheme.reason ? `<small class="text-muted"><br>推荐理由：${scheme.reason}</small>` : ''}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${priorityClass}">${priorityText}优先级</span><br>
                        <span class="badge bg-${coverageClass} mt-1">覆盖${scheme.covered_items_count}个项目</span>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    // 显示检测项目详情（支持悬浮窗）
    html += `
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> 检测项目详情
                    <small class="text-muted">（鼠标悬停查看该项目的所有可用方法）</small>
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
    `;

    data.input_items.forEach(item => {
        const isMatched = data.matched_items.includes(item);
        const itemClass = isMatched ? 'success' : 'danger';
        const itemIcon = isMatched ? 'check-circle' : 'times-circle';

        html += `
            <div class="col-md-4 mb-2">
                <span class="badge bg-${itemClass} item-hover-trigger"
                      data-item-name="${item}"
                      data-scope-id="${$('#applicableScopeSelect option:selected').data('scope-id')}"
                      style="cursor: pointer;">
                    <i class="fas fa-${itemIcon} me-1"></i>${item}
                </span>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    // 添加操作按钮
    html += `
        <div class="mt-3 text-center">
            <button class="btn btn-outline-secondary" onclick="clearRecommendationForm()">
                <i class="fas fa-eraser"></i> 重新推荐
            </button>
        </div>
    `;

    content.html(html);

    // 绑定悬浮窗事件
    bindItemHoverEvents();
}

// 显示推荐模态框
function showRecommendationModal() {
    updateScopeSelects();
    updateItemSelects();
    $('#recommendationModal').modal('show');
}

// 模态框推荐
function getModalRecommendations() {
    const detectionType = $('#modalDetectionType').val();
    const detectionItems = $('#modalDetectionItems').val();

    if (!detectionType || !detectionItems || detectionItems.length === 0) {
        alert('请选择检测类型和检测项目');
        return;
    }

    $('#modalRecommendationResults').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在分析推荐...</p>
        </div>
    `);

    apiRequest('/api/intelligent_recommendations', {
        method: 'POST',
        body: JSON.stringify({
            detection_type: detectionType,
            detection_items: detectionItems
        })
    })
    .then(data => {
        if (data.success) {
            displayModalRecommendations(data.data);
        } else {
            $('#modalRecommendationResults').html(`
                <div class="alert alert-danger">
                    获取推荐失败：${data.message}
                </div>
            `);
        }
    });
}

// 显示模态框推荐结果
function displayModalRecommendations(data) {
    const content = $('#modalRecommendationResults');

    if (data.recommendations.length === 0) {
        content.html(`
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>未找到匹配的推荐方法</p>
            </div>
        `);
        return;
    }

    let html = `
        <div class="alert alert-info mb-3">
            <strong>检测类型：</strong>${data.detection_type}<br>
            <strong>检测项目：</strong>${data.detection_items.join('、')}<br>
            <strong>推荐方法数量：</strong>${data.total_methods}个
        </div>
    `;

    data.recommendations.forEach(rec => {
        const statusClass = rec.recommended ? 'border-success' : 'border-warning';
        const statusBadge = rec.recommended ? 'bg-success' : 'bg-warning';
        const statusText = rec.recommended ? '推荐使用' : '不推荐（有新方法）';

        html += `
            <div class="card mb-3 ${statusClass}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${rec.full_standard_number}</h6>
                    <span class="badge ${statusBadge}">${statusText}</span>
                </div>
                <div class="card-body">
                    <p class="card-text">${rec.standard_name}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>覆盖项目：</strong>${rec.covered_item_names}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>覆盖率：</strong>${rec.covered_items}/${data.detection_items.length}
                                (${Math.round(rec.covered_items / data.detection_items.length * 100)}%)
                            </small>
                        </div>
                    </div>
                    ${rec.is_informative_appendix ? `
                        <div class="mt-2">
                            <span class="badge bg-warning">资料性附录方法</span>
                        </div>
                    ` : ''}
                    ${rec.alternatives && rec.alternatives.length > 0 ? `
                        <div class="mt-3">
                            <h6 class="text-danger">可用替代方法：</h6>
                            ${rec.alternatives.map(alt => `
                                <div class="alert alert-light py-2">
                                    <strong>${alt.new_method_number}</strong> - ${alt.new_method_name}
                                    ${alt.remark ? `<br><small class="text-muted">${alt.remark}</small>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });

    content.html(html);
}

// ==================== 评价方法编辑和删除功能 ====================

// 编辑评价方法
function editEvaluationMethod(methodId) {
    console.log('开始编辑评价方法，ID:', methodId);

    // 保存原始模态框内容（如果还没有保存的话）
    if (!$('#evaluationModal .modal-body').data('original-content')) {
        $('#evaluationModal .modal-body').data('original-content', $('#evaluationModal .modal-body').html());
    }

    // 显示加载状态
    const loadingHtml = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载评价方法详情...</p>
        </div>
    `;

    // 先显示模态框和加载状态
    $('#evaluationModal .modal-body').html(loadingHtml);
    $('#evaluationModalTitle').text('加载中...');
    $('#evaluationModal').modal('show');

    // 获取评价方法详情
    apiRequest(`/api/evaluation_methods/${methodId}`)
        .then(data => {
            console.log('API响应:', data);

            if (data && data.success) {
                // 恢复模态框原始内容
                const originalContent = $('#evaluationModal .modal-body').data('original-content');
                $('#evaluationModal .modal-body').html(originalContent);

                // 显示编辑模态框（推荐配置将通过无限滚动加载）
                showEditEvaluationModal(data.data, methodId);
            } else {
                $('#evaluationModal').modal('hide');
                const errorMsg = data ? data.message : '未知错误';
                alert('获取评价方法详情失败：' + errorMsg);
            }
        })
        .catch(error => {
            console.error('获取评价方法详情失败:', error);
            $('#evaluationModal').modal('hide');

            // 检查是否是认证错误
            if (error.message && (error.message.includes('401') || error.message.includes('登录'))) {
                alert('登录已过期，请重新登录');
                window.location.href = '/login';
            } else {
                alert('获取评价方法详情失败：' + (error.message || '网络错误或数据异常'));
            }
        });
}

// 显示编辑评价方法模态框
function showEditEvaluationModal(methodData, methodId) {
    try {
        // 数据验证
        if (!methodData || !methodData.id) {
            throw new Error('评价方法数据无效');
        }

        // 不要重新生成HTML，直接使用现有的表单元素
        // 填充表单数据
        $('#evaluationId').val(methodData.id);
        $('#evaluationName').val(methodData.name || '');
        $('#evaluationDescription').val(methodData.description || '');

        // 确保适用范围选项已加载，然后设置值
        updateScopeSelects();
        setTimeout(() => {
            $('#evaluationScope').val(methodData.applicable_scope_id || '');
        }, 100);

        // 清空推荐配置容器并初始化无限滚动
        $('#recommendationConfigs').empty();

        // 初始化推荐配置的无限滚动加载
        initRecommendationInfiniteScroll(methodId, methodData.total_recommendations || 0);

        // 更新模态框标题
        $('#evaluationModalTitle').text('编辑评价方法');

        // 显示模态框
        $('#evaluationModal').modal('show');

    } catch (error) {
        console.error('显示编辑评价方法模态框失败:', error);
        $('#evaluationModal').modal('hide');
        alert('显示编辑界面失败：' + error.message);
    }
}

// 删除评价方法
function deleteEvaluationMethod(methodId) {
    // 获取评价方法信息用于确认
    apiRequest(`/api/evaluation_methods/${methodId}`)
        .then(data => {
            if (data.success) {
                const methodName = data.data.name;

                // 确认删除
                if (confirm(`确定要删除评价方法"${methodName}"吗？\n\n此操作将同时删除该评价方法下的所有推荐配置，且无法恢复。`)) {
                    performDeleteEvaluationMethod(methodId);
                }
            } else {
                alert('获取评价方法信息失败：' + data.message);
            }
        })
        .catch(error => {
            // 如果获取信息失败，仍然允许删除，但使用通用确认信息
            if (confirm('确定要删除这个评价方法吗？\n\n此操作将同时删除该评价方法下的所有推荐配置，且无法恢复。')) {
                performDeleteEvaluationMethod(methodId);
            }
        });
}

// 执行删除操作
function performDeleteEvaluationMethod(methodId) {
    apiRequest(`/api/evaluation_methods/${methodId}`, {
        method: 'DELETE'
    })
    .then(data => {
        if (data.success) {
            alert('评价方法删除成功！');
            loadEvaluationMethods(); // 重新加载列表
        } else {
            alert('删除失败：' + data.message);
        }
    })
    .catch(error => {
        alert('删除失败：网络错误');
    });
}

// ==================== Excel导入推荐配置功能 ====================

let currentEvaluationId = null;

// 显示Excel导入模态框
function showImportRecommendationsModal() {
    // 获取当前编辑的评价方法ID
    currentEvaluationId = $('#evaluationId').val();

    if (!currentEvaluationId) {
        alert('请先保存评价方法，然后再进行导入操作');
        return;
    }

    // 重置模态框状态
    $('#importFile').val('');
    $('#startImportBtn').prop('disabled', true);
    $('#importProgress').hide();
    $('#importResult').hide();

    $('#importRecommendationsModal').modal('show');
}

// 下载Excel模板
function downloadTemplate() {
    window.open('/api/evaluation_recommendations_template', '_blank');
}

// 处理文件选择
function handleFileSelect(input) {
    const file = input.files[0];
    const startBtn = $('#startImportBtn');

    if (file) {
        // 验证文件格式
        const fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
            alert('请选择Excel文件（.xlsx或.xls格式）');
            input.value = '';
            startBtn.prop('disabled', true);
            return;
        }

        // 验证文件大小（限制10MB）
        if (file.size > 10 * 1024 * 1024) {
            alert('文件大小不能超过10MB');
            input.value = '';
            startBtn.prop('disabled', true);
            return;
        }

        startBtn.prop('disabled', false);
    } else {
        startBtn.prop('disabled', true);
    }
}

// 开始导入
function startImport() {
    const fileInput = $('#importFile')[0];
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择要导入的Excel文件');
        return;
    }

    if (!currentEvaluationId) {
        alert('评价方法ID无效');
        return;
    }

    // 显示进度条
    $('#importProgress').show();
    $('#importResult').hide();
    $('#startImportBtn').prop('disabled', true);

    // 更新进度条
    updateProgress(20, '正在上传文件...');

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 发送请求
    fetch(`/api/evaluation_methods/${currentEvaluationId}/import_recommendations`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        updateProgress(60, '正在处理数据...');
        return response.json();
    })
    .then(data => {
        updateProgress(100, '导入完成');

        setTimeout(() => {
            $('#importProgress').hide();
            showImportResult(data);
            $('#startImportBtn').prop('disabled', false);

            // 如果导入成功，刷新推荐配置列表
            if (data.success) {
                // 这里可以添加刷新逻辑
                setTimeout(() => {
                    $('#importRecommendationsModal').modal('hide');
                    alert('导入成功！请刷新页面查看最新数据。');
                }, 2000);
            }
        }, 1000);
    })
    .catch(error => {
        console.error('导入失败:', error);
        $('#importProgress').hide();
        showImportResult({
            success: false,
            message: '导入失败：网络错误'
        });
        $('#startImportBtn').prop('disabled', false);
    });
}

// 更新进度条
function updateProgress(percent, message) {
    const progressBar = $('.progress-bar');
    progressBar.css('width', percent + '%');
    progressBar.attr('aria-valuenow', percent);

    if (message) {
        progressBar.text(message);
    }
}

// 显示导入结果
function showImportResult(result) {
    const resultDiv = $('#importResult');
    const contentDiv = $('#importResultContent');

    let html = '';

    if (result.success) {
        const data = result.data;
        html = `
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle me-2"></i>导入成功</h6>
                <p class="mb-2">${result.message}</p>
                <ul class="mb-0">
                    <li>总行数：${data.total_rows}</li>
                    <li>成功导入：${data.success_count} 条</li>
                    <li>失败：${data.error_count} 条</li>
                </ul>
            </div>
        `;

        // 显示错误详情
        if (data.errors && data.errors.length > 0) {
            html += `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>错误详情</h6>
                    <ul class="mb-0">
            `;

            data.errors.slice(0, 10).forEach(error => {
                html += `<li>${error}</li>`;
            });

            if (data.errors.length > 10) {
                html += `<li>... 还有 ${data.errors.length - 10} 个错误</li>`;
            }

            html += `
                    </ul>
                </div>
            `;
        }

        // 显示成功导入的项目
        if (data.processed_items && data.processed_items.length > 0) {
            html += `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>成功导入的配置（前10条）</h6>
                    <ul class="mb-0">
            `;

            data.processed_items.slice(0, 10).forEach(item => {
                html += `<li>${item.item_name} → ${item.method_name}</li>`;
            });

            if (data.processed_items.length > 10) {
                html += `<li>... 还有 ${data.processed_items.length - 10} 条配置</li>`;
            }

            html += `
                    </ul>
                </div>
            `;
        }
    } else {
        html = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-times-circle me-2"></i>导入失败</h6>
                <p class="mb-0">${result.message}</p>
            </div>
        `;
    }

    contentDiv.html(html);
    resultDiv.show();
}

// ==================== 无限滚动推荐配置加载 ====================

// 推荐配置无限滚动状态
let recommendationScrollState = {
    methodId: null,
    page: 1,
    loading: false,
    hasMore: true,
    totalCount: 0,
    loadedCount: 0
};

// 初始化推荐配置无限滚动
function initRecommendationInfiniteScroll(methodId, totalCount) {
    // 重置状态
    recommendationScrollState = {
        methodId: methodId,
        page: 1,
        loading: false,
        hasMore: totalCount > 0,
        totalCount: totalCount,
        loadedCount: 0
    };

    // 创建滚动容器和加载指示器
    const scrollContainer = `
        <div id="recommendationScrollContainer" style="max-height: 400px; overflow-y: auto;">
            <div id="recommendationList"></div>
            <div id="recommendationLoadMore" class="text-center py-3" style="display: none;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span class="ms-2 text-muted">正在加载更多推荐配置...</span>
            </div>
            <div id="recommendationLoadComplete" class="text-center py-3 text-muted" style="display: none;">
                <i class="fas fa-check-circle text-success"></i>
                <span class="ms-2">已加载全部推荐配置</span>
            </div>
        </div>
        <div class="d-flex gap-2 mt-3">
            <button type="button" class="btn btn-outline-primary" onclick="addRecommendationConfig()">
                <i class="fas fa-plus me-1"></i>添加推荐配置
            </button>
            <button type="button" class="btn btn-outline-success" onclick="showImportRecommendationsModal()">
                <i class="fas fa-upload me-1"></i>Excel导入推荐配置
            </button>
        </div>
    `;

    $('#recommendationConfigs').html(scrollContainer);

    // 绑定滚动事件
    $('#recommendationScrollContainer').on('scroll', handleRecommendationScroll);

    // 显示总数信息
    if (totalCount > 0) {
        const infoHtml = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle"></i>
                该评价方法共有 <strong>${totalCount}</strong> 个推荐配置，将按需加载以提升性能。
            </div>
        `;
        $('#recommendationList').append(infoHtml);
    }

    // 加载第一页数据
    loadMoreRecommendations();
}

// 处理滚动事件
function handleRecommendationScroll() {
    const container = $('#recommendationScrollContainer');
    const scrollTop = container.scrollTop();
    const scrollHeight = container[0].scrollHeight;
    const clientHeight = container.height();

    // 当滚动到底部附近时加载更多
    if (scrollTop + clientHeight >= scrollHeight - 100) {
        loadMoreRecommendations();
    }
}

// 加载更多推荐配置
function loadMoreRecommendations() {
    if (recommendationScrollState.loading || !recommendationScrollState.hasMore) {
        return;
    }

    recommendationScrollState.loading = true;
    $('#recommendationLoadMore').show();

    const url = `/api/evaluation_methods/${recommendationScrollState.methodId}/recommendations`;
    const params = new URLSearchParams({
        page: recommendationScrollState.page,
        size: 20
    });

    apiRequest(`${url}?${params}`)
        .then(data => {
            if (data.success) {
                const recommendations = data.data.recommendations;
                const pagination = data.data.pagination;

                // 渲染推荐配置
                recommendations.forEach(rec => {
                    addRecommendationConfigToList(rec);
                });

                // 更新状态
                recommendationScrollState.page++;
                recommendationScrollState.hasMore = pagination.has_next;
                recommendationScrollState.loadedCount += recommendations.length;

                // 更新UI状态
                if (!recommendationScrollState.hasMore) {
                    $('#recommendationLoadComplete').show();
                }
            } else {
                console.error('加载推荐配置失败:', data.message);
                alert('加载推荐配置失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('加载推荐配置失败:', error);
            alert('加载推荐配置失败：网络错误');
        })
        .finally(() => {
            recommendationScrollState.loading = false;
            $('#recommendationLoadMore').hide();
        });
}

// 将推荐配置添加到列表中
function addRecommendationConfigToList(recData) {
    const configHtml = `
        <div class="recommendation-config row g-3 mb-3 border rounded p-3" data-rec-id="${recData.id}">
            <div class="col-md-4">
                <label class="form-label">检测项目</label>
                <select class="form-select detection-item-select" disabled>
                    <option value="${recData.detection_item_id}" selected>
                        ${recData.detection_item_display_name || recData.detection_item_name}
                    </option>
                </select>
                <small class="text-muted">ID: ${recData.detection_item_id}</small>
            </div>
            <div class="col-md-4">
                <label class="form-label">推荐标准方法</label>
                <select class="form-select standard-method-select" disabled>
                    <option value="${recData.recommended_standard_method_id}" selected>
                        ${recData.full_standard_number} - ${recData.standard_name}
                    </option>
                </select>
                <small class="text-muted">ID: ${recData.recommended_standard_method_id}</small>
            </div>
            <div class="col-md-3">
                <label class="form-label">优先级</label>
                <select class="form-select priority-select">
                    <option value="1" ${recData.priority == 1 ? 'selected' : ''}>高</option>
                    <option value="2" ${recData.priority == 2 ? 'selected' : ''}>中</option>
                    <option value="3" ${recData.priority == 3 ? 'selected' : ''}>低</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-outline-danger d-block w-100"
                        onclick="removeRecommendationConfig(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            ${recData.reason ? `
                <div class="col-12">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        推荐理由：${recData.reason}
                    </small>
                </div>
            ` : ''}
        </div>
    `;

    $('#recommendationList').append(configHtml);
}

// ==================== 悬浮窗功能 ====================

// 绑定检测项目悬浮事件
function bindItemHoverEvents() {
    $('.item-hover-trigger').hover(
        function() {
            const itemName = $(this).data('item-name');
            const scopeId = $(this).data('scope-id');
            showItemMethodsPopover(this, itemName, scopeId);
        },
        function() {
            hideItemMethodsPopover();
        }
    );
}

// 显示检测项目方法悬浮窗
function showItemMethodsPopover(element, itemName, scopeId) {
    // 移除现有的悬浮窗
    $('.item-methods-popover').remove();

    if (!scopeId) {
        return;
    }

    // 创建悬浮窗
    const popover = $(`
        <div class="item-methods-popover position-absolute bg-white border rounded shadow-lg p-3"
             style="z-index: 1050; min-width: 300px; max-width: 400px;">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">${itemName} - 可用方法</h6>
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
            <div class="popover-content">
                <small class="text-muted">正在加载...</small>
            </div>
        </div>
    `);

    // 定位悬浮窗
    const $element = $(element);
    const offset = $element.offset();
    const elementHeight = $element.outerHeight();

    popover.css({
        top: offset.top + elementHeight + 5,
        left: offset.left
    });

    $('body').append(popover);

    // 加载方法数据
    apiRequest(`/api/detection_items/${encodeURIComponent(itemName)}/alternatives?scope_id=${scopeId}`)
        .then(data => {
            if (data.success) {
                displayItemMethods(popover, data.data);
            } else {
                popover.find('.popover-content').html(`
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        加载失败：${data.message}
                    </div>
                `);
            }
        })
        .catch(error => {
            console.error('加载检测项目方法失败:', error);
            popover.find('.popover-content').html(`
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    网络错误
                </div>
            `);
        })
        .finally(() => {
            popover.find('.spinner-border').remove();
        });
}

// 隐藏悬浮窗
function hideItemMethodsPopover() {
    setTimeout(() => {
        if (!$('.item-methods-popover:hover').length) {
            $('.item-methods-popover').remove();
        }
    }, 200);
}

// 显示检测项目的方法列表
function displayItemMethods(popover, data) {
    const methods = data.methods;

    if (methods.length === 0) {
        popover.find('.popover-content').html(`
            <div class="text-muted">
                <i class="fas fa-info-circle"></i>
                该项目在当前适用范围内无可用方法
            </div>
        `);
        return;
    }

    let html = `
        <div class="mb-2">
            <small class="text-muted">
                共找到 ${data.total_methods} 个方法，其中 ${data.available_methods} 个可用
            </small>
        </div>
        <div class="method-list" style="max-height: 200px; overflow-y: auto;">
    `;

    methods.forEach(method => {
        const availabilityClass = method.is_available ? 'success' : 'danger';
        const availabilityIcon = method.is_available ? 'check-circle' : 'times-circle';

        html += `
            <div class="method-item border-bottom py-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${method.full_standard_number}</div>
                        <div class="small text-muted">${method.standard_name}</div>
                        ${method.is_informative_appendix ? '<span class="badge bg-warning">资料性附录</span>' : ''}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${availabilityClass}">
                            <i class="fas fa-${availabilityIcon} me-1"></i>${method.availability_reason}
                        </span>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';

    popover.find('.popover-content').html(html);

    // 绑定悬浮窗的鼠标事件，防止意外关闭
    popover.hover(
        function() {
            // 鼠标进入悬浮窗时不关闭
        },
        function() {
            hideItemMethodsPopover();
        }
    );
}

// ==================== 搜索式选择框功能 ====================

// 初始化搜索式选择框
function initSearchableSelects($config) {
    const $detectionInput = $config.find('.detection-item-search');
    const $detectionDropdown = $config.find('.detection-item-dropdown');
    const $detectionId = $config.find('.detection-item-id');

    const $methodInput = $config.find('.standard-method-search');
    const $methodDropdown = $config.find('.standard-method-dropdown');
    const $methodId = $config.find('.standard-method-id');

    // 检测项目搜索
    let detectionSearchTimeout;
    $detectionInput.on('input focus', function() {
        clearTimeout(detectionSearchTimeout);
        const query = $(this).val().trim();

        detectionSearchTimeout = setTimeout(() => {
            searchDetectionItems(query, $detectionDropdown, $detectionInput, $detectionId);
        }, 300);
    });

    // 标准方法搜索
    let methodSearchTimeout;
    $methodInput.on('input focus', function() {
        clearTimeout(methodSearchTimeout);
        const query = $(this).val().trim();

        methodSearchTimeout = setTimeout(() => {
            searchStandardMethods(query, $methodDropdown, $methodInput, $methodId);
        }, 300);
    });

    // 点击外部关闭下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest($config).length) {
            $detectionDropdown.removeClass('show');
            $methodDropdown.removeClass('show');
        }
    });
}

// 搜索检测项目
function searchDetectionItems(query, $dropdown, $input, $hiddenInput) {
    $dropdown.html('<div class="dropdown-item text-center"><div class="spinner-border spinner-border-sm"></div> 搜索中...</div>');
    $dropdown.addClass('show');

    const url = '/api/detection_items/search';
    const params = new URLSearchParams({
        q: query,
        limit: 20
    });

    apiRequest(`${url}?${params}`)
        .then(data => {
            if (data.success) {
                displayDetectionItemResults(data.data, $dropdown, $input, $hiddenInput);
            } else {
                $dropdown.html('<div class="dropdown-item text-danger">搜索失败</div>');
            }
        })
        .catch(error => {
            console.error('搜索检测项目失败:', error);
            $dropdown.html('<div class="dropdown-item text-danger">网络错误</div>');
        });
}

// 显示检测项目搜索结果
function displayDetectionItemResults(items, $dropdown, $input, $hiddenInput) {
    if (items.length === 0) {
        $dropdown.html('<div class="dropdown-item text-muted">未找到匹配的检测项目</div>');
        return;
    }

    let html = '';
    items.forEach(item => {
        const displayText = item.display_name || item.name;
        html += `
            <div class="dropdown-item" style="cursor: pointer;"
                 data-id="${item.id}" data-name="${item.name}" data-display="${displayText}">
                <div class="fw-bold">${displayText}</div>
                ${item.name !== displayText ? `<small class="text-muted">${item.name}</small>` : ''}
            </div>
        `;
    });

    $dropdown.html(html);

    // 绑定点击事件
    $dropdown.find('.dropdown-item').on('click', function() {
        const id = $(this).data('id');
        const displayText = $(this).data('display');

        $input.val(displayText);
        $hiddenInput.val(id);
        $dropdown.removeClass('show');
    });
}

// 搜索标准方法
function searchStandardMethods(query, $dropdown, $input, $hiddenInput) {
    $dropdown.html('<div class="dropdown-item text-center"><div class="spinner-border spinner-border-sm"></div> 搜索中...</div>');
    $dropdown.addClass('show');

    const url = '/api/standard_methods/search';
    const params = new URLSearchParams({
        q: query,
        limit: 20
    });

    apiRequest(`${url}?${params}`)
        .then(data => {
            if (data.success) {
                displayStandardMethodResults(data.data, $dropdown, $input, $hiddenInput);
            } else {
                $dropdown.html('<div class="dropdown-item text-danger">搜索失败</div>');
            }
        })
        .catch(error => {
            console.error('搜索标准方法失败:', error);
            $dropdown.html('<div class="dropdown-item text-danger">网络错误</div>');
        });
}

// 显示标准方法搜索结果
function displayStandardMethodResults(methods, $dropdown, $input, $hiddenInput) {
    if (methods.length === 0) {
        $dropdown.html('<div class="dropdown-item text-muted">未找到匹配的标准方法</div>');
        return;
    }

    let html = '';
    methods.forEach(method => {
        html += `
            <div class="dropdown-item" style="cursor: pointer;"
                 data-id="${method.id}" data-display="${method.display_text}">
                <div class="fw-bold">${method.full_standard_number}</div>
                <small class="text-muted">${method.standard_name}</small>
            </div>
        `;
    });

    $dropdown.html(html);

    // 绑定点击事件
    $dropdown.find('.dropdown-item').on('click', function() {
        const id = $(this).data('id');
        const displayText = $(this).data('display');

        $input.val(displayText);
        $hiddenInput.val(id);
        $dropdown.removeClass('show');
    });
}

</script>
{% endblock %}

{% block extra_css %}
<style>
/* 推荐方案样式 */
.recommendation-scheme-text {
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid #007bff;
    font-size: 0.95rem;
}

/* 检测项目悬浮触发器 */
.item-hover-trigger {
    transition: all 0.2s ease;
}

.item-hover-trigger:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 悬浮窗样式 */
.item-methods-popover {
    border: 1px solid #dee2e6;
    background-color: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.method-item {
    transition: background-color 0.2s ease;
}

.method-item:hover {
    background-color: #f8f9fa;
}

.method-item:last-child {
    border-bottom: none !important;
}

/* 推荐方案卡片增强 */
.list-group-item.border-primary {
    border-width: 2px !important;
}

.list-group-item .fas.fa-crown {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .item-methods-popover {
        max-width: 90vw !important;
        left: 5vw !important;
    }
}
</style>
{% endblock %}
