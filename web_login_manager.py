"""
康达价格管理系统 - Web登录管理模块
处理Web端的登录、验证码获取、会话管理
"""

import os
import io
import json
import base64
from datetime import datetime
from network_manager import HttpRequestManager


class WebLoginManager:
    """Web端登录管理器"""
    
    def __init__(self):
        self.http_manager = HttpRequestManager()
        self.login_sessions = {}  # 存储登录会话信息
    
    def get_captcha_for_web(self):
        """获取验证码图片（Web端使用）
        
        Returns:
            dict: {
                'success': bool,
                'image_base64': str,  # base64编码的图片
                'jsessionid': str,
                'message': str
            }
        """
        try:
            success, image_data, jsessionid, message = self.http_manager.get_captcha_image()
            
            if success and image_data:
                # 将图片数据转换为base64编码
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                
                return {
                    'success': True,
                    'image_base64': image_base64,
                    'jsessionid': jsessionid,
                    'message': message
                }
            else:
                return {
                    'success': False,
                    'image_base64': None,
                    'jsessionid': None,
                    'message': message
                }
                
        except Exception as e:
            # 确保错误消息不包含特殊字符，避免编码问题
            error_msg = str(e).encode('utf-8', errors='ignore').decode('utf-8')
            return {
                'success': False,
                'image_base64': None,
                'jsessionid': None,
                'message': f"获取验证码时发生错误: {error_msg}"
            }
    
    def login_for_web(self, username, password, captcha, jsessionid):
        """Web端登录处理
        
        Args:
            username: 用户名
            password: 密码
            captcha: 验证码
            jsessionid: 验证码会话ID
            
        Returns:
            dict: {
                'success': bool,
                'message': str,
                'user_info': dict,
                'session_key': str  # 用于Web会话管理
            }
        """
        try:
            success, message, user_info = self.http_manager.login(
                username, password, captcha, jsessionid
            )
            
            if success:
                # 生成Web会话密钥
                session_key = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{username}"
                
                # 存储登录会话信息
                self.login_sessions[session_key] = {
                    'username': username,
                    'user_info': user_info,
                    'login_time': datetime.now(),
                    'http_manager': self.http_manager,
                    'cookies': dict(self.http_manager.session.cookies)
                }
                
                return {
                    'success': True,
                    'message': message,
                    'user_info': user_info,
                    'session_key': session_key
                }
            else:
                return {
                    'success': False,
                    'message': message,
                    'user_info': None,
                    'session_key': None
                }
                
        except Exception as e:
            # 确保错误消息不包含特殊字符，避免编码问题
            error_msg = str(e).encode('utf-8', errors='ignore').decode('utf-8')
            return {
                'success': False,
                'message': f"登录过程中发生错误: {error_msg}",
                'user_info': None,
                'session_key': None
            }
    
    def get_session_info(self, session_key):
        """获取会话信息
        
        Args:
            session_key: 会话密钥
            
        Returns:
            dict: 会话信息，如果会话不存在返回None
        """
        return self.login_sessions.get(session_key)
    
    def is_session_valid(self, session_key):
        """检查会话是否有效
        
        Args:
            session_key: 会话密钥
            
        Returns:
            bool: 会话是否有效
        """
        session_info = self.get_session_info(session_key)
        if not session_info:
            return False
        
        # 检查会话是否超时（24小时）
        login_time = session_info['login_time']
        now = datetime.now()
        if (now - login_time).total_seconds() > 24 * 3600:
            # 会话超时，清理
            self.logout_session(session_key)
            return False
        
        return True
    
    def get_http_manager(self, session_key):
        """获取会话对应的HTTP管理器
        
        Args:
            session_key: 会话密钥
            
        Returns:
            HttpRequestManager: HTTP管理器实例，如果会话无效返回None
        """
        if not self.is_session_valid(session_key):
            return None
        
        session_info = self.get_session_info(session_key)
        return session_info['http_manager']
    
    def logout_session(self, session_key):
        """登出会话
        
        Args:
            session_key: 会话密钥
            
        Returns:
            bool: 是否成功登出
        """
        if session_key in self.login_sessions:
            del self.login_sessions[session_key]
            return True
        return False
    
    def get_all_sessions(self):
        """获取所有活跃会话（管理用途）
        
        Returns:
            list: 会话信息列表
        """
        sessions = []
        for session_key, session_info in self.login_sessions.items():
            sessions.append({
                'session_key': session_key,
                'username': session_info['username'],
                'login_time': session_info['login_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'user_info': session_info['user_info']
            })
        return sessions
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        expired_keys = []
        now = datetime.now()
        
        for session_key, session_info in self.login_sessions.items():
            login_time = session_info['login_time']
            if (now - login_time).total_seconds() > 24 * 3600:
                expired_keys.append(session_key)
        
        for key in expired_keys:
            del self.login_sessions[key]
        
        return len(expired_keys)


# 全局Web登录管理器实例
web_login_manager = WebLoginManager()