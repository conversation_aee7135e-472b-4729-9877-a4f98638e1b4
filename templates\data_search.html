{% extends "base.html" %}

{% block title %}数据搜索 - 康达价格管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center pt-2 pb-2 mb-3 border-bottom">
    <h1 class="h4">
        <i class="fas fa-search me-2"></i>数据搜索
    </h1>
</div>

<!-- 紧凑的搜索表单 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-3">
                <form id="search-form" class="row g-2">
                    <div class="col-md-3">
                        <input type="text" class="form-control form-control-sm" id="keyword" name="keyword" 
                               placeholder="项目名称或编号">
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control form-control-sm" id="category" name="category" 
                               placeholder="检测分类">
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary btn-sm me-1" onclick="searchItems(1)">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="col-md-3 text-end">
                        <span id="result-count" class="badge bg-primary">0 个结果</span>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 方法分析功能 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light py-2">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>方法分析
                </h6>
            </div>
            <div class="card-body p-3">
                <div class="row g-2">
                    <div class="col-md-8">
                        <input type="text" class="form-control form-control-sm" id="method-analysis-input" 
                               placeholder="输入方法名称进行分析（支持直接复制完整方法名，自动处理空格差异）">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-info btn-sm w-100" onclick="analyzeMethod()">
                            <i class="fas fa-search me-1"></i>分析方法
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="clearMethodAnalysis()">
                            <i class="fas fa-times"></i>清空
                        </button>
                    </div>
                </div>
                
                <!-- 方法分析结果 -->
                <div id="method-analysis-results" class="mt-3" style="display: none;">
                    <div class="border-top pt-3">
                        <div id="method-analysis-summary" class="mb-3"></div>
                        <div id="method-analysis-details"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div id="search-results">
                    <div class="loading text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                        <p class="mt-2 mb-0">正在加载数据...</p>
                    </div>
                </div>
                
                <!-- 分页和每页显示数量 -->
                <div id="pagination-container" class="d-flex justify-content-between align-items-center p-3 border-top bg-light" style="display: none !important;">
                    <div class="flex-grow-1 d-flex justify-content-center">
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                    <div style="width: 120px;">
                        <select class="form-select form-select-sm" id="limit" name="limit">
                            <option value="20" selected>20条/页</option>
                            <option value="50">50条/页</option>
                            <option value="100">100条/页</option>
                            <option value="200">200条/页</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 子项目详情弹窗 -->
<div class="modal fade" id="childItemModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title h6">
                    <i class="fas fa-info-circle me-2"></i>子项目详情
                </h5>
                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body py-2">
                <div id="child-item-content" class="small">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentPage = 1;
let totalPages = 1;
let currentKeyword = '';
let currentCategory = '';
let currentLimit = 20;

// 搜索项目
function searchItems(page = 1) {
    currentPage = page;
    currentKeyword = document.getElementById('keyword').value.trim();
    currentCategory = document.getElementById('category').value.trim();
    currentLimit = parseInt(document.getElementById('limit').value);
    
    showLoading('search-results', '搜索中...');
    document.getElementById('pagination-container').style.display = 'none';
    
    const params = new URLSearchParams();
    if (currentKeyword) params.append('keyword', currentKeyword);
    if (currentCategory) params.append('category', currentCategory);
    params.append('limit', currentLimit);
    params.append('offset', (currentPage - 1) * currentLimit);
    
    apiRequest(`/api/search_items?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayOptimizedSearchResults(data.items);
                
                // 更新结果计数和分页
                const displayText = data.total > 0 ? `共 ${data.total} 个结果` : '无搜索结果';
                document.getElementById('result-count').textContent = displayText;
                
                if (data.total > currentLimit) {
                    totalPages = Math.ceil(data.total / currentLimit);
                    updatePagination(data.total);
                }
            } else {
                showError('search-results', '搜索失败: ' + data.message);
            }
        })
        .catch(error => {
            showError('search-results', '请求出错: ' + error.message);
        });
}

// 显示优化的搜索结果 - 直接使用带有详细信息的数据
function displayOptimizedSearchResults(items) {
    const container = document.getElementById('search-results');
    
    if (!items || items.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-search fa-2x mb-3"></i><h6>未找到匹配结果</h6></div>';
        return;
    }
    
    let html = `
    <div class="table-responsive">
        <table class="table table-sm table-hover mb-0" style="font-size: 0.95rem;">
            <thead style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                <tr style="line-height: 1.2;">
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="12%">分类</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="8%">编号</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="15%">项目名称</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="8%">项目状态</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="25%">检测方法</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="12%">价格(分析/最低)</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="12%">采样价格</th>
                    <th class="px-2 py-2 text-dark fw-bold border-end" width="6%">方法状态</th>
                    <th class="px-2 py-2 text-dark fw-bold" width="6%">资质</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>`;
    
    container.innerHTML = html;
    const tbody = container.querySelector('tbody');
    
    // 直接处理每个项目的数据
    items.forEach(item => {
        appendOptimizedItemRows(tbody, item);
    });
}

function appendOptimizedItemRows(tbody, item) {
    const methods = item.methods || [];
    const hasChildItem = item.child_item && item.child_item.trim() !== '';
    
    if (methods.length === 0) {
        tbody.appendChild(createBasicItemRow(item, hasChildItem));
        return;
    }
    
    // 同一项目的所有方法连续显示
    methods.forEach((method, methodIndex) => {
        const sampleTypes = parseSampleTypes(method.sample_types);
        
        if (sampleTypes.length === 0) {
            tbody.appendChild(createMethodRow(item, method, null, methodIndex, hasChildItem));
        } else {
            sampleTypes.forEach((sampleType, sampleIndex) => {
                tbody.appendChild(createMethodRow(item, method, sampleType, methodIndex, hasChildItem, sampleIndex));
            });
        }
    });
}

function fetchItemMethodsForDisplay(item) {
    apiRequest('/api/get_item_details/' + item.id)
        .then(data => {
            if (data.success && data.details) {
                appendItemRowToTable(item, data.details);
            } else {
                appendBasicItemRow(item);
            }
        })
        .catch(() => appendBasicItemRow(item));
}

function appendItemRowToTable(item, details) {
    const tbody = document.querySelector('#results-tbody');
    if (!tbody) return;
    
    const methods = details.methods || [];
    const hasChildItem = item.child_item && item.child_item.trim() !== '';
    
    if (methods.length === 0) {
        tbody.appendChild(createBasicItemRow(item, hasChildItem));
        return;
    }
    
    // 同一项目的所有方法连续显示
    methods.forEach((method, methodIndex) => {
        const sampleTypes = parseSampleTypes(method.sample_types);
        
        if (sampleTypes.length === 0) {
            tbody.appendChild(createMethodRow(item, method, null, methodIndex, hasChildItem));
        } else {
            sampleTypes.forEach((sampleType, sampleIndex) => {
                tbody.appendChild(createMethodRow(item, method, sampleType, methodIndex, hasChildItem, sampleIndex));
            });
        }
    });
}

function createBasicItemRow(item, hasChildItem) {
    const row = document.createElement('tr');
    row.className = 'border-bottom';
    
    const projectNameHtml = hasChildItem 
        ? '<a href="javascript:void(0)" class="text-primary text-decoration-none fw-bold" onclick="showChildItems(\'' + item.id + '\', \'' + escapeHtml(item.child_item) + '\')" title="点击查看子项目">' + item.name + '</a>'
        : '<span class="fw-bold">' + item.name + '</span>';
    
    const projectStatusBadge = item.status == 0 ? 
        '<span class="badge bg-success-subtle text-success border border-success" style="font-size:0.7rem;">正常</span>' : 
        '<span class="badge bg-warning-subtle text-warning border border-warning" style="font-size:0.7rem;">锁定</span>';
    
    const categoryText = (item.service_category_name || '') + 
                        (item.second_category_name ? '-' + item.second_category_name : '');
    
    row.innerHTML = 
        '<td class="px-2 py-2 small border-end">' + (categoryText || '-') + '</td>' +
        '<td class="px-2 py-2 border-end"><code class="small text-muted">' + item.number + '</code></td>' +
        '<td class="px-2 py-2 border-end">' + projectNameHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + projectStatusBadge + '</td>' +
        '<td class="px-2 py-2 text-muted small border-end">暂无检测方法</td>' +
        '<td class="px-2 py-2 text-muted border-end">-</td>' +
        '<td class="px-2 py-2 text-muted border-end">-</td>' +
        '<td class="px-2 py-2 text-muted border-end">-</td>' +
        '<td class="px-2 py-2 text-muted">-</td>';
    
    return row;
}

function createMethodRow(item, method, sampleType, methodIndex, hasChildItem, sampleIndex = 0) {
    const row = document.createElement('tr');
    row.className = 'border-bottom';
    
    // 每一行都显示完整的项目信息
    
    const projectNameHtml = hasChildItem 
        ? '<a href="javascript:void(0)" class="text-primary text-decoration-none fw-bold" onclick="showChildItems(\'' + item.id + '\', \'' + escapeHtml(item.child_item) + '\')" title="点击查看子项目">' + item.name + '</a>'
        : '<span class="fw-bold">' + item.name + '</span>';
        
    const projectStatusHtml = item.status == 0 ? 
        '<span class="badge bg-success-subtle text-success border border-success" style="font-size:0.7rem;">正常</span>' : 
        '<span class="badge bg-warning-subtle text-warning border border-warning" style="font-size:0.7rem;">锁定</span>';
        
    const categoryHtml = (item.service_category_name || '') +
                      (item.second_category_name ? '-' + item.second_category_name : '');
    const numberHtml = '<code class="small text-muted">' + item.number + '</code>';
    
    // 方法状态
    const methodStatusBadge = method.status == 0 ? 
        '<span class="badge bg-success-subtle text-success border border-success" style="font-size:0.7rem;">正常</span>' : 
        '<span class="badge bg-warning-subtle text-warning border border-warning" style="font-size:0.7rem;">锁定</span>';
    
    // 资质认证
    let certificationHtml = '';
    const hasCMA = method.cma == 1;
    const hasCNAS = method.cnas == 1;
    if (hasCMA && hasCNAS) {
        certificationHtml = '<span class="badge bg-primary-subtle text-primary border border-primary" style="font-size:0.7rem;">CMA/CNAS</span>';
    } else if (hasCMA) {
        certificationHtml = '<span class="badge bg-success-subtle text-success border border-success" style="font-size:0.7rem;">CMA</span>';
    } else if (hasCNAS) {
        certificationHtml = '<span class="badge bg-info-subtle text-info border border-info" style="font-size:0.7rem;">CNAS</span>';
    } else {
        certificationHtml = '<span class="text-muted small">无</span>';
    }
    
    // 紧凑的价格显示
    const analysisPrice = method.price || '未设置';
    const lowestPrice = method.lowest_bid || analysisPrice;
    const priceHtml = '<span class="small">' + analysisPrice + '/' + lowestPrice + '</span>';
    
    // 紧凑的采样价格显示
    const samplePriceHtml = sampleType 
        ? '<span class="small">' + sampleType.type + '：' + (sampleType.price || '未设置') + '</span>'
        : '<span class="text-muted small">无采样</span>';
    
    row.innerHTML = 
        '<td class="px-2 py-2 small border-end">' + categoryHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + numberHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + projectNameHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + projectStatusHtml + '</td>' +
        '<td class="px-2 py-2 border-end"><div class="small fw-bold">' + method.name + '</div><div class="text-muted" style="font-size:0.75rem;">' + (method.method_no || '-') + '</div></td>' +
        '<td class="px-2 py-2 border-end">' + priceHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + samplePriceHtml + '</td>' +
        '<td class="px-2 py-2 border-end">' + methodStatusBadge + '</td>' +
        '<td class="px-2 py-2">' + certificationHtml + '</td>';
    
    return row;
}

// 显示子项目详情弹窗
function showChildItems(itemId, childItem) {
    const modal = new bootstrap.Modal(document.getElementById('childItemModal'));
    const content = document.getElementById('child-item-content');
    content.innerHTML = '<p class="mb-0">' + childItem + '</p>';
    modal.show();
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function updatePagination(totalItems) {
    const paginationContainer = document.getElementById('pagination-container');
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }
    
    let paginationHtml = '';
    
    if (currentPage > 1) {
        paginationHtml += '<li class="page-item"><a class="page-link" href="javascript:searchItems(' + (currentPage - 1) + ')">上一页</a></li>';
    }
    
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        paginationHtml += '<li class="page-item ' + activeClass + '"><a class="page-link" href="javascript:searchItems(' + i + ')">' + i + '</a></li>';
    }
    
    if (currentPage < totalPages) {
        paginationHtml += '<li class="page-item"><a class="page-link" href="javascript:searchItems(' + (currentPage + 1) + ')">下一页</a></li>';
    }
    
    pagination.innerHTML = paginationHtml;
    paginationContainer.style.display = 'flex';
}

function appendBasicItemRow(item) {
    const tbody = document.querySelector('#results-tbody');
    if (!tbody) return;
    
    const hasChildItem = item.child_item && item.child_item.trim() !== '';
    tbody.appendChild(createBasicItemRow(item, hasChildItem));
}

function parseSampleTypes(sampleTypesData) {
    if (!sampleTypesData) return [];
    
    try {
        if (typeof sampleTypesData === 'string') {
            if (sampleTypesData.includes(':')) {
                return sampleTypesData.split(',').map(item => {
                    const parts = item.split(':');
                    return { type: parts[0].trim(), price: parts[1].trim() };
                });
            } else {
                return JSON.parse(sampleTypesData);
            }
        } else if (Array.isArray(sampleTypesData)) {
            return sampleTypesData;
        }
    } catch (e) {
        console.warn('解析样品类型数据失败:', e);
    }
    
    return [];
}

function clearSearch() {
    document.getElementById('keyword').value = '';
    document.getElementById('category').value = '';
    document.getElementById('limit').value = '20';
    
    currentPage = 1;
    currentKeyword = '';
    currentCategory = '';
    currentLimit = 20;
    
    searchItems(1);
}

// 方法分析功能
function analyzeMethod() {
    const methodName = document.getElementById('method-analysis-input').value.trim();
    
    if (!methodName) {
        alert('请输入要分析的方法名称');
        return;
    }
    
    const resultsContainer = document.getElementById('method-analysis-results');
    const summaryContainer = document.getElementById('method-analysis-summary');
    const detailsContainer = document.getElementById('method-analysis-details');
    
    // 显示加载状态
    summaryContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在分析方法...</div>';
    detailsContainer.innerHTML = '';
    resultsContainer.style.display = 'block';
    
    const params = new URLSearchParams();
    params.append('method_name', methodName);
    
    apiRequest(`/api/analyze_method?${params.toString()}`)
        .then(data => {
            if (data.success) {
                displayMethodAnalysisResults(data.data);
            } else {
                summaryContainer.innerHTML = `<div class="alert alert-warning small mb-0">${data.message}</div>`;
                detailsContainer.innerHTML = '';
            }
        })
        .catch(error => {
            summaryContainer.innerHTML = `<div class="alert alert-danger small mb-0">分析失败: ${error.message}</div>`;
            detailsContainer.innerHTML = '';
        });
}

function displayMethodAnalysisResults(data) {
    const summaryContainer = document.getElementById('method-analysis-summary');
    const detailsContainer = document.getElementById('method-analysis-details');
    
    // 显示统计摘要
    summaryContainer.innerHTML = `
        <div class="alert alert-info small mb-0">
            <strong>分析结果：</strong>
            找到 <strong>${data.total_methods}</strong> 个匹配的方法，
            涉及 <strong>${data.total_projects}</strong> 个项目，
            分布在 <strong>${data.categories_count}</strong> 个分类中
        </div>
    `;
    
    // 按分类分组显示详细结果
    let detailsHtml = '';
    
    for (const [categoryName, categoryData] of Object.entries(data.category_groups)) {
        detailsHtml += `
            <div class="mb-4">
                <h6 class="text-primary border-bottom pb-1 mb-3">
                    <i class="fas fa-folder me-2"></i>${categoryName}
                    <span class="badge bg-primary ms-2">${categoryData.methods.length} 个方法</span>
                </h6>
                
                <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0" style="font-size: 0.95rem;">
                        <thead class="table-light">
                            <tr>
                                <th width="8%">项目编号</th>
                                <th width="8%">方法ID</th>
                                <th width="15%">项目名称</th>
                                <th width="4%">数量</th>
                                <th width="12%">分析价格</th>
                                <th width="12%">最低价格</th>
                                <th width="20%">采样价格</th>
                                <th width="8%">二级分类</th>
                                <th width="6%">项目状态</th>
                                <th width="6%">方法状态</th>
                                <th width="7%">资质</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        categoryData.methods.forEach(method => {
            const secondCategory = method.second_category_name || '-';
            const analysisPrice = method.analysis_price_display || '未设置';
            const lowestPrice = method.lowest_price_display || '未设置';
            
            // 处理采样价格
            let samplePricesHtml = '';
            if (method.sample_types && method.sample_types.length > 0) {
                const samplePrices = method.sample_types.map(st => 
                    `${st.type}: ${st.price || '未设置'}`
                ).join('<br>');
                samplePricesHtml = samplePrices;
            } else {
                samplePricesHtml = '<span class="text-muted">无采样价格</span>';
            }
            
            // 项目状态
            const itemStatusBadge = method.item_status == 0 ? 
                '<span class="badge bg-success-subtle text-success">正常</span>' : 
                '<span class="badge bg-warning-subtle text-warning">锁定</span>';
            
            // 方法状态
            const methodStatusBadge = method.method_status == 0 ? 
                '<span class="badge bg-success-subtle text-success">正常</span>' : 
                '<span class="badge bg-warning-subtle text-warning">锁定</span>';
            
            // 资质认证
            let certificationHtml = '';
            const hasCMA = method.cma == 1;
            const hasCNAS = method.cnas == 1;
            if (hasCMA && hasCNAS) {
                certificationHtml = '<span class="badge bg-primary">CMA/CNAS</span>';
            } else if (hasCMA) {
                certificationHtml = '<span class="badge bg-success">CMA</span>';
            } else if (hasCNAS) {
                certificationHtml = '<span class="badge bg-info">CNAS</span>';
            } else {
                certificationHtml = '<span class="text-muted">无</span>';
            }
            
            detailsHtml += `
                <tr>
                    <td><code class="small">${method.service_item_number}</code></td>
                    <td><code class="small">${method.method_no || '-'}</code></td>
                    <td><strong>${method.service_item_name}</strong></td>
                    <td class="text-center">${method.project_count}</td>
                    <td>${analysisPrice}</td>
                    <td>${lowestPrice}</td>
                    <td class="small">${samplePricesHtml}</td>
                    <td>${secondCategory}</td>
                    <td>${itemStatusBadge}</td>
                    <td>${methodStatusBadge}</td>
                    <td>${certificationHtml}</td>
                </tr>
            `;
        });
        
        detailsHtml += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }
    
    detailsContainer.innerHTML = detailsHtml;
}

function clearMethodAnalysis() {
    document.getElementById('method-analysis-input').value = '';
    document.getElementById('method-analysis-results').style.display = 'none';
    document.getElementById('method-analysis-summary').innerHTML = '';
    document.getElementById('method-analysis-details').innerHTML = '';
}

// 页面加载完成后自动显示数据
document.addEventListener('DOMContentLoaded', function() {
    ['keyword', 'category'].forEach(id => {
        document.getElementById(id).addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchItems(1);
            }
        });
    });
    
    // 为方法分析输入框添加回车键事件
    document.getElementById('method-analysis-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            analyzeMethod();
        }
    });
    
    document.getElementById('limit').addEventListener('change', function() {
        searchItems(1);
    });
    
    searchItems(1);
});
</script>
{% endblock %}