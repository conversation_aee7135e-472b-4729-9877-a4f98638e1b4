-- 评价方法自定义检测项目支持迁移脚本
-- 允许在评价推荐配置中存储自定义的检测项目名称

-- 1. 为评价推荐配置表添加自定义检测项目名称字段
ALTER TABLE evaluation_recommendations ADD COLUMN custom_detection_item_name TEXT;

-- 2. 修改外键约束，允许detection_item_id为空（当使用自定义检测项目时）
-- 注意：SQLite不支持直接修改外键约束，所以我们需要重建表

-- 创建新的评价推荐配置表
CREATE TABLE evaluation_recommendations_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    evaluation_method_id INTEGER NOT NULL,
    detection_item_id INTEGER,                       -- 允许为空，用于自定义检测项目
    custom_detection_item_name TEXT,                 -- 自定义检测项目名称
    recommended_standard_method_id INTEGER NOT NULL,
    priority INTEGER DEFAULT 1,                      -- 推荐优先级
    reason TEXT,                                      -- 推荐理由
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (evaluation_method_id) REFERENCES evaluation_methods(id) ON DELETE CASCADE,
    FOREIGN KEY (detection_item_id) REFERENCES detection_items(id) ON DELETE CASCADE,
    FOREIGN KEY (recommended_standard_method_id) REFERENCES standard_methods(id) ON DELETE CASCADE,
    -- 确保每个评价方法中，检测项目（无论是系统的还是自定义的）和推荐方法的组合是唯一的
    UNIQUE(evaluation_method_id, detection_item_id, recommended_standard_method_id),
    UNIQUE(evaluation_method_id, custom_detection_item_name, recommended_standard_method_id),
    -- 确保detection_item_id和custom_detection_item_name不能同时为空
    CHECK ((detection_item_id IS NOT NULL) OR (custom_detection_item_name IS NOT NULL))
);

-- 复制现有数据
INSERT INTO evaluation_recommendations_new 
(id, evaluation_method_id, detection_item_id, recommended_standard_method_id, priority, reason, created_at)
SELECT id, evaluation_method_id, detection_item_id, recommended_standard_method_id, priority, reason, created_at
FROM evaluation_recommendations;

-- 删除旧表
DROP TABLE evaluation_recommendations;

-- 重命名新表
ALTER TABLE evaluation_recommendations_new RENAME TO evaluation_recommendations;

-- 3. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_custom_item 
ON evaluation_recommendations(evaluation_method_id, custom_detection_item_name);

CREATE INDEX IF NOT EXISTS idx_evaluation_recommendations_detection_item 
ON evaluation_recommendations(evaluation_method_id, detection_item_id);

-- 4. 创建视图以便于查询
CREATE VIEW evaluation_recommendations_with_items AS
SELECT 
    er.*,
    CASE 
        WHEN er.detection_item_id IS NOT NULL THEN di.name
        ELSE er.custom_detection_item_name
    END as effective_item_name,
    CASE 
        WHEN er.detection_item_id IS NOT NULL THEN di.display_name
        ELSE er.custom_detection_item_name
    END as effective_display_name,
    CASE 
        WHEN er.detection_item_id IS NOT NULL THEN 'system'
        ELSE 'custom'
    END as item_type,
    di.name as system_item_name,
    di.display_name as system_display_name,
    sm.full_standard_number,
    sm.standard_name
FROM evaluation_recommendations er
LEFT JOIN detection_items di ON er.detection_item_id = di.id
JOIN standard_methods sm ON er.recommended_standard_method_id = sm.id;
