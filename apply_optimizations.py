#!/usr/bin/env python3
"""
应用数据库优化脚本
为评价管理系统应用性能优化
"""

import os
import sys
import sqlite3
from database_cache_manager import apply_database_optimizations

def check_database_exists(db_path: str) -> bool:
    """检查数据库文件是否存在"""
    return os.path.exists(db_path)

def backup_database(db_path: str) -> str:
    """备份数据库"""
    import shutil
    from datetime import datetime
    
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path

def verify_optimizations(db_path: str) -> bool:
    """验证优化是否成功应用"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查索引是否创建
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)
            
            indexes = cursor.fetchall()
            expected_indexes = [
                'idx_evaluation_methods_active',
                'idx_evaluation_methods_scope',
                'idx_evaluation_recommendations_method_id',
                'idx_detection_items_name',
                'idx_standard_methods_status'
            ]
            
            created_indexes = [idx[0] for idx in indexes]
            missing_indexes = []
            
            for expected in expected_indexes:
                if expected not in created_indexes:
                    missing_indexes.append(expected)
            
            if missing_indexes:
                print(f"警告: 以下索引未创建成功: {missing_indexes}")
                return False
            else:
                print(f"成功创建 {len(created_indexes)} 个索引")
                return True
                
    except Exception as e:
        print(f"验证优化失败: {e}")
        return False

def show_database_info(db_path: str):
    """显示数据库信息"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = cursor.fetchall()
            
            print(f"\n数据库信息 ({db_path}):")
            print(f"表数量: {len(tables)}")
            
            # 获取评价方法相关表的记录数
            evaluation_tables = [
                'evaluation_methods',
                'evaluation_recommendations',
                'detection_items',
                'standard_methods'
            ]
            
            for table in evaluation_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"  {table}: {count} 条记录")
                except sqlite3.Error:
                    print(f"  {table}: 表不存在")
            
            # 获取索引信息
            cursor.execute("""
                SELECT COUNT(*) FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)
            index_count = cursor.fetchone()[0]
            print(f"优化索引数量: {index_count}")
            
    except Exception as e:
        print(f"获取数据库信息失败: {e}")

def main():
    """主函数"""
    db_path = 'kangda_prices.db'
    
    print("评价管理系统数据库优化工具")
    print("="*50)
    
    # 检查数据库是否存在
    if not check_database_exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        print("请确保数据库文件存在后再运行此脚本")
        sys.exit(1)
    
    # 显示当前数据库信息
    show_database_info(db_path)
    
    # 询问是否继续
    response = input(f"\n是否要对数据库 {db_path} 应用性能优化? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        sys.exit(0)
    
    # 备份数据库
    print("\n正在备份数据库...")
    backup_path = backup_database(db_path)
    
    try:
        # 应用优化
        print("\n正在应用数据库优化...")
        apply_database_optimizations(db_path)
        
        # 验证优化
        print("\n正在验证优化结果...")
        if verify_optimizations(db_path):
            print("\n✅ 数据库优化成功完成!")
            print("\n优化内容:")
            print("  - 添加了关键字段的索引")
            print("  - 优化了复杂查询的性能")
            print("  - 启用了查询缓存机制")
            print("  - 更新了数据库统计信息")
            
            # 显示优化后的数据库信息
            show_database_info(db_path)
            
            print(f"\n数据库备份保存在: {backup_path}")
            print("如果遇到问题，可以使用备份文件恢复")
            
        else:
            print("\n❌ 数据库优化可能未完全成功")
            print("请检查错误信息并重试")
            
    except Exception as e:
        print(f"\n❌ 应用优化时发生错误: {e}")
        print(f"数据库备份保存在: {backup_path}")
        print("建议使用备份文件恢复数据库")
        sys.exit(1)

if __name__ == "__main__":
    main()
